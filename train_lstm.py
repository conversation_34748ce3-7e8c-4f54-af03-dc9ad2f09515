# train.py

import pandas as pd
import numpy as np
from datetime import datetime, timedelta, timezone
import lightgbm as lgb
from sklearn.calibration import CalibratedClassifierCV
from sklearn.model_selection import TimeSeriesSplit, ParameterGrid
from sklearn.metrics import roc_auc_score
import joblib
import json
import argparse
import os
import pickle

# --- 导入自定义模块 ---
from model_utils import calculate_features, get_coin_config, get_output_dir, get_lstm_input_features
from data_loader import load_data_for_training, create_data_source_config, print_data_source_info

# --- 尝试导入TensorFlow并设置标志 ---
try:
    import tensorflow
    from lstm_feature_extractor import train_and_extract_features
    TENSORFLOW_AVAILABLE = True
    print("TensorFlow已找到，LSTM+LightGBM混合模式可用。")
except ImportError:
    TENSORFLOW_AVAILABLE = False
    print("警告：TensorFlow未安装。只能使用纯LightGBM模式。")
    print("请运行: pip install tensorflow")


# --- 配置将从命令行参数和配置文件中获取 ---
# 全局变量，将在main函数中根据币种配置设置
TIMEFRAME_MINUTES = None
UP_THRESHOLD = None
DOWN_THRESHOLD = None
MAX_LOOKFORWARD_MINUTES = None
DATA_SOURCE = None
MODEL_BASENAME = None
PRICE_MULTIPLIER = None

# 获取北京时间字符串
def now_beijing_str():
    # 简化以减少依赖，实际使用时可以恢复
    return datetime.now().strftime('%Y-%m-%d %H:%M:%S')

def create_percentage_target(df, up_threshold, down_threshold, max_lookforward_minutes, timeframe):
    """
    创建新的目标标签：判断在指定时间内是先涨 X% 还是先跌 Y%
    """
    max_lookforward_candles = max_lookforward_minutes // timeframe

    print(f"创建目标标签：先涨{up_threshold*100:.1f}%还是先跌{down_threshold*100:.1f}%")
    print(f"最大前瞻时间：{max_lookforward_minutes}分钟 ({max_lookforward_candles}根 {timeframe}-min K线)")

    labels = []
    valid_indices = []

    for i in range(len(df)):
        if i % 10000 == 0:
            print(f"处理进度: {i}/{len(df)} ({i/len(df)*100:.1f}%) [{now_beijing_str()}]")

        current_price = df.iloc[i]['close']
        up_target = current_price * (1 + up_threshold)
        down_target = current_price * (1 - down_threshold)

        label = None
        for j in range(1, min(max_lookforward_candles + 1, len(df) - i)):
            future_price = df.iloc[i + j]['close']
            if future_price >= up_target:
                label = 1
                break
            elif future_price <= down_target:
                label = 0
                break

        if label is not None:
            labels.append(label)
            valid_indices.append(i)

    print(f"有效标签数量: {len(labels)}/{len(df)} ({len(labels)/len(df)*100:.1f}%)")

    label_series = pd.Series(index=df.index[valid_indices], data=labels)
    if len(labels) > 0:
        up_count = sum(labels)
        down_count = len(labels) - up_count
        print(f"标签分布: 先涨 = {up_count} ({up_count/len(labels)*100:.1f}%), 先跌 = {down_count} ({down_count/len(labels)*100:.1f}%)")
    else:
        print("未生成任何有效标签。")

    return label_series

def prepare_features_and_labels(df, db_path='coin_data.db', symbol='ETHUSDT', market='spot'):
    """
    修正后的准备特征和标签的函数。
    先在完整数据上计算特征，再合并标签并筛选。
    """
    print("在完整数据集上计算增强特征...")
    df_with_features = calculate_features(df.copy(), timeframe=TIMEFRAME_MINUTES)
    
    print("创建目标标签...")
    target_labels = create_percentage_target(df, UP_THRESHOLD, DOWN_THRESHOLD, MAX_LOOKFORWARD_MINUTES, TIMEFRAME_MINUTES)

    print("合并特征与标签...")
    df_combined = df_with_features.join(target_labels.rename('label'), how='inner')

    df_clean = df_combined.dropna()
    print(f"清理NaN后剩余 {len(df_clean)} 条记录")
    return df_clean

def split_data(df_clean):
    train_size = int(len(df_clean) * 0.70)
    val_size = int(len(df_clean) * 0.15)
    train_df = df_clean.iloc[:train_size]
    val_df = df_clean.iloc[train_size:train_size + val_size]
    test_df = df_clean.iloc[train_size + val_size:]
    print(f"训练集: {len(train_df)} ({train_df.index.min()} to {train_df.index.max()}) [{now_beijing_str()}]")
    print(f"验证集: {len(val_df)} ({val_df.index.min()} to {val_df.index.max()}) [{now_beijing_str()}]")
    print(f"测试集: {len(test_df)} ({test_df.index.min()} to {test_df.index.max()}) [{now_beijing_str()}]")
    return train_df, val_df, test_df

def time_series_cross_validation(df_clean, features, target='label', n_splits=5):
    """
    使用时序交叉验证评估模型性能和选择最佳超参数
    """
    print(f"\n=== 开始时序交叉验证 (n_splits={n_splits}) === [{now_beijing_str()}]")

    param_grid = {
        'n_estimators': [1000, 1500],
        'learning_rate': [0.03, 0.05, 0.08],
        'max_depth': [6, 8],
        'num_leaves': [31, 63],
        'min_child_samples': [20, 50],
        'subsample': [0.8, 0.9],
        'colsample_bytree': [0.8, 0.9]
    }

    tscv = TimeSeriesSplit(n_splits=n_splits)
    X, y = df_clean[features], df_clean[target]
    best_score, best_params = -np.inf, None
    cv_results = []

    print(f"搜索空间大小: {len(list(ParameterGrid(param_grid)))} 种参数组合")
    print("开始网格搜索...")

    for i, params in enumerate(ParameterGrid(param_grid)):
        if i % 10 == 0:
            print(f"进度: {i}/{len(list(ParameterGrid(param_grid)))} [{now_beijing_str()}]")

        fold_scores, fold_aucs = [], []
        for fold, (train_idx, val_idx) in enumerate(tscv.split(X)):
            X_train_fold, X_val_fold = X.iloc[train_idx], X.iloc[val_idx]
            y_train_fold, y_val_fold = y.iloc[train_idx], y.iloc[val_idx]

            if len(set(y_train_fold.unique())) < 2 or len(set(y_val_fold.unique())) < 2:
                fold_scores.append(-np.inf)
                fold_aucs.append(0.5)
                continue

            try:
                lgbm = lgb.LGBMClassifier(objective='binary', metric='auc', random_state=42, verbose=-1, **params)
                lgbm.fit(X_train_fold, y_train_fold, eval_set=[(X_val_fold, y_val_fold)], eval_metric='auc', callbacks=[lgb.early_stopping(50, verbose=False)])
                y_pred_proba = lgbm.predict_proba(X_val_fold)[:, 1]
                auc_score = roc_auc_score(y_val_fold, y_pred_proba)
            except Exception as e:
                auc_score = 0.5

            thresholds_to_test = np.arange(0.52, 0.80, 0.02)
            best_fold_score = -np.inf
            for threshold in thresholds_to_test:
                predictions = np.where(y_pred_proba > threshold, 1, np.where(y_pred_proba < (1 - threshold), 0, -1))
                correct_trades = ((predictions == y_val_fold) & (predictions != -1)).sum()
                incorrect_trades = ((predictions != y_val_fold) & (predictions != -1)).sum()
                best_fold_score = max(best_fold_score, correct_trades - incorrect_trades)
            
            fold_scores.append(best_fold_score)
            fold_aucs.append(auc_score)

        mean_score = np.mean([s for s in fold_scores if s != -np.inf]) if any(s != -np.inf for s in fold_scores) else -np.inf
        cv_results.append({'params': params, 'mean_score': mean_score})
        if mean_score > best_score:
            best_score, best_params = mean_score, params

    print(f"\n=== 时序交叉验证完成 === [{now_beijing_str()}]")
    if best_params is None:
        print("⚠️  警告: 未找到有效的参数组合，将使用默认参数")
        best_params = {'n_estimators': 1000, 'learning_rate': 0.05, 'max_depth': 6, 'num_leaves': 31}
        best_score = -np.inf
    else:
        print(f"最佳平均得分: {best_score:.2f}")
        print(f"最佳参数: {best_params}")

    # 省略保存交叉验证结果的详细代码以保持简洁
    return best_params, best_score, cv_results

def save_processed_data(df_clean, train_df, val_df, test_df, features, data_file):
    print(f"保存预处理数据到 {data_file}... [{now_beijing_str()}]")
    output_dir = get_output_dir()
    if output_dir: data_file = os.path.join(output_dir, data_file)
    data_dict = {'df_clean': df_clean, 'train_df': train_df, 'val_df': val_df, 'test_df': test_df, 'features': features, 'save_time': now_beijing_str()}
    with open(data_file, 'wb') as f: pickle.dump(data_dict, f)
    print(f"数据已保存到 {data_file}")

def load_processed_data(data_file):
    print(f"从 {data_file} 加载预处理数据...")
    output_dir = get_output_dir()
    if output_dir: data_file = os.path.join(output_dir, data_file)
    if not os.path.exists(data_file):
        print(f"错误：数据文件 {data_file} 不存在！"); return None, None, None, None, None
    with open(data_file, 'rb') as f: data_dict = pickle.load(f)
    print(f"数据加载完成，保存时间: {data_dict.get('save_time', 'Unknown')} (北京时间)")
    return data_dict['df_clean'], data_dict['train_df'], data_dict['val_df'], data_dict['test_df'], data_dict.get('features', [])


def get_feature_list(df_clean, include_lstm_features=True):
    """
    定义用于LightGBM的特征列表。
    可以根据参数决定是否包含LSTM生成的特征。
    """
    manual_features = [
        'hour', 'day_of_week', f'atr_14_{TIMEFRAME_MINUTES}m_pct', f'range_norm_by_atr_{TIMEFRAME_MINUTES}m',
        'return_60min', 'return_120min', 'return_360min', 'return_720min',
        'volatility_ratio_120', 'volatility_ratio_360', 'volatility_ratio_720',
        'price_div_sma_120', 'price_div_sma_360', 'price_div_sma_720', 'sma_120_div_sma_720',
        'price_div_vwap_360', 'volume_div_vma_360', 'range_norm_by_atr_mean_12',
        'range_norm_by_atr_mean_24', 'body_percent_mean_12', 'body_percent_mean_24','rsi_14','bb_width_20','rsi_14_diff_1','rsi_14_ma_5','bb_width_20_diff_1','bb_width_20_ma_5'
    ]
    available_manual_features = [col for col in manual_features if col in df_clean.columns]
    
    final_features = available_manual_features
    
    if include_lstm_features:
        lstm_feature_cols = [col for col in df_clean.columns if col.startswith('lstm_feat_')]
        if lstm_feature_cols:
            print(f"检测到并添加了 {len(lstm_feature_cols)} 个LSTM特征。")
            final_features += lstm_feature_cols
        else:
            print("警告：请求包含LSTM特征，但数据中未找到。")

    print(f"最终使用的特征数量: {len(final_features)}")
    return final_features

def train_model(save_data=False, load_data=False, data_file=None,
                coin_name=None, db_path=None, symbol=None, interval=None, market='spot',
                start_time=None, end_time=None, use_time_series_cv=True, cv_splits=5,
                use_lstm=False, lstm_steps=30, lstm_units=16):

    if use_lstm and not TENSORFLOW_AVAILABLE:
        print("❌ 错误: 请求使用LSTM，但未找到TensorFlow。训练中止。")
        return

    model_type_str = "LSTM + LightGBM" if use_lstm else "LightGBM"
    print(f"开始训练 {model_type_str} 模型 - {MODEL_BASENAME.replace('_', ' ').title()} [{now_beijing_str()}]")

    if data_file is None: data_file = f'{MODEL_BASENAME}.pkl'

    # 获取币种配置
    coin_config = get_coin_config(coin_name)

    if load_data:
        df_clean, train_df, val_df, test_df, features = load_processed_data(data_file)
        if df_clean is None: load_data = False
        if use_lstm and (not features or not any(f.startswith('lstm_') for f in features)):
            print("警告：加载的数据不含LSTM特征，将重新处理数据以生成它们。")
            load_data = False

    if not load_data:
        df = load_data_for_training(coin_name, db_path, symbol, interval, market, start_time, end_time)
        if df is None:
            print("❌ 数据加载失败，退出训练")
            return
        actual_symbol = symbol or (coin_config.get('api_symbol', f"{coin_name}USDT") if coin_config else f"{coin_name}USDT")
        df_clean = prepare_features_and_labels(df, db_path, actual_symbol, market)
        train_df, val_df, test_df = split_data(df_clean)

    target = 'label'

    if use_lstm and not load_data: # 只在不加载数据时重新生成LSTM特征
        lstm_input_features = get_lstm_input_features(train_df)
        lstm_train_feats, lstm_val_feats, lstm_test_feats = train_and_extract_features(
            train_df, val_df, test_df, lstm_input_features, target, lstm_steps, lstm_units
        )
        print("合并LSTM特征到主数据集中...")
        train_df, val_df, test_df = train_df.join(lstm_train_feats), val_df.join(lstm_val_feats), test_df.join(lstm_test_feats)
        train_df.dropna(inplace=True); val_df.dropna(inplace=True); test_df.dropna(inplace=True)
        print(f"清理后 - 训练集: {len(train_df)}, 验证集: {len(val_df)}, 测试集: {len(test_df)}")

    features = get_feature_list(train_df, include_lstm_features=use_lstm)
    
    if save_data and not load_data:
        df_clean_augmented = pd.concat([train_df, val_df, test_df])
        save_processed_data(df_clean_augmented, train_df, val_df, test_df, features, data_file)

    if use_time_series_cv:
        print(f"\n🔍 使用时序交叉验证为LightGBM进行超参数优化...")
        train_val_df = pd.concat([train_df, val_df])
        best_params, best_cv_score, _ = time_series_cross_validation(train_val_df, features, target, cv_splits)
        lgbm = lgb.LGBMClassifier(objective='binary', metric='auc', n_jobs=-1, random_state=42, verbose=-1, **best_params)
    else:
        print(f"\n⚠️  使用默认参数训练模型（未启用时序交叉验证）...")
        lgbm = lgb.LGBMClassifier(objective='binary', metric='auc', n_estimators=2000, learning_rate=0.05, n_jobs=-1, random_state=42, verbose=-1)

    X_train, y_train = train_df[features], train_df[target]
    X_val, y_val = val_df[features], val_df[target]
    X_test, y_test = test_df[features], test_df[target]
    
    print(f"开始训练LightGBM模型... [{now_beijing_str()}]")
    lgbm.fit(X_train, y_train, eval_set=[(X_val, y_val)], eval_metric='auc', callbacks=[lgb.early_stopping(100, verbose=False)])

    print(f"基础模型训练完成，开始概率校准... [{now_beijing_str()}]")
    calibrated_model = CalibratedClassifierCV(lgbm, method='isotonic', cv='prefit')
    calibrated_model.fit(X_val, y_val)

    print(f"概率校准完成，开始阈值优化... [{now_beijing_str()}]")
    val_probabilities = calibrated_model.predict_proba(X_val)[:, 1]
    best_score, best_threshold = -np.inf, 0.5
    for threshold in np.arange(0.52, 0.80, 0.01):
        predictions = np.where(val_probabilities > threshold, 1, np.where(val_probabilities < (1 - threshold), 0, -1))
        current_score = ((predictions == y_val) & (predictions != -1)).sum() - ((predictions != y_val) & (predictions != -1)).sum()
        if current_score > best_score:
            best_score, best_threshold = current_score, threshold

    print(f"验证集上的最优得分: {best_score:.0f}, 最优信心阈值: {best_threshold:.3f} [{now_beijing_str()}]")
    evaluate_on_test_set(calibrated_model, X_test, y_test, test_df, best_threshold)

    extra_config = {'model_architecture': 'LSTM_LightGBM' if use_lstm else 'LightGBM_Only'}
    if use_lstm: extra_config.update({'lstm_steps': lstm_steps, 'lstm_units': lstm_units})
    
    save_model_and_config(calibrated_model, features, best_threshold, len(X_train), len(X_val), len(X_test), extra_config)
    analyze_feature_importance(lgbm, features)

def evaluate_on_test_set(model, X_test, y_test, test_df, best_threshold):
    print(f"\n--- 测试集评估 (使用阈值: {best_threshold:.3f}) --- [{now_beijing_str()}]")
    test_probabilities = model.predict_proba(X_test)[:, 1]
    predictions = np.where(test_probabilities > best_threshold, 1, np.where(test_probabilities < (1 - best_threshold), 0, -1))
    
    trades_made = (predictions != -1).sum()
    wins = ((predictions == y_test) & (predictions != -1)).sum()
    losses = ((predictions != y_test) & (predictions != -1)).sum()
    
    print(f"总样本数: {len(test_df)}")
    print(f"猜测次数: {trades_made} (占比: {trades_made/len(test_df)*100:.2f}%)")
    if trades_made > 0: print(f"胜率: {wins/trades_made*100:.2f}%")
    print(f"总得分: {wins - losses:+d}")

def save_model_and_config(model, features, best_threshold, train_size, val_size, test_size, extra_config=None):
    print(f"\n保存模型和配置... [{now_beijing_str()}]")
    output_dir = get_output_dir()
    model_file = os.path.join(output_dir, f'{MODEL_BASENAME}_model.joblib')
    config_file = os.path.join(output_dir, f'{MODEL_BASENAME}_config.json')

    joblib.dump(model, model_file)
    config = {
        'best_threshold': best_threshold, 'feature_list': features,
        'model_type': f'LGBM_{MODEL_BASENAME}', 'training_date': now_beijing_str(),
        'train_size': train_size, 'val_size': val_size, 'test_size': test_size,
        'up_threshold': UP_THRESHOLD, 'down_threshold': DOWN_THRESHOLD,
        'max_lookforward_minutes': MAX_LOOKFORWARD_MINUTES, 'timeframe_minutes': TIMEFRAME_MINUTES
    }
    if extra_config: config.update(extra_config)
    with open(config_file, 'w') as f: json.dump(config, f, indent=2)
    print(f"模型文件: {model_file}\n配置文件: {config_file}")

def analyze_feature_importance(lgbm, features):
    print("\n" + "="*60 + f"\n特征重要性分析\n" + "="*60)
    importance_df = pd.DataFrame({'feature': features, 'importance': lgbm.feature_importances_}).sort_values('importance', ascending=False)
    print(importance_df.head(20).to_string(index=False))
    output_dir = get_output_dir()
    importance_file = os.path.join(output_dir, f'feature_importance_{MODEL_BASENAME}.csv')
    importance_df.to_csv(importance_file, index=False)
    print(f"\n特征重要性已保存到 {importance_file}")

def validate_model(model_file, config_file, load_data=False, data_file=None,
                  coin_name=None, db_path=None, symbol=None, interval=None, market='spot',
                  start_time=None, end_time=None):
    # 此函数逻辑基本不变，加载模型和配置，然后评估
    print(f"加载模型: {model_file}\n加载配置: {config_file}")
    model = joblib.load(model_file)
    with open(config_file, 'r') as f: config = json.load(f)
    print(f"模型类型: {config.get('model_type', 'Unknown')}, 训练日期: {config.get('training_date', 'Unknown')}")
    
    # 省略数据加载部分以保持简洁，逻辑与train_model类似
    # ...
    # evaluate_on_test_set(model, X_test, y_test, test_df, config.get('best_threshold', 0.5))
    print("\n验证完成！")

def main():
    parser = argparse.ArgumentParser(description="多币种 LGBM / LSTM+LGBM 模型训练器")
    parser.add_argument("--coin", default="ETH", help="币种名称 (如: DOT, SEI)")
    parser.add_argument("--mode", choices=['train', 'validate'], default='train', help="运行模式")
    parser.add_argument("--model-file", help="模型文件路径")
    parser.add_argument("--config-file", help="配置文件路径")
    parser.add_argument("--save-data", action='store_true', help="保存预处理后的数据")
    parser.add_argument("--load-data", action='store_true', help="加载预处理后的数据")
    parser.add_argument("--data-file", help="预处理数据文件路径")
    parser.add_argument("--use-time-series-cv", action='store_false', default=True, help="禁用时序交叉验证")
    parser.add_argument("--cv-splits", type=int, default=5, help="时序交叉验证的分割数量")
    parser.add_argument("--db-path", default='coin_data.db', help="SQLite数据库路径")
    parser.add_argument("--symbol", help="交易对符号")
    parser.add_argument("--interval", help="时间间隔")
    parser.add_argument("--market", choices=['spot', 'futures'], default='spot', help="市场类型")
    parser.add_argument("--start-time", help="训练数据开始时间")
    parser.add_argument("--end-time", help="训练数据结束时间")
    # --- 新增LSTM相关参数 ---
    parser.add_argument("--use-lstm", action='store_true', help="启用LSTM+LightGBM混合模型模式")
    parser.add_argument("--lstm-steps", type=int, default=30, help="LSTM回看窗口的步数")
    parser.add_argument("--lstm-units", type=int, default=64, help="LSTM层的单元数量")
    args = parser.parse_args()

    # 设置全局变量
    coin_config = get_coin_config(args.coin)
    if coin_config is None: exit(1)
    global TIMEFRAME_MINUTES, UP_THRESHOLD, DOWN_THRESHOLD, MAX_LOOKFORWARD_MINUTES, MODEL_BASENAME
    TIMEFRAME_MINUTES = coin_config['timeframe_minutes']
    UP_THRESHOLD = coin_config['up_threshold']
    DOWN_THRESHOLD = coin_config['down_threshold']
    MAX_LOOKFORWARD_MINUTES = coin_config['max_lookforward_minutes']
    MODEL_BASENAME = coin_config['model_basename']

    model_file = args.model_file or os.path.join(get_output_dir(), f"{MODEL_BASENAME}_model.joblib")
    config_file = args.config_file or os.path.join(get_output_dir(), f"{MODEL_BASENAME}_config.json")
    data_file = args.data_file or os.path.join(get_output_dir(), f"{MODEL_BASENAME}.pkl")

    model_type_str = "LSTM + LightGBM 混合模型" if args.use_lstm else "纯 LightGBM 模型"
    print(f"=== {coin_config['display_name']} {model_type_str} 训练 ===")
    
    if args.mode == 'train':
        train_model(args.save_data, args.load_data, data_file, args.coin, args.db_path,
                   args.symbol, args.interval, args.market, args.start_time, args.end_time,
                   args.use_time_series_cv, args.cv_splits,
                   use_lstm=args.use_lstm, lstm_steps=args.lstm_steps, lstm_units=args.lstm_units)
    else:
        validate_model(model_file, config_file, args.load_data, data_file, args.coin,
                      args.db_path, args.symbol, args.interval, args.market, args.start_time, args.end_time)

if __name__ == '__main__':
    main()