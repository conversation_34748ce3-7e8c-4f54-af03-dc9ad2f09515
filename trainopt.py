import pandas as pd
import numpy as np
from datetime import datetime
import lightgbm as lgb
from sklearn.calibration import CalibratedClassifierCV
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import roc_auc_score
import joblib
import json
import argparse
import os
import pickle
import optuna  # 引入 Optuna

# 假设这些工具函数存在于您的项目中
from model_utils_815 import calculate_features, get_coin_config, get_output_dir, get_feature_list
from data_loader import load_data_for_training, create_data_source_config, print_data_source_info

# --- 配置将从命令行参数和配置文件中获取 ---
# 全局变量，将在main函数中根据币种配置设置
TIMEFRAME_MINUTES = None
UP_THRESHOLD = None
DOWN_THRESHOLD = None
MAX_LOOKFORWARD_MINUTES = None
DATA_SOURCE = None
MODEL_BASENAME = None
PRICE_MULTIPLIER = None

def create_percentage_target(df, up_threshold, down_threshold, max_lookforward_minutes, timeframe):
    """
    创建新的目标标签：判断在指定时间内是先涨 X% 还是先跌 Y%
    """
    max_lookforward_candles = max_lookforward_minutes // timeframe

    print(f"创建目标标签：先涨{up_threshold*100:.1f}%还是先跌{down_threshold*100:.1f}%")
    print(f"最大前瞻时间：{max_lookforward_minutes}分钟 ({max_lookforward_candles}根 {timeframe}-min K线)")

    labels = []
    valid_indices = []

    for i in range(len(df)):
        if i % 10000 == 0:
            print(f"处理进度: {i}/{len(df)} ({i/len(df)*100:.1f}%)")

        current_price = df.iloc[i]['close']
        up_target = current_price * (1 + up_threshold)
        down_target = current_price * (1 - down_threshold)

        label = None
        for j in range(1, min(max_lookforward_candles + 1, len(df) - i)):
            future_price = df.iloc[i + j]['close']
            if future_price >= up_target:
                label = 1
                break
            elif future_price <= down_target:
                label = 0
                break

        if label is not None:
            labels.append(label)
            valid_indices.append(i)

    print(f"有效标签数量: {len(labels)}/{len(df)} ({len(labels)/len(df)*100:.1f}%)")

    label_series = pd.Series(index=df.index[valid_indices], data=labels)
    if len(labels) > 0:
        up_count = sum(labels)
        down_count = len(labels) - up_count
        print(f"标签分布: 先涨 = {up_count} ({up_count/len(labels)*100:.1f}%), 先跌 = {down_count} ({down_count/len(labels)*100:.1f}%)")
    else:
        print("未生成任何有效标签。")

    return label_series

def prepare_features_and_labels(df, db_path='coin_data.db', symbol='ETHUSDT', market='spot'):
    """
    修正后的准备特征和标签的函数。
    先在完整数据上计算特征，再合并标签并筛选。
    """
    print("在完整数据集上计算增强特征...")
    df_with_features = calculate_features(df.copy(), timeframe=TIMEFRAME_MINUTES)
    
    print("创建目标标签...")
    target_labels = create_percentage_target(df, UP_THRESHOLD, DOWN_THRESHOLD, MAX_LOOKFORWARD_MINUTES, TIMEFRAME_MINUTES)
    print("合并特征与标签...")
    df_combined = df_with_features.join(target_labels.rename('label'), how='inner')
    df_clean = df_combined.dropna()
    print(f"清理NaN后剩余 {len(df_clean)} 条记录")
    return df_clean

def split_data(df_clean):
    train_size = int(len(df_clean) * 0.70)
    val_size = int(len(df_clean) * 0.15)
    train_df = df_clean.iloc[:train_size]
    val_df = df_clean.iloc[train_size:train_size + val_size]
    test_df = df_clean.iloc[train_size + val_size:]
    print(f"训练集: {len(train_df)} ({train_df.index.min()} to {train_df.index.max()})")
    print(f"验证集: {len(val_df)} ({val_df.index.min()} to {val_df.index.max()})")
    print(f"测试集: {len(test_df)} ({test_df.index.min()} to {test_df.index.max()})")
    return train_df, val_df, test_df

def time_series_cross_validation(df_clean, features, target='label', n_splits=5, n_trials=100):
    """
    使用时序交叉验证和Optuna评估模型性能并选择最佳超参数。
    """
    print(f"\n=== 开始使用 Optuna 进行时序交叉验证 (n_splits={n_splits}, n_trials={n_trials}) ===")

    tscv = TimeSeriesSplit(n_splits=n_splits)
    X = df_clean[features]
    y = df_clean[target]

    def objective(trial):
        # 为 Optuna 定义超参数搜索空间
        params = {
            'objective': 'binary',
            'metric': 'auc',
            'random_state': 42,
            'verbose': -1,
            'n_jobs': -1, # 使用所有核心
            'n_estimators': trial.suggest_int('n_estimators', 800, 2500),
            'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.1, log=True),
            'max_depth': trial.suggest_int('max_depth', 5, 12),
            'num_leaves': trial.suggest_int('num_leaves', 20, 150),
            'min_child_samples': trial.suggest_int('min_child_samples', 20, 100),
            'subsample': trial.suggest_float('subsample', 0.7, 1.0),
            'colsample_bytree': trial.suggest_float('colsample_bytree', 0.7, 1.0),
        }

        fold_scores = []
        for fold, (train_idx, val_idx) in enumerate(tscv.split(X)):
            X_train_fold, X_val_fold = X.iloc[train_idx], X.iloc[val_idx]
            y_train_fold, y_val_fold = y.iloc[train_idx], y.iloc[val_idx]

            # 确保训练集和验证集都至少包含两个类别
            if len(set(y_train_fold.unique())) < 2 or len(set(y_val_fold.unique())) < 2:
                continue

            try:
                lgbm = lgb.LGBMClassifier(**params)
                lgbm.fit(
                    X_train_fold, y_train_fold,
                    eval_set=[(X_val_fold, y_val_fold)],
                    eval_metric='auc',
                    callbacks=[lgb.early_stopping(stopping_rounds=50, verbose=False)]
                )
                y_pred_proba = lgbm.predict_proba(X_val_fold)[:, 1]

                # 计算自定义得分
                thresholds_to_test = np.arange(0.52, 0.80, 0.02)
                best_fold_score = -np.inf
                for threshold in thresholds_to_test:
                    predictions = np.where(y_pred_proba > threshold, 1, np.where(y_pred_proba < (1 - threshold), 0, -1))
                    correct_trades = (predictions == y_val_fold) & (predictions != -1)
                    incorrect_trades = (predictions != y_val_fold) & (predictions != -1)
                    current_score = correct_trades.sum() - incorrect_trades.sum()
                    if current_score > best_fold_score:
                        best_fold_score = current_score
                fold_scores.append(best_fold_score)
            except Exception:
                continue
        
        # 如果所有折都失败了，返回一个很差的分数
        if not fold_scores:
            return -np.inf

        return np.mean(fold_scores)

    # 设置Optuna日志级别，避免过多输出
    optuna.logging.set_verbosity(optuna.logging.INFO)
    study = optuna.create_study(direction='maximize')
    study.optimize(objective, n_trials=n_trials)

    print(f"\n=== 时序交叉验证完成 ===")
    
    best_params = study.best_params
    best_score = study.best_value

    if best_score == -np.inf:
        print("⚠️ 警告: 未找到有效的参数组合，将使用默认参数")
        best_params = {
            'n_estimators': 1000, 'learning_rate': 0.05, 'max_depth': 6,
            'num_leaves': 31, 'min_child_samples': 20, 'subsample': 0.8, 'colsample_bytree': 0.8
        }
    else:
        print(f"最佳平均得分: {best_score:.2f}")
        print(f"最佳参数: {best_params}")

    # 保存交叉验证结果到CSV
    cv_results_df = study.trials_dataframe()
    output_dir = get_output_dir()
    cv_results_file = f'cv_results_optuna_{MODEL_BASENAME}.csv'
    if output_dir:
        cv_results_file = os.path.join(output_dir, cv_results_file)
    
    cv_results_df.to_csv(cv_results_file, index=False)
    print(f"交叉验证结果已保存到: {cv_results_file}")

    return best_params, best_score

def save_processed_data(df_clean, train_df, val_df, test_df, features, data_file):
    print(f"保存预处理数据到 {data_file}...")
    # 使用不带时区的当前时间
    data_dict = {'df_clean': df_clean, 'train_df': train_df, 'val_df': val_df, 'test_df': test_df, 'features': features, 'save_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
    with open(data_file, 'wb') as f: pickle.dump(data_dict, f)
    print(f"数据已保存到 {data_file}")

def load_processed_data(data_file):
    print(f"从 {data_file} 加载预处理数据...")
    if not os.path.exists(data_file):
        print(f"错误：数据文件 {data_file} 不存在！"); return None, None, None, None, None
    with open(data_file, 'rb') as f: data_dict = pickle.load(f)
    print(f"数据加载完成，保存时间: {data_dict.get('save_time', '未知')}")
    return data_dict['df_clean'], data_dict['train_df'], data_dict['val_df'], data_dict['test_df'], data_dict['features']

def train_model(save_data=False, load_data=False, data_file=f'{MODEL_BASENAME}.pkl',
                coin_name=None, db_path=None, symbol=None, interval=None, market='spot',
                start_time=None, end_time=None, use_time_series_cv=True, cv_splits=5, cv_trials=100):
    print(f"开始训练LightGBM模型 - {MODEL_BASENAME.replace('_', ' ').title()}")

    coin_config = get_coin_config(coin_name)

    if load_data:
        df_clean, train_df, val_df, test_df, features = load_processed_data(data_file)
        if df_clean is None: load_data = False
    if not load_data:
        df = load_data_for_training(coin_name, db_path, symbol, interval, market, start_time=start_time, end_time=end_time)
        if df is None:
            print("❌ 数据加载失败，退出训练。")
            return
        actual_symbol = symbol or (coin_config.get('api_symbol', f"{coin_name}USDT") if coin_config else f"{coin_name}USDT")
        df_clean = prepare_features_and_labels(df, db_path, actual_symbol, market)
        train_df, val_df, test_df = split_data(df_clean)
        features = get_feature_list(df_clean,time_frame=coin_config.get('timeframe_minutes', 5))
        if save_data:
            save_processed_data(df_clean, train_df, val_df, test_df, features, data_file)

    target = 'label'

    if use_time_series_cv:
        print(f"\n🔍 使用时序交叉验证进行超参数优化...")
        train_val_df = pd.concat([train_df, val_df])
        best_params, best_cv_score = time_series_cross_validation(
            train_val_df, features, target, n_splits=cv_splits, n_trials=cv_trials
        )
        print(f"\n✅ 时序交叉验证完成，使用最佳参数训练最终模型...")
        print(f"最佳参数: {best_params}")
        print(f"交叉验证得分: {best_cv_score:.2f}")

        X_train, y_train = train_df[features], train_df[target]
        X_val, y_val = val_df[features], val_df[target]
        X_test, y_test = test_df[features], test_df[target]

        lgbm = lgb.LGBMClassifier(objective='binary', metric='auc', n_jobs=-1, random_state=42, verbose=-1, **best_params)
    else:
        print(f"\n⚠️  使用默认参数训练模型（未启用时序交叉验证）...")
        X_train, y_train = train_df[features], train_df[target]
        X_val, y_val = val_df[features], val_df[target]
        X_test, y_test = test_df[features], test_df[target]
        lgbm = lgb.LGBMClassifier(
            objective='binary', metric='auc', n_estimators=2000,
            learning_rate=0.05, n_jobs=-1, random_state=42, verbose=-1
        )
    
    print(f"开始训练LightGBM模型...")
    lgbm.fit(X_train, y_train, eval_set=[(X_val, y_val)], eval_metric='auc',
             callbacks=[lgb.early_stopping(stopping_rounds=100, verbose=False)])

    print(f"基础模型训练完成，开始概率校准...")
    calibrated_model = CalibratedClassifierCV(lgbm, method='isotonic', cv='prefit')
    calibrated_model.fit(X_val, y_val)

    print(f"概率校准完成，开始阈值优化...")
    val_probabilities = calibrated_model.predict_proba(X_val)[:, 1]
    thresholds_to_test = np.arange(0.52, 0.80, 0.01)
    best_score, best_threshold = -np.inf, 0.5
    for threshold in thresholds_to_test:
        predictions = np.where(val_probabilities > threshold, 1, np.where(val_probabilities < (1 - threshold), 0, -1))
        correct_trades = (predictions == y_val) & (predictions != -1)
        incorrect_trades = (predictions != y_val) & (predictions != -1)
        current_score = correct_trades.sum() - incorrect_trades.sum()
        if current_score > best_score:
            best_score, best_threshold = current_score, threshold

    print(f"验证集上的最优得分: {best_score:.0f}, 最优信心阈值: {best_threshold:.3f}")
    evaluate_on_test_set(calibrated_model, X_test, y_test, test_df, best_threshold)
    print(f"最终信心阈值: {best_threshold:.3f}")

    extra_config = {}
    if use_time_series_cv:
        extra_config.update({
            'time_series_cv_used': True, 'cv_splits': cv_splits, 'cv_trials': cv_trials,
            'best_cv_score': float(best_cv_score) if best_cv_score != -np.inf else 'N/A',
            'best_cv_params': best_params
        })
    else:
        extra_config['time_series_cv_used'] = False
    
    save_model_and_config(calibrated_model, features, best_threshold, len(X_train), len(X_val), len(X_test), extra_config)
    analyze_feature_importance(lgbm, features)

def evaluate_on_test_set(model, X_test, y_test, test_df, best_threshold):
    """
    在测试集上评估模型, 并将详细结果保存到CSV文件。
    """
    print(f"\n--- 测试集评估 (使用阈值: {best_threshold:.3f}) ---")
    test_probabilities = model.predict_proba(X_test)[:, 1]
    results_log = []

    for i in range(len(test_probabilities)):
        prob = test_probabilities[i]
        actual_result = y_test.iloc[i]
        current_price = test_df.iloc[i]['close']
        timestamp = test_df.index[i]
        
        guess = -1  # -1 代表放弃
        action_str = "放弃"
        result_str = "-"
        score_for_this_trade = 0

        if prob > best_threshold:
            guess = 1
            action_str = "\033[92m猜先涨\033[0m"
        elif prob < (1 - best_threshold):
            guess = 0
            action_str = "\033[91m猜先跌\033[0m"
        
        if guess != -1:
            if guess == actual_result:
                score_for_this_trade = 1
                result_str = "成功✅"
            else:
                score_for_this_trade = -1
                result_str = "失败❌"
        
        # 为了保存到CSV，去除颜色代码
        action_for_csv = action_str.replace('\033[92m', '').replace('\033[91m', '').replace('\033[0m', '')

        log_entry = {
            'Timestamp': timestamp, 'ClosePrice': current_price,
            'ConfidenceUp': prob, 'Action': action_for_csv, 'Prediction': guess,
            'ActualResult': actual_result, 'Outcome': result_str, 'Score': score_for_this_trade
        }
        results_log.append(log_entry)

    results_df = pd.DataFrame(results_log)
    col_order = ['Timestamp', 'ClosePrice', 'ConfidenceUp', 'Action', 'Prediction', 'ActualResult', 'Outcome', 'Score']
    results_df = results_df[col_order]

    trades_made = (results_df['Prediction'] != -1).sum()
    wins = (results_df['Score'] == 1).sum()
    losses = (results_df['Score'] == -1).sum()
    test_score = wins - losses

    print(f"总样本数: {len(test_df)}")
    print(f"猜测次数: {trades_made} (占比: {trades_made/len(test_df)*100:.2f}%)")
    if trades_made > 0:
        print(f"胜率: {wins/trades_made*100:.2f}%")
    print(f"总得分: {test_score:+d}")

    results_filename = f'test_results_{MODEL_BASENAME}.csv'
    output_dir = get_output_dir()
    if output_dir:
        results_filename = os.path.join(output_dir, results_filename)
    try:
        results_df.to_csv(results_filename, index=False, float_format='%.4f')
        print(f"\n详细测试结果已保存到文件: {results_filename}")
    except Exception as e:
        print(f"\n保存测试结果到CSV时出错: {e}")

def save_model_and_config(model, features, best_threshold, train_size, val_size, test_size, extra_config=None):
    print(f"\n保存模型和配置...")
    model_file = f'{MODEL_BASENAME}_model.joblib'
    config_file = f'{MODEL_BASENAME}_config.json'
    output_dir = get_output_dir()
    if output_dir:
        model_file = os.path.join(output_dir, model_file)
        config_file = os.path.join(output_dir, config_file)
    
    joblib.dump(model, model_file)
    config = {
        'best_threshold': best_threshold, 'feature_list': features,
        'model_type': f'LGBM_{MODEL_BASENAME}',
        'target_description': f'predict_first_{UP_THRESHOLD*100}%_move_within_{MAX_LOOKFORWARD_MINUTES}_minutes',
        'training_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'train_size': train_size, 'val_size': val_size, 'test_size': test_size,
        'up_threshold': UP_THRESHOLD, 'down_threshold': DOWN_THRESHOLD,
        'max_lookforward_minutes': MAX_LOOKFORWARD_MINUTES, 'timeframe_minutes': TIMEFRAME_MINUTES
    }

    if extra_config:
        config.update(extra_config)

    with open(config_file, 'w') as f: json.dump(config, f, indent=2, ensure_ascii=False)
    print(f"模型文件: {model_file}\n配置文件: {config_file}")

def analyze_feature_importance(lgbm, features):
    print("\n" + "="*60 + f"\n特征重要性分析\n" + "="*60)
    importance_df = pd.DataFrame({'feature': features, 'importance': lgbm.feature_importances_}).sort_values('importance', ascending=False)
    print(importance_df.head(20).to_string(index=False))
    importance_file = f'feature_importance_{MODEL_BASENAME}.csv'
    output_dir = get_output_dir()
    if output_dir:
        importance_file = os.path.join(output_dir, importance_file)
    importance_df.to_csv(importance_file, index=False)
    print(f"\n特征重要性已保存到 {importance_file}")

def validate_model(model_file, config_file, load_data=False, data_file=f'{MODEL_BASENAME}.pkl',
                  coin_name=None, db_path=None, symbol=None, interval=None, market='spot',
                  start_time=None, end_time=None):
    print(f"加载模型: {model_file}\n加载配置: {config_file}")
    if not os.path.exists(model_file) or not os.path.exists(config_file):
        print(f"错误：模型或配置文件不存在！"); return
    model = joblib.load(model_file)
    with open(config_file, 'r') as f: config = json.load(f)
    print(f"模型类型: {config.get('model_type', '未知')}, 训练日期: {config.get('training_date', '未知')}")

    coin_config = get_coin_config(coin_name)

    if load_data:
        df_clean, _, _, test_df, _ = load_processed_data(data_file)
        if df_clean is None: load_data = False
    if not load_data:
        df = load_data_for_training(coin_name, db_path, symbol, interval, market, start_time=start_time, end_time=end_time)
        if df is None:
            print("❌ 数据加载失败，退出验证。")
            return
        actual_symbol = symbol or (coin_config.get('api_symbol', f"{coin_name}USDT") if coin_config else f"{coin_name}USDT")
        df_clean = prepare_features_and_labels(df, db_path, actual_symbol, market)
        _, _, test_df = split_data(df_clean)
    
    features = config.get('feature_list', [])
    missing_features = [f for f in features if f not in df_clean.columns]
    if missing_features:
        print(f"错误：以下特征在数据中不存在: {missing_features}"); return
    
    target = 'label'
    X_test, y_test = test_df[features], test_df[target]
    print(f"测试集大小: {len(X_test)}")
    evaluate_on_test_set(model, X_test, y_test, test_df, config.get('best_threshold', 0.5))
    print("\n验证完成！")

def main():
    parser = argparse.ArgumentParser(description="多币种 LGBM 模型训练器")
    parser.add_argument("--coin", default="ETH", help="币种名称 (如: DOT, SEI)")
    parser.add_argument("--mode", choices=['train', 'validate'], default='train', help="运行模式")
    parser.add_argument("--model-file", help="模型文件路径 - 可选，会根据币种自动生成")
    parser.add_argument("--config-file", help="配置文件路径 - 可选，会根据币种自动生成")
    parser.add_argument("--save-data", action='store_true', help="保存预处理后的数据")
    parser.add_argument("--load-data", action='store_true', help="加载预处理后的数据")
    parser.add_argument("--data-file", help="预处理数据文件路径 - 可选，会根据币种自动生成")

    # 时序交叉验证参数
    parser.add_argument("--use-time-series-cv", action='store_true', default=True, help="使用时序交叉验证进行超参数优化 (默认启用)")
    parser.add_argument("--no-time-series-cv", action='store_true', help="禁用时序交叉验证，使用默认参数")
    parser.add_argument("--cv-splits", type=int, default=5, help="时序交叉验证的分割数量 (默认: 5)")
    parser.add_argument("--cv-trials", type=int, default=100, help="Optuna在时序交叉验证中的尝试次数 (默认: 100)")

    # SQLite数据源参数
    parser.add_argument("--db-path", help="SQLite数据库路径", default='coin_data.db')
    parser.add_argument("--symbol", help="交易对符号")
    parser.add_argument("--interval", help="时间间隔")
    parser.add_argument("--market", choices=['spot', 'futures'], default='spot', help="市场类型")

    # 时间范围参数
    parser.add_argument("--start-time", help="训练数据开始时间，格式: 2024-01-01 或 2024-01-01 12:00:00")
    parser.add_argument("--end-time", help="训练数据结束时间，格式: 2024-12-31 或 2024-12-31 23:59:59")

    args = parser.parse_args()

    coin_config = get_coin_config(args.coin)
    if coin_config is None:
        exit(1)

    global TIMEFRAME_MINUTES, UP_THRESHOLD, DOWN_THRESHOLD, MAX_LOOKFORWARD_MINUTES, DATA_SOURCE, MODEL_BASENAME, PRICE_MULTIPLIER
    TIMEFRAME_MINUTES = coin_config['timeframe_minutes']
    UP_THRESHOLD = coin_config['up_threshold']
    DOWN_THRESHOLD = coin_config['down_threshold']
    MAX_LOOKFORWARD_MINUTES = coin_config['max_lookforward_minutes']
    MODEL_BASENAME = coin_config['model_basename']
    PRICE_MULTIPLIER = coin_config.get('price_multiplier', 1.0)

    data_source = create_data_source_config(args.coin, args.db_path, args.symbol, args.interval, args.market)
    model_file = args.model_file or f"{MODEL_BASENAME}_model.joblib"
    config_file = args.config_file or f"{MODEL_BASENAME}_config.json"
    data_file = args.data_file or f"{MODEL_BASENAME}.pkl"
    use_time_series_cv = args.use_time_series_cv and not args.no_time_series_cv

    print(f"=== {coin_config['display_name']} 模型训练 ===")
    print_data_source_info(args.coin, data_source)
    print(f"目标涨幅: {UP_THRESHOLD*100:.1f}%")
    print(f"目标跌幅: {DOWN_THRESHOLD*100:.1f}%")
    print(f"最大等待: {MAX_LOOKFORWARD_MINUTES}分钟")
    print(f"模型基名: {MODEL_BASENAME}")
    if args.mode == 'train':
        print(f"时序交叉验证 (Optuna): {'启用' if use_time_series_cv else '禁用'}")
        if use_time_series_cv:
            print(f"交叉验证分割数: {args.cv_splits}")
            print(f"交叉验证尝试次数: {args.cv_trials}")
    print()

    if args.mode == 'train':
        train_model(args.save_data, args.load_data, data_file, args.coin, args.db_path,
                   args.symbol, args.interval, args.market, args.start_time, args.end_time,
                   use_time_series_cv, args.cv_splits, args.cv_trials)
    else:
        validate_model(model_file, config_file, args.load_data, data_file, args.coin,
                      args.db_path, args.symbol, args.interval, args.market, args.start_time, args.end_time)

if __name__ == '__main__':
    main()