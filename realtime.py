#!/usr/bin/env python3
"""
多币种实时预测脚本 (简化版)
基于已训练的LightGBM模型进行实时价格预测。
每次预测都会获取最新的1000条K线数据，无需维护本地数据状态。
特征计算逻辑从共享的 model_utils.py 模块导入。
日志系统经过优化，确保稳定写入文件并简化API错误记录。
支持通过配置文件切换不同币种。
"""

import pandas as pd
import joblib
import json
import numpy as np
import time
import requests
from datetime import datetime, timezone
import os
import signal
import sys
import logging
from typing import Dict, Optional, Tuple
import platform
import argparse
import pytz

# 从共享模块导入特征计算函数，确保逻辑一致性
from model_utils_815 import calculate_features, get_coin_config, get_output_dir

BINANCE_API_ENDPOINTS = [
    "https://api.binance.com",
    "https://api-gcp.binance.com",
    "https://api1.binance.com",
    "https://api2.binance.com",
    "https://api3.binance.com",
    "https://api4.binance.com",
]

class CoinRealTimePredictor:
    def __init__(self,
                 coin_name: str,
                 model_file: str = None,
                 config_file: str = None,
                 log_file: str = None,
                 update_interval=60,
                 enable_sound=True):
        """
        初始化多币种实时预测器
        """
        self.coin_name = coin_name.upper()

        # 获取币种配置
        self.coin_config = get_coin_config(self.coin_name)
        if self.coin_config is None:
            raise ValueError(f"无法获取币种 {self.coin_name} 的配置")

        # 设置默认文件路径
        self.api_symbol = self.coin_config['api_symbol']
        self.display_name = self.coin_config['display_name']
        self.output_dir = get_output_dir()
        self.model_file = model_file or os.path.join(self.output_dir, f"{self.coin_config['model_basename']}_model.joblib")
        self.config_file = config_file or os.path.join(self.output_dir, f"{self.coin_config['model_basename']}_config.json")
        self.log_file = log_file or os.path.join(self.output_dir, f"{self.coin_name.lower()}_prediction_log.txt")

        self.update_interval = update_interval
        self.enable_sound = enable_sound
        self.running = True

        self.model = None
        self.config = None
        self.timeframe_minutes = self.coin_config['timeframe_minutes']
        self.local_tz = pytz.timezone('Asia/Shanghai') # 定义UTC+8时区

        self.active_predictions: Dict[str, dict] = {}
        self.total_predictions = 0
        self.successful_predictions = 0
        self.failed_predictions = 0
        self.timeout_predictions = 0

        self.logger = self.setup_logging()
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)

        self.logger.info("="*20 + f" {self.display_name} 预测器启动 (简化版) " + "="*20)
        self.logger.info(f"币种: {self.display_name}")
        self.logger.info(f"API符号: {self.api_symbol}")
        self.logger.info(f"模型文件: {self.model_file}")
        self.logger.info(f"配置文件: {self.config_file}")
        self.logger.info(f"日志文件: {self.log_file}")
        self.logger.info(f"更新间隔: {self.update_interval}秒")
        self.logger.info(f"报警声: {'启用' if self.enable_sound else '禁用'}")
        self.logger.info("按 Ctrl+C 停止预测\n")

    def signal_handler(self, signum, frame):
        self.logger.info(f"接收到退出信号 {signum}，正在停止...")
        self.running = False

    def setup_logging(self) -> logging.Logger:
        logger = logging.getLogger(f'{self.coin_name}Predictor')
        logger.setLevel(logging.INFO)
        logger.propagate = False
        if logger.hasHandlers():
            logger.handlers.clear()
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        stream_handler = logging.StreamHandler(sys.stdout)
        stream_handler.setLevel(logging.INFO)
        stream_handler.setFormatter(formatter)
        logger.addHandler(stream_handler)
        return logger

    def play_alert_sound(self, message):
        if not self.enable_sound: return
        try:
            system = platform.system().lower()
            if system == "windows":
                import winsound
                winsound.MessageBeep(winsound.MB_ICONEXCLAMATION)
            elif system == "darwin":
                os.system(f'say "{message}"')
            elif system == "linux":
                os.system('paplay /usr/share/sounds/freedesktop/stereo/complete.oga 2>/dev/null || echo -e "\\a"')
            else:
                print('\a' * 3)
        except Exception as e:
            print('\a' * 3)
            self.logger.warning(f"播放报警声失败: {e}")

    def log_message(self, event_type: str, message: str, data: dict = None, level: str = 'info'):
        log_entry = f"[{event_type}] {message}"
        if data:
            log_entry += f" | 数据: {json.dumps({k: (f'{v:.4f}' if isinstance(v, float) else v) for k, v in data.items()})}"
        log_method = getattr(self.logger, level, self.logger.info)
        log_method(log_entry)

    def load_model_and_config(self) -> bool:
        try:
            self.model = joblib.load(self.model_file)
            with open(self.config_file, 'r') as f:
                self.config = json.load(f)
            required_keys = ['feature_list', 'best_threshold', 'up_threshold', 'down_threshold', 'max_lookforward_minutes']
            if not all(key in self.config for key in required_keys):
                raise ValueError(f"配置文件缺少关键项，需要: {required_keys}")
            self.timeframe_minutes = self.config.get('timeframe_minutes', 5)
            self.log_message("系统", "模型和配置加载成功")
            self.logger.info(f"   - 目标涨幅: {self.config['up_threshold']*100:.2f}%")
            self.logger.info(f"   - 目标跌幅: {self.config['down_threshold']*100:.2f}%")
            self.logger.info(f"   - 最优阈值: {self.config['best_threshold']:.3f}")
            self.logger.info(f"   - K线周期: {self.timeframe_minutes}分钟")
            return True
        except Exception as e:
            self.logger.critical(f"加载模型或配置失败: {e}", exc_info=True)
            return False

    def _fetch_binance_klines(self, limit=1000) -> Optional[pd.DataFrame]:
        params = {'symbol': self.api_symbol, 'interval': f'{self.timeframe_minutes}m', 'limit': limit}
        for base_url in BINANCE_API_ENDPOINTS:
            try:
                url = f"{base_url}/api/v3/klines"
                response = requests.get(url, params=params, timeout=10)
                response.raise_for_status()
                klines = response.json()
                if not klines: return None
                df = pd.DataFrame(klines, columns=['Timestamp', 'Open', 'High', 'Low', 'Close', 'Volume', 'CloseTime', 'QuoteVolume', 'TradeCount', 'TakerBuyBaseVolume', 'TakerBuyQuoteVolume', 'Ignore'])
                df = df.astype({'Open': float, 'High': float, 'Low': float, 'Close': float, 'Volume': float})
                df['Timestamp'] = pd.to_datetime(df['Timestamp'], unit='ms', utc=True)
                df.set_index('Timestamp', inplace=True)
                df.rename(columns={'Open': 'open', 'High': 'high', 'Low': 'low', 'Close': 'close', 'Volume': 'volume'}, inplace=True)
                return df[['open', 'high', 'low', 'close', 'volume']]
            except requests.exceptions.RequestException:
                continue
        self.logger.error("所有Binance API节点均无法连接，获取K线数据失败。")
        return None

    def make_prediction(self, df: pd.DataFrame) -> Tuple[Optional[int], float, float, Optional[datetime]]:
        try:
            longest_window = 720 // self.timeframe_minutes
            if len(df) < longest_window + 20:
                self.logger.warning(f"数据不足 ({len(df)}条)，无法计算所有特征，跳过此次预测。")
                return None, 0.0, 0.0, None

            features_df = calculate_features(df, timeframe=self.timeframe_minutes)
            features_df_clean = features_df.dropna()
            
            # <<< 新增修改 1/2 >>>
            # 检查清理后数据是否足够使用倒数第二根K线
            if len(features_df_clean) < 2:
                self.logger.warning(f"特征计算后数据不足2行 ({len(features_df_clean)}行)，无法使用已收盘K线预测。")
                return None, 0.0, 0.0, None

            # <<< 新增修改 2/2 >>>
            # 使用倒数第二根（最新已收盘）的K线进行预测，确保稳定性
            latest_features_series = features_df_clean.iloc[-2]
            
            kline_timestamp = latest_features_series.name.to_pydatetime()
            feature_list = self.config['feature_list']
            if any(f not in latest_features_series.index for f in feature_list):
                 return None, 0.0, 0.0, None

            latest_features_df = latest_features_series[feature_list].to_frame().T
            current_price = latest_features_series['close']
            probability = self.model.predict_proba(latest_features_df)[0, 1]
            best_threshold = self.config['best_threshold']
            guess = None
            if probability > best_threshold: guess = 1
            elif probability < (1 - best_threshold): guess = 0
            return guess, probability, current_price, kline_timestamp
        except Exception as e:
            self.logger.error(f"预测过程中发生严重错误: {e}", exc_info=True)
            return None, 0.0, 0.0, None

    def add_prediction(self, guess: int, probability: float, price: float, timestamp: datetime):
        """添加新的预测到活跃列表，并使用新的日志格式"""
        prediction_id = f"pred_{timestamp.strftime('%Y%m%d_%H%M%S')}"
        max_wait = pd.Timedelta(minutes=self.config['max_lookforward_minutes'])
        expire_time = timestamp + max_wait

        prediction = {
            'id': prediction_id, 'guess': guess, 'probability': probability, 'price': price,
            'timestamp': timestamp, 'expire_time': expire_time,
            'up_target': price * (1 + self.config['up_threshold']),
            'down_target': price * (1 - self.config['down_threshold']), 'status': 'active'
        }
        self.active_predictions[prediction_id] = prediction
        self.total_predictions += 1
        
        direction_str = f"先涨{self.config['up_threshold']*100:.1f}%" if guess == 1 else f"先跌{self.config['down_threshold']*100:.1f}%"
        local_timestamp_str = timestamp.astimezone(self.local_tz).strftime('%Y-%m-%d %H:%M:%S %z')
        
        log_message_str = (
            f"K线: {local_timestamp_str}, 方向: {direction_str}, 价格: {price:.4f}, "
            f"信心: {probability:.4f}, 目标: [涨: {prediction['up_target']:.4f}, 跌: {prediction['down_target']:.4f}]"
        )
        self.log_message("新预测", log_message_str)
        
        alert_message = f"发现交易信号: {self.display_name} {direction_str}，当前价格 {price:.4f}"
        self.play_alert_sound(alert_message)

    def check_predictions(self, current_price: float, current_time: datetime):
        completed_ids = []
        for pred_id, pred in self.active_predictions.items():
            if pred['status'] != 'active': continue
            if current_price >= pred['up_target']:
                result, reason = (1, "达到上涨目标") if pred['guess'] == 1 else (0, "达到上涨目标 (预测错误)")
                self.complete_prediction(pred_id, result, current_price, current_time, reason)
                completed_ids.append(pred_id)
            elif current_price <= pred['down_target']:
                result, reason = (1, "达到下跌目标") if pred['guess'] == 0 else (0, "达到下跌目标 (预测错误)")
                self.complete_prediction(pred_id, result, current_price, current_time, reason)
                completed_ids.append(pred_id)
            elif current_time >= pred['expire_time']:
                self.complete_prediction(pred_id, -1, current_price, current_time, "超时")
                completed_ids.append(pred_id)
        for pred_id in completed_ids:
            if pred_id in self.active_predictions:
                del self.active_predictions[pred_id]

    def complete_prediction(self, pred_id: str, result: int, final_price: float, end_time: datetime, reason: str):
        pred = self.active_predictions.get(pred_id)
        if not pred: return
        pred['status'] = 'completed'
        duration_minutes = (end_time - pred['timestamp']).total_seconds() / 60
        status_map = {1: "成功✅", 0: "失败❌", -1: "超时⏰"}
        status_str = status_map.get(result, "未知")
        if result == 1: self.successful_predictions += 1
        elif result == 0: self.failed_predictions += 1
        else: self.timeout_predictions += 1
        direction_str = f"先涨{self.config['up_threshold']*100:.1f}%" if pred['guess'] == 1 else f"先跌{self.config['down_threshold']*100:.1f}%"
        self.log_message("预测完成", f"ID: {pred_id}, 结果: {status_str}", {
            'direction': direction_str, 'confidence': pred['probability'], 'start_price': pred['price'],
            'end_price': final_price, 'duration_min': duration_minutes, 'reason': reason
        })

    def print_status(self):
        active_count = len([p for p in self.active_predictions.values() if p['status'] == 'active'])
        valid_trades = self.successful_predictions + self.failed_predictions
        success_rate = (self.successful_predictions / max(1, valid_trades)) * 100
        self.logger.info("\n--- 状态摘要 ---\n"
                        f"  活跃预测: {active_count}, 总预测数: {self.total_predictions}\n"
                        f"  成功: {self.successful_predictions}, 失败: {self.failed_predictions}, 超时: {self.timeout_predictions}\n"
                        f"  胜率 (非超时): {success_rate:.2f}%\n"
                        "------------------")

    def run(self):
        if not self.load_model_and_config(): return
        self.logger.info("启动主预测循环...")
        last_kline_timestamp = None

        while self.running:
            try:
                if self.active_predictions:
                    try:
                        price_response = requests.get(f"https://api.binance.com/api/v3/ticker/price?symbol={self.api_symbol}", timeout=5)
                        if price_response.status_code == 200:
                            current_price = float(price_response.json()['price'])
                            self.check_predictions(current_price, datetime.now(timezone.utc))
                    except requests.exceptions.RequestException: pass

                df = self._fetch_binance_klines(limit=1000)
                if df is None or df.empty:
                    time.sleep(self.update_interval)
                    continue

                current_kline_timestamp = df.index[-1]
                if last_kline_timestamp and current_kline_timestamp <= last_kline_timestamp:
                    time.sleep(self.update_interval)
                    continue
                
                # 日志中打印的是最新（未收盘）K线的时间，表示我们已经观察到了新周期
                local_kline_time_str = current_kline_timestamp.astimezone(self.local_tz).strftime('%Y-%m-%d %H:%M:%S %z')
                self.logger.info(f"[{self.coin_name}] 检测到新的K线: {local_kline_time_str}，开始分析上一根已收盘K线...")
                last_kline_timestamp = current_kline_timestamp

                guess, probability, pred_price, pred_timestamp = self.make_prediction(df)

                if guess is not None and pred_timestamp:
                    prediction_time = pred_timestamp.replace(tzinfo=timezone.utc)
                    self.add_prediction(guess, probability, pred_price, prediction_time)
                else:
                    if pred_timestamp and pred_price > 0:
                        local_timestamp_str = pred_timestamp.astimezone(self.local_tz).strftime('%Y-%m-%d %H:%M:%S %z')
                        log_message_str = f"[{self.coin_name}] 分析已收盘K线: {local_timestamp_str}, 价格: {pred_price:.4f}, 信心不足 ({probability:.4f}), 放弃预测"
                    else:
                        log_message_str = f"[{self.coin_name}] 分析已收盘K线: 数据不足或特征计算失败, 信心 ({probability:.4f}), 放弃预测"
                    self.log_message("分析结果", log_message_str)
                
                if self.total_predictions > 0 and self.total_predictions % 10 == 0:
                    self.print_status()

                time.sleep(self.update_interval)

            except KeyboardInterrupt: self.running = False
            except Exception as e:
                self.logger.error(f"主循环运行时发生严重错误: {e}", exc_info=True)
                time.sleep(15)

        self.print_final_summary()

    def print_final_summary(self):
        self.logger.info("\n" + "="*20 + " 实时预测总结 " + "="*20)
        self.print_status()
        self.log_message("系统", "预测器已停止", level='info')

def main():
    parser = argparse.ArgumentParser(description="多币种实时预测器 (简化版)")
    parser.add_argument("--coin", default="DOT", help="币种名称 (如: DOT, SEI)")
    parser.add_argument("--model-file", help="模型文件路径")
    parser.add_argument("--config-file", help="模型配置文件路径")
    parser.add_argument("--log-file", help="日志文件路径")
    parser.add_argument("--update-interval", type=int, default=60, help="数据获取和预测的更新间隔（秒）")
    parser.add_argument("--no-sound", action="store_true", help="禁用报警声")
    args = parser.parse_args()

    try:
        predictor = CoinRealTimePredictor(
            coin_name=args.coin, model_file=args.model_file, config_file=args.config_file,
            log_file=args.log_file, update_interval=args.update_interval, enable_sound=not args.no_sound
        )
        if not os.path.exists(predictor.model_file) or not os.path.exists(predictor.config_file):
            print(f"❌ 错误: 模型文件 '{predictor.model_file}' 或配置文件 '{predictor.config_file}' 不存在。")
            return
        predictor.run()
    except ValueError as e:
        print(f"❌ 配置错误: {e}")
        return

if __name__ == "__main__":
    main()