#!/usr/bin/env python3
"""
测试ETH 5m实盘交易配置
验证订单管理器和实盘交易集成
"""

import os
import sys
import logging
from datetime import datetime, timezone
import pytz

# 添加trade目录到路径
sys.path.append('trade')

from trade.order_manager import OrderManager, OrderSide, OrderType
from trade.database_manager import DatabaseManager

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger('TestETHLive')

def test_eth_live_config():
    """测试ETH实盘配置"""
    logger = setup_logging()
    
    # 检查环境变量
    api_key = os.environ.get('BINANCE_APIKEY')
    if not api_key:
        logger.error("❌ BINANCE_APIKEY 环境变量未设置")
        return False
    
    # 检查rsa.txt文件
    rsa_file = 'rsa.txt'  # 在项目根目录
    if not os.path.exists(rsa_file):
        logger.error(f"❌ {rsa_file} 文件不存在")
        return False
    
    logger.info("✅ 环境配置检查通过")
    return True

def test_order_manager():
    """测试订单管理器"""
    logger = setup_logging()
    
    # 创建数据库管理器
    db_manager = DatabaseManager("test_eth_trading.db")
    
    # ETH 5m实盘配置
    config = {
        'live_trading': True,
        'futures_leverage': 20,
        'paper_usdt_balance': 1000.0,
        'taker_fee_rate': 0.0005,
        'paper_commission_rate': 0.0005
    }
    
    try:
        # 创建订单管理器
        order_manager = OrderManager(logger=logger, db_manager=db_manager, config=config)
        
        logger.info(f"✅ 订单管理器创建成功")
        logger.info(f"   实盘模式: {order_manager.live_trading}")
        logger.info(f"   杠杆倍数: {order_manager.futures_leverage}x")
        
        if order_manager.live_trading:
            # 测试币安连接
            if order_manager.client:
                try:
                    order_manager.client.ping()
                    logger.info("✅ 币安连接测试成功")
                    
                    # 测试设置杠杆
                    order_manager.set_symbol_leverage("ETHUSDT", 20)
                    
                    # 测试获取余额
                    balance = order_manager.get_available_usdt()
                    if balance:
                        logger.info(f"✅ 账户USDT余额: {balance:.2f}")
                    else:
                        logger.warning("⚠️ 无法获取账户余额")
                        
                except Exception as e:
                    logger.error(f"❌ 币安连接测试失败: {e}")
                    return False
            else:
                logger.error("❌ 币安客户端未初始化")
                return False
        
        # 测试数量计算
        test_price = 3000.0  # ETH价格
        quantity = order_manager.compute_order_quantity("ETHUSDT", test_price, 1.0)  # 1%风险
        logger.info(f"✅ 数量计算测试: 价格={test_price}, 风险=1%, 数量={quantity:.3f}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 订单管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_order_creation():
    """测试订单创建"""
    logger = setup_logging()
    
    db_manager = DatabaseManager("test_eth_trading.db")
    config = {'live_trading': False, 'paper_usdt_balance': 1000.0}  # 模拟模式测试
    
    try:
        order_manager = OrderManager(logger=logger, db_manager=db_manager, config=config)
        
        # 测试创建开仓订单
        entry_order = order_manager.execute_market_order(
            position_id="test_pos_001",
            coin_symbol="ETHUSDT",
            side=OrderSide.BUY,
            quantity=0.1,
            price_hint=3000.0,
            position_side="LONG"
        )
        
        logger.info(f"✅ 开仓订单创建成功: {entry_order.order_id}")
        logger.info(f"   状态: {entry_order.status.value}")
        logger.info(f"   数量: {entry_order.quantity}")
        
        # 测试创建平仓订单
        exit_order = order_manager.execute_market_order(
            position_id="test_pos_001",
            coin_symbol="ETHUSDT",
            side=OrderSide.SELL,
            quantity=0.1,
            price_hint=3100.0,
            position_side="LONG"
        )
        
        logger.info(f"✅ 平仓订单创建成功: {exit_order.order_id}")
        logger.info(f"   状态: {exit_order.status.value}")
        
        # 获取订单统计
        stats = order_manager.get_order_statistics("ETHUSDT")
        logger.info(f"✅ 订单统计: {stats}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 订单创建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("ETH 5m实盘交易配置测试")
    print("=" * 50)
    
    # 测试1: 环境配置
    print("\n1. 测试环境配置...")
    if not test_eth_live_config():
        print("❌ 环境配置测试失败，请检查配置")
        return
    
    # 测试2: 订单管理器
    print("\n2. 测试订单管理器...")
    if not test_order_manager():
        print("❌ 订单管理器测试失败")
        return
    
    # 测试3: 订单创建
    print("\n3. 测试订单创建...")
    if not test_order_creation():
        print("❌ 订单创建测试失败")
        return
    
    print("\n" + "=" * 50)
    print("✅ 所有测试通过！ETH 5m实盘交易配置就绪")
    print("=" * 50)
    
    print("\n📋 配置摘要:")
    print("   - 实盘模式: 启用")
    print("   - 杠杆倍数: 20x")
    print("   - 风险控制: 1% 每笔订单")
    print("   - 最大仓位: 5个活跃仓位")
    print("   - 手续费率: 0.05%")
    print("   - 币种: ETHUSDT")
    print("   - 时间框架: 5分钟")

if __name__ == "__main__":
    main() 