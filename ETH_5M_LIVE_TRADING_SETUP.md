# ETH 5m 实盘交易集成配置指南 (V3)

## 概述
本系统已成功集成币安合约实盘交易功能，支持ETH 5分钟K线策略的自动开仓、平仓和风险管理。**V3版本引入了Position-First逻辑，提供更好的订单跟踪和状态管理。**

## 🚀 核心功能

### 1. 风险控制
- **每笔订单风险**: 默认1%账户资金（可配置）
- **最大活跃仓位**: 默认5个（可配置）
- **杠杆倍数**: 20倍（可配置）
- **自动平仓**: 达到目标价格或超时时自动创建平仓订单

### 2. 订单管理 (V3增强)
- **Position-First逻辑**: 先创建仓位记录，再执行订单
- **详细状态跟踪**: pending_open → active/open_failed → closing → completed
- **订单关联**: 每个仓位记录entry_order_id和exit_order_id
- **失败处理**: 开仓失败和平仓失败都有明确状态标记
- **自动重试**: 平仓失败时自动重试机制

### 3. 实盘交易集成
- **币安UM合约**: 支持ETHUSDT等主流币种
- **API认证**: 使用环境变量和RSA私钥文件
- **自动杠杆设置**: 启动时自动设置指定杠杆倍数
- **余额查询**: 实时读取账户USDT余额计算下单数量

## 📁 文件结构

```
eth-trade/
├── trade/
│   ├── order_manager.py      # 订单管理器（核心）
│   ├── real_manager.py       # 资金管理（V3 - Position-First逻辑）
│   ├── real_signal.py        # 信号生成
│   ├── real_main.py          # 主程序入口
│   └── database_manager.py   # 数据库管理（已更新Schema）
├── rsa.txt                   # 币安私钥文件
├── .env                      # 环境变量配置
├── test_eth_live_trading.py # 实盘交易测试
└── test_position_first_logic.py # Position-First逻辑测试
```

## ⚙️ 配置要求

### 1. 环境变量
```bash
# .env 文件
BINANCE_APIKEY=your_api_key_here
```

### 2. 私钥文件
```bash
# rsa.txt 文件（项目根目录）
-----BEGIN PRIVATE KEY-----
your_private_key_content
-----END PRIVATE KEY-----
```

### 3. 模型配置
在模型的 `*_config.json` 中添加或覆盖：
```json
{
  "risk_per_order_pct": 1.0,        // 每笔订单风险比例（%）
  "max_active_positions": 5,         // 最大活跃仓位数量
  "paper_usdt_balance": 1000.0,     // 模拟资金规模
  "order_manager": {                 // 订单管理器配置
    "live_trading": true,            // 启用实盘交易
    "futures_leverage": 20,          // 杠杆倍数
    "taker_fee_rate": 0.0005,       // 手续费率
    "paper_commission_rate": 0.0005  // 模拟手续费率
  }
}
```

## 🔧 使用方法

### 1. 启动实盘交易
```bash
# 启动ETH 5m预测器（自动启用实盘）
python trade/real_main.py --coin ETH --update-interval 300
```

### 2. 测试配置
```bash
# 运行实盘交易测试
python test_eth_live_trading.py

# 运行Position-First逻辑测试
python test_position_first_logic.py
```

### 3. 监控订单
```bash
# 查看订单统计
python trade/order_manager.py --table-stats ETHUSDT_5min_futures
```

## 📊 订单流程 (V3)

### 开仓流程
1. **信号生成** → `real_signal.py` 生成交易信号
2. **仓位创建** → `real_manager.py` 立即创建仓位记录（状态：pending_open）
3. **风险检查** → 检查仓位数量限制
4. **数量计算** → `order_manager.py` 根据1%风险计算下单数量
5. **实盘下单** → 通过币安API创建市价开仓单
6. **状态更新** → 根据订单结果更新仓位状态（active/open_failed）
7. **订单关联** → 记录entry_order_id到仓位

### 平仓流程
1. **价格监控** → `real_manager.py` 监控价格达到目标
2. **状态标记** → 标记仓位状态为closing
3. **平仓订单** → 自动创建反向平仓单
4. **结果处理** → 根据平仓订单结果更新最终状态
5. **订单关联** → 记录exit_order_id到仓位
6. **自动重试** → 平仓失败时自动重试机制

## 🆕 V3新特性

### 1. Position-First逻辑
- **意图记录**: 每个交易信号都先创建仓位记录
- **状态跟踪**: 完整的生命周期状态管理
- **失败处理**: 开仓失败有明确的状态标记

### 2. 详细状态系统
- `pending_open`: 仓位已创建，等待开仓
- `active`: 开仓成功，仓位活跃
- `open_failed`: 开仓失败
- `closing`: 正在平仓
- `completed`: 仓位完成

### 3. 订单关联
- `entry_order_id`: 开仓订单ID
- `exit_order_id`: 平仓订单ID
- 完整的订单-仓位关联关系

### 4. 自动重试机制
- 平仓失败时自动重试
- 防止仓位卡在中间状态
- 提高系统可靠性

## 🛡️ 安全特性

### 1. 风险控制
- 单笔订单最大风险限制
- 总仓位数量限制
- 自动超时平仓机制

### 2. 错误处理
- 网络异常自动重试
- API错误优雅降级
- 完整的错误日志记录

### 3. 数据完整性
- 所有订单数据库记录
- 仓位状态实时同步
- 交易历史完整追溯

## 🔍 故障排除

### 1. 连接问题
```bash
# 检查网络连接
ping fapi.binance.com

# 检查API密钥
echo $BINANCE_APIKEY

# 检查私钥文件
ls -la rsa.txt
```

### 2. 权限问题
```bash
# 检查API权限
# 确保API Key有合约交易权限
# 确保IP白名单配置正确
```

### 3. 余额不足
```bash
# 检查账户余额
# 确保有足够的USDT保证金
# 检查杠杆设置是否合理
```

### 4. 状态异常
```bash
# 检查仓位状态
# 查看订单关联
# 检查数据库记录
```

## 📈 性能监控

### 1. 订单统计
- 总订单数量
- 成交率
- 平均手续费
- 订单状态分布

### 2. 仓位统计
- 活跃仓位数量
- 成功/失败率
- 平均持仓时间
- 盈亏统计

### 3. 系统状态
- 网络连接状态
- API调用频率
- 错误率监控
- 响应时间统计

## 🚨 注意事项

1. **实盘风险**: 实盘交易存在资金损失风险，请谨慎使用
2. **API限制**: 注意币安API调用频率限制
3. **网络稳定**: 确保网络连接稳定，避免订单执行失败
4. **资金管理**: 合理设置风险比例，避免过度杠杆
5. **监控告警**: 建议设置关键指标监控和告警
6. **状态监控**: 定期检查仓位状态，确保没有卡住的仓位

## 📞 技术支持

如遇到问题，请检查：
1. 日志文件中的错误信息
2. 网络连接状态
3. API密钥和权限配置
4. 账户余额和杠杆设置
5. 仓位状态和订单关联

---

**版本**: 3.0 (Position-First Logic)  
**更新日期**: 2025-08-18  
**状态**: ✅ 已集成完成，测试通过，包含V3新特性 