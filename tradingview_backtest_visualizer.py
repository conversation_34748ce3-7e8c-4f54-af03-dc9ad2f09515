#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TradingView风格的回测信号可视化工具
加载K线数据和回测日志，在K线图上显示买入卖出信号
"""

import pandas as pd
import numpy as np
import sqlite3
import argparse
import os
from datetime import datetime, timedelta
import pytz
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.express as px
import json
import math

# 引入现有的工具函数
from get_coin_history import get_table_name

# 北京时区
BEIJING_TZ = pytz.timezone('Asia/Shanghai')

def parse_time_input(time_str):
    """解析时间输入"""
    if not time_str:
        return None
    try:
        formats = ['%Y-%m-%d %H:%M:%S', '%Y-%m-%d %H:%M', '%Y-%m-%d', '%m-%d %H:%M', '%m-%d']
        dt = None
        for fmt in formats:
            try:
                dt = datetime.strptime(time_str, fmt)
                break
            except ValueError:
                continue
        if dt is None:
            raise ValueError(f"无法解析时间格式: {time_str}")
        if dt.year == 1900:
            dt = dt.replace(year=datetime.now().year)
        beijing_dt = BEIJING_TZ.localize(dt)
        utc_dt = beijing_dt.astimezone(pytz.UTC)
        return pd.Timestamp(utc_dt).tz_localize(None)
    except Exception as e:
        print(f"时间解析错误: {e}")
        return None

def load_kline_data(db_path, coin, interval, market, start_time=None, end_time=None):
    """从SQLite加载K线数据"""
    # 将币种名称转换为完整的交易对名称
    if coin.upper() in ['BTC', 'ETH', 'DOT', 'ENA', 'LINK', 'SUI', 'UNI']:
        symbol = f"{coin.upper()}USDT"
    else:
        symbol = coin.upper()

    table_name = get_table_name(symbol, interval, market)
    conn = sqlite3.connect(db_path)
    
    query = f"SELECT timestamp, open, high, low, close, volume FROM {table_name}"
    conditions = []
    params = []
    
    if start_time:
        conditions.append("timestamp >= ?")
        params.append(int(start_time.timestamp()))
    if end_time:
        conditions.append("timestamp <= ?")
        params.append(int(end_time.timestamp()))
    
    if conditions:
        query += " WHERE " + " AND ".join(conditions)
    query += " ORDER BY timestamp ASC"
    
    df = pd.read_sql_query(query, conn, params=params)
    conn.close()
    
    if df.empty:
        print("❌ 数据库中无符合条件的K线数据")
        return None
    
    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s', utc=True)
    df.set_index('timestamp', inplace=True)
    df = df.astype(float)
    
    print(f"✅ 加载了 {len(df)} 条K线数据")
    return df

def sample_kline_data(df, max_points=5000):
    """对K线数据进行智能采样以提高渲染性能"""
    if len(df) <= max_points:
        return df

    print(f"⚡ 数据量较大 ({len(df)} 条)，进行智能采样以提高性能...")

    # 计算采样比例
    sample_ratio = max_points / len(df)

    # 使用分层采样：保留最近的数据密度更高
    total_len = len(df)
    recent_len = min(total_len // 3, max_points // 2)  # 最近1/3的数据保留一半的采样点

    # 最近的数据保持较高密度
    recent_data = df.iloc[-recent_len:]
    recent_sample_step = max(1, len(recent_data) // (max_points // 2))
    recent_sampled = recent_data.iloc[::recent_sample_step]

    # 较早的数据使用较低密度
    earlier_data = df.iloc[:-recent_len]
    if len(earlier_data) > 0:
        earlier_sample_step = max(1, len(earlier_data) // (max_points // 2))
        earlier_sampled = earlier_data.iloc[::earlier_sample_step]

        # 合并数据
        sampled_df = pd.concat([earlier_sampled, recent_sampled])
    else:
        sampled_df = recent_sampled

    # 按时间排序
    sampled_df = sampled_df.sort_index()

    print(f"✅ 采样完成：{len(df)} → {len(sampled_df)} 条数据")
    return sampled_df

def filter_signals_by_timerange(backtest_df, start_time, end_time):
    """根据时间范围过滤交易信号"""
    if backtest_df is None or backtest_df.empty:
        return backtest_df

    filtered_df = backtest_df.copy()

    if start_time:
        filtered_df = filtered_df[filtered_df['StartTime'] >= start_time]
    if end_time:
        filtered_df = filtered_df[filtered_df['StartTime'] <= end_time]

    return filtered_df

def load_backtest_log(log_file):
    """加载回测日志"""
    if not os.path.exists(log_file):
        print(f"❌ 回测日志文件不存在: {log_file}")
        return None
    
    df = pd.read_csv(log_file)
    
    # 解析时间戳
    df['StartTime'] = pd.to_datetime(df['StartTimestamp'].str.replace(' UTC+8', '', regex=False))
    df['EndTime'] = pd.to_datetime(df['EndTimestamp'].str.replace(' UTC+8', '', regex=False))
    
    # 转换为UTC时间
    df['StartTime'] = df['StartTime'].dt.tz_localize('Asia/Shanghai').dt.tz_convert('UTC').dt.tz_localize(None)
    df['EndTime'] = df['EndTime'].dt.tz_localize('Asia/Shanghai').dt.tz_convert('UTC').dt.tz_localize(None)
    
    print(f"✅ 加载了 {len(df)} 条回测记录")
    return df

def create_tradingview_chart(kline_df, backtest_df, coin, interval, output_file=None, max_points=5000):
    """创建TradingView风格的K线图表，包含回测信号"""

    # 性能优化：对K线数据进行采样
    if len(kline_df) > max_points:
        kline_df = sample_kline_data(kline_df, max_points)

    # 创建子图：主图为K线，副图为成交量和资金曲线
    fig = make_subplots(
        rows=3, cols=1,
        shared_xaxes=True,
        vertical_spacing=0.02,
        subplot_titles=(f'{coin.upper()} {interval} K线图 - 回测信号', '成交量', '资金曲线'),
        row_heights=[0.6, 0.2, 0.2]
    )

    # 添加K线图
    fig.add_trace(
        go.Candlestick(
            x=kline_df.index,
            open=kline_df['open'],
            high=kline_df['high'],
            low=kline_df['low'],
            close=kline_df['close'],
            name='K线',
            increasing_line_color='#26a69a',
            decreasing_line_color='#ef5350',
            showlegend=False
        ),
        row=1, col=1
    )

    # 添加成交量
    colors = ['#26a69a' if close >= open else '#ef5350'
              for close, open in zip(kline_df['close'], kline_df['open'])]

    fig.add_trace(
        go.Bar(
            x=kline_df.index,
            y=kline_df['volume'],
            name='成交量',
            marker_color=colors,
            opacity=0.7,
            showlegend=False
        ),
        row=2, col=1
    )
    
    if backtest_df is not None and not backtest_df.empty:
        # 性能优化：限制信号数量
        max_signals = 1000
        if len(backtest_df) > max_signals:
            print(f"⚡ 交易信号较多 ({len(backtest_df)} 个)，采样显示前 {max_signals} 个以提高性能...")
            # 保留最重要的信号：成功的交易 + 最近的交易
            successful_trades = backtest_df[backtest_df['Result'] == 1]
            recent_trades = backtest_df.tail(max_signals // 2)

            # 合并并去重
            important_trades = pd.concat([successful_trades, recent_trades]).drop_duplicates()
            if len(important_trades) > max_signals:
                important_trades = important_trades.tail(max_signals)
            backtest_df = important_trades.sort_values('StartTime')

        # 添加资金曲线（采样以提高性能）
        if 'CapitalAfter' in backtest_df.columns:
            capital_data = backtest_df[['EndTime', 'CapitalAfter']].copy()
            if len(capital_data) > 500:  # 资金曲线最多500个点
                step = len(capital_data) // 500
                capital_data = capital_data.iloc[::step]

            fig.add_trace(
                go.Scatter(
                    x=capital_data['EndTime'],
                    y=capital_data['CapitalAfter'],
                    mode='lines',
                    line=dict(color='#2196F3', width=2),
                    name='账户资金',
                    showlegend=False
                ),
                row=3, col=1
            )

        # 计算价格范围用于信号位置调整
        price_range = kline_df['high'].max() - kline_df['low'].min()
        offset = price_range * 0.02  # 2%的偏移量

        # 创建一个简化的时间匹配函数
        def find_closest_kline_price(signal_time, price_type='low'):
            """找到最接近信号时间的K线价格"""
            # 统一时区处理
            if hasattr(signal_time, 'tz') and signal_time.tz:
                signal_time = signal_time.tz_localize(None)

            kline_times = kline_df.index
            if hasattr(kline_times, 'tz') and kline_times.tz:
                kline_times = kline_times.tz_localize(None)

            # 找到最接近的时间点
            time_diffs = abs(kline_times - signal_time)
            closest_idx = time_diffs.argmin()

            # 使用iloc来避免时区问题
            if price_type == 'low':
                return kline_df.iloc[closest_idx]['low'] - offset
            else:  # high
                return kline_df.iloc[closest_idx]['high'] + offset

        # 添加买入信号（预测上涨）- 显示在K线下方
        buy_signals = backtest_df[backtest_df['Prediction'] == 1]
        if not buy_signals.empty:

            # 成功的买入信号
            successful_buys = buy_signals[buy_signals['Result'] == 1]
            failed_buys = buy_signals[buy_signals['Result'] != 1]

            if not successful_buys.empty:
                # 计算成功买入信号的位置
                successful_y_positions = []
                for _, signal in successful_buys.iterrows():
                    y_pos = find_closest_kline_price(signal['StartTime'], 'low')
                    successful_y_positions.append(y_pos)

                fig.add_trace(
                    go.Scatter(
                        x=successful_buys['StartTime'],
                        y=successful_y_positions,
                        mode='markers',
                        marker=dict(
                            symbol='triangle-up',
                            size=14,
                            color='#4CAF50',
                            line=dict(width=2, color='white')
                        ),
                        name='成功买入信号',
                        text=[f"买入信号<br>ID: {pid}<br>信心: {conf:.3f}<br>开始价格: ${sp:.2f}<br>结束价格: ${ep:.2f}<br>收益: ${pl:+.2f}"
                              for pid, conf, sp, ep, pl in zip(successful_buys['PredictionID'],
                                                             successful_buys['Confidence'],
                                                             successful_buys['StartPrice'],
                                                             successful_buys['EndPrice'],
                                                             successful_buys['ProfitLoss'])],
                        hovertemplate='%{text}<extra></extra>'
                    ),
                    row=1, col=1
                )

            if not failed_buys.empty:
                # 计算失败买入信号的位置
                failed_y_positions = []
                for _, signal in failed_buys.iterrows():
                    y_pos = find_closest_kline_price(signal['StartTime'], 'low')
                    failed_y_positions.append(y_pos)

                fig.add_trace(
                    go.Scatter(
                        x=failed_buys['StartTime'],
                        y=failed_y_positions,
                        mode='markers',
                        marker=dict(
                            symbol='triangle-up',
                            size=12,
                            color='#FFC107',
                            opacity=0.7,
                            line=dict(width=1, color='white')
                        ),
                        name='失败买入信号',
                        text=[f"失败买入<br>ID: {pid}<br>信心: {conf:.3f}<br>开始价格: ${sp:.2f}<br>结束价格: ${ep:.2f}<br>亏损: ${pl:+.2f}"
                              for pid, conf, sp, ep, pl in zip(failed_buys['PredictionID'],
                                                             failed_buys['Confidence'],
                                                             failed_buys['StartPrice'],
                                                             failed_buys['EndPrice'],
                                                             failed_buys['ProfitLoss'])],
                        hovertemplate='%{text}<extra></extra>'
                    ),
                    row=1, col=1
                )

        # 添加卖出信号（预测下跌）- 显示在K线上方
        sell_signals = backtest_df[backtest_df['Prediction'] == 0]
        if not sell_signals.empty:
            # 成功的卖出信号
            successful_sells = sell_signals[sell_signals['Result'] == 1]
            failed_sells = sell_signals[sell_signals['Result'] != 1]

            if not successful_sells.empty:
                # 计算成功卖出信号的位置
                successful_sell_y_positions = []
                for _, signal in successful_sells.iterrows():
                    y_pos = find_closest_kline_price(signal['StartTime'], 'high')
                    successful_sell_y_positions.append(y_pos)

                fig.add_trace(
                    go.Scatter(
                        x=successful_sells['StartTime'],
                        y=successful_sell_y_positions,
                        mode='markers',
                        marker=dict(
                            symbol='triangle-down',
                            size=14,
                            color='#F44336',
                            line=dict(width=2, color='white')
                        ),
                        name='成功卖出信号',
                        text=[f"卖出信号<br>ID: {pid}<br>信心: {conf:.3f}<br>开始价格: ${sp:.2f}<br>结束价格: ${ep:.2f}<br>收益: ${pl:+.2f}"
                              for pid, conf, sp, ep, pl in zip(successful_sells['PredictionID'],
                                                             successful_sells['Confidence'],
                                                             successful_sells['StartPrice'],
                                                             successful_sells['EndPrice'],
                                                             successful_sells['ProfitLoss'])],
                        hovertemplate='%{text}<extra></extra>'
                    ),
                    row=1, col=1
                )

            if not failed_sells.empty:
                # 计算失败卖出信号的位置
                failed_sell_y_positions = []
                for _, signal in failed_sells.iterrows():
                    y_pos = find_closest_kline_price(signal['StartTime'], 'high')
                    failed_sell_y_positions.append(y_pos)

                fig.add_trace(
                    go.Scatter(
                        x=failed_sells['StartTime'],
                        y=failed_sell_y_positions,
                        mode='markers',
                        marker=dict(
                            symbol='triangle-down',
                            size=12,
                            color='#FF9800',
                            opacity=0.7,
                            line=dict(width=1, color='white')
                        ),
                        name='失败卖出信号',
                        text=[f"失败卖出<br>ID: {pid}<br>信心: {conf:.3f}<br>开始价格: ${sp:.2f}<br>结束价格: ${ep:.2f}<br>亏损: ${pl:+.2f}"
                              for pid, conf, sp, ep, pl in zip(failed_sells['PredictionID'],
                                                             failed_sells['Confidence'],
                                                             failed_sells['StartPrice'],
                                                             failed_sells['EndPrice'],
                                                             failed_sells['ProfitLoss'])],
                        hovertemplate='%{text}<extra></extra>'
                    ),
                    row=1, col=1
                )
    
        # 添加交易连接线（限制数量以提高性能）
        successful_trades = backtest_df[backtest_df['Result'] == 1]
        max_lines = 100  # 最多显示100条连接线

        if len(successful_trades) > max_lines:
            # 优先显示盈利最多的交易
            successful_trades = successful_trades.nlargest(max_lines, 'ProfitLoss')

        for _, trade in successful_trades.iterrows():
            fig.add_trace(
                go.Scatter(
                    x=[trade['StartTime'], trade['EndTime']],
                    y=[trade['StartPrice'], trade['EndPrice']],
                    mode='lines',
                    line=dict(
                        color='#4CAF50' if trade['ProfitLoss'] > 0 else '#F44336',
                        width=1,
                        dash='dot'
                    ),
                    showlegend=False,
                    hoverinfo='skip'
                ),
                row=1, col=1
            )

    # 设置布局
    fig.update_layout(
        title=dict(
            text=f'{coin.upper()} {interval} 回测信号可视化',
            x=0.5,
            font=dict(size=20)
        ),
        template='plotly_dark',
        height=1000,
        showlegend=True,
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        ),
        margin=dict(l=50, r=50, t=80, b=50)
    )

    # 隐藏上面两个子图的x轴标签（因为共享x轴）
    fig.update_xaxes(showticklabels=False, row=1, col=1)
    fig.update_xaxes(showticklabels=False, row=2, col=1)

    # 设置y轴格式
    fig.update_yaxes(title_text="价格 ($)", row=1, col=1)
    fig.update_yaxes(title_text="成交量", row=2, col=1)
    fig.update_yaxes(title_text="资金 ($)", row=3, col=1)

    # 添加范围选择器到最底部的x轴
    fig.update_layout(
        xaxis3=dict(
            rangeselector=dict(
                buttons=list([
                    dict(count=1, label="1天", step="day", stepmode="backward"),
                    dict(count=7, label="7天", step="day", stepmode="backward"),
                    dict(count=30, label="30天", step="day", stepmode="backward"),
                    dict(step="all", label="全部")
                ]),
                x=0,
                y=-0.1
            ),
            rangeslider=dict(visible=False),
            type="date",
            title="时间"
        )
    )
    
    # 添加统计信息注释
    if backtest_df is not None and not backtest_df.empty:
        total_trades = len(backtest_df)
        successful_trades = len(backtest_df[backtest_df['Result'] == 1])
        win_rate = successful_trades / total_trades * 100 if total_trades > 0 else 0

        if 'CapitalAfter' in backtest_df.columns:
            initial_capital = backtest_df.iloc[0]['CapitalAfter'] - backtest_df.iloc[0]['ProfitLoss']
            final_capital = backtest_df.iloc[-1]['CapitalAfter']
            total_return = (final_capital - initial_capital) / initial_capital * 100

            stats_text = (
                f"总交易: {total_trades} | "
                f"胜率: {win_rate:.1f}% | "
                f"总收益率: {total_return:+.2f}% | "
                f"初始资金: ${initial_capital:,.0f} | "
                f"最终资金: ${final_capital:,.0f}"
            )
        else:
            total_score = backtest_df['Score'].sum()
            stats_text = (
                f"总交易: {total_trades} | "
                f"胜率: {win_rate:.1f}% | "
                f"总得分: {total_score:+.2f}"
            )

        # 添加统计信息到标题
        fig.update_layout(
            title=dict(
                text=f'{coin.upper()} {interval} 回测信号可视化<br><sub>{stats_text}</sub>',
                x=0.5,
                font=dict(size=18)
            )
        )

    # 保存图表
    if output_file:
        fig.write_html(output_file)
        print(f"📊 交互式图表已保存到: {output_file}")

    return fig

def print_backtest_summary(backtest_df):
    """打印回测摘要统计"""
    if backtest_df is None or backtest_df.empty:
        print("❌ 没有回测数据可分析")
        return

    print("\n" + "="*60)
    print("📊 回测结果摘要")
    print("="*60)

    total_trades = len(backtest_df)
    successful_trades = len(backtest_df[backtest_df['Result'] == 1])
    failed_trades = len(backtest_df[backtest_df['Result'] == 0])
    timeout_trades = len(backtest_df[backtest_df['Result'] == -1])
    stop_loss_trades = len(backtest_df[backtest_df['Result'] == -2])

    win_rate = successful_trades / total_trades * 100 if total_trades > 0 else 0

    print(f"总交易数量: {total_trades}")
    print(f"成功交易: {successful_trades} ({win_rate:.2f}%)")
    print(f"失败交易: {failed_trades}")
    print(f"超时交易: {timeout_trades}")
    print(f"止损交易: {stop_loss_trades}")

    if 'CapitalAfter' in backtest_df.columns:
        initial_capital = backtest_df.iloc[0]['CapitalAfter'] - backtest_df.iloc[0]['ProfitLoss']
        final_capital = backtest_df.iloc[-1]['CapitalAfter']
        total_return = final_capital - initial_capital
        total_return_pct = total_return / initial_capital * 100

        print(f"\n💰 资金情况:")
        print(f"初始资金: ${initial_capital:,.2f}")
        print(f"最终资金: ${final_capital:,.2f}")
        print(f"总收益: ${total_return:+,.2f} ({total_return_pct:+.2f}%)")

        # 最大回撤
        running_max = backtest_df['CapitalAfter'].cummax()
        drawdown = (running_max - backtest_df['CapitalAfter']) / running_max * 100
        max_drawdown = drawdown.max()
        print(f"最大回撤: {max_drawdown:.2f}%")

        # 平均每笔交易
        avg_profit_per_trade = backtest_df['ProfitLoss'].mean()
        print(f"平均每笔收益: ${avg_profit_per_trade:+.2f}")

    # 信心度分析
    if 'Confidence' in backtest_df.columns:
        avg_confidence = backtest_df['Confidence'].mean()
        successful_avg_conf = backtest_df[backtest_df['Result'] == 1]['Confidence'].mean()
        failed_avg_conf = backtest_df[backtest_df['Result'] != 1]['Confidence'].mean()

        print(f"\n🎯 信心度分析:")
        print(f"平均信心度: {avg_confidence:.3f}")
        print(f"成功交易平均信心度: {successful_avg_conf:.3f}")
        print(f"失败交易平均信心度: {failed_avg_conf:.3f}")

    # 预测方向分析
    buy_signals = len(backtest_df[backtest_df['Prediction'] == 1])
    sell_signals = len(backtest_df[backtest_df['Prediction'] == 0])
    buy_success_rate = len(backtest_df[(backtest_df['Prediction'] == 1) & (backtest_df['Result'] == 1)]) / buy_signals * 100 if buy_signals > 0 else 0
    sell_success_rate = len(backtest_df[(backtest_df['Prediction'] == 0) & (backtest_df['Result'] == 1)]) / sell_signals * 100 if sell_signals > 0 else 0

    print(f"\n📈 预测方向分析:")
    print(f"买入信号: {buy_signals} (成功率: {buy_success_rate:.1f}%)")
    print(f"卖出信号: {sell_signals} (成功率: {sell_success_rate:.1f}%)")

    print("="*60)

def main():
    parser = argparse.ArgumentParser(description="TradingView风格的回测信号可视化工具")
    parser.add_argument("--coin", default="ETH", help="交易对，例如 BTC")
    parser.add_argument("--interval", default="15m", help="K线间隔，例如 15m")
    parser.add_argument("--market", default="spot", choices=["spot", "futures"], help="市场类型")
    parser.add_argument("--db", default="coin_data.db", help="SQLite数据库路径")
    parser.add_argument("--log", default="backtest_money_log_quick.csv", help="回测日志文件路径")
    parser.add_argument("--start-time", help="开始时间(北京时间, YYYY-MM-DD HH:MM)")
    parser.add_argument("--end-time", help="结束时间(北京时间, YYYY-MM-DD HH:MM)")
    parser.add_argument("--output", help="输出HTML文件路径")
    parser.add_argument("--show", action='store_true', help="显示图表")
    parser.add_argument("--max-points", type=int, default=5000, help="最大K线数据点数 (默认5000)")
    parser.add_argument("--fast", action='store_true', help="快速模式：更激进的数据采样")
    
    args = parser.parse_args()
    
    # 解析时间
    start_time = parse_time_input(args.start_time) if args.start_time else None
    end_time = parse_time_input(args.end_time) if args.end_time else None
    
    # 加载K线数据
    print("正在加载K线数据...")
    kline_df = load_kline_data(args.db, args.coin, args.interval, args.market, start_time, end_time)
    
    if kline_df is None:
        return
    
    # 加载回测日志
    print("正在加载回测日志...")
    backtest_df = load_backtest_log(args.log)
    
    # 如果有时间范围限制，过滤回测数据
    if backtest_df is not None and (start_time or end_time):
        backtest_df = filter_signals_by_timerange(backtest_df, start_time, end_time)
        print(f"时间过滤后剩余 {len(backtest_df)} 条回测记录")

    # 性能优化设置
    max_points = args.max_points
    if args.fast:
        max_points = min(max_points, 2000)  # 快速模式使用更少的数据点
        print("🚀 启用快速模式，将使用更激进的数据采样")

    # 生成输出文件名
    if not args.output:
        output_file = f"tradingview_{args.coin}_{args.interval}_backtest.html"
    else:
        output_file = args.output

    # 打印回测摘要
    if backtest_df is not None:
        print_backtest_summary(backtest_df)

    # 创建图表
    print("正在生成TradingView风格图表...")
    fig = create_tradingview_chart(kline_df, backtest_df, args.coin, args.interval, output_file, max_points)

    # 显示图表
    if args.show:
        fig.show()

    print("✅ 可视化完成！")
    print(f"💡 提示: 在浏览器中打开 {output_file} 查看交互式图表")

if __name__ == '__main__':
    main()
