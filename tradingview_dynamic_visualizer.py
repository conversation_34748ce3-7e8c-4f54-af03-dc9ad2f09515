#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动态加载TradingView风格回测可视化工具
类似交易所K线图，支持真正的动态数据加载，不丢失任何数据
"""

import pandas as pd
import numpy as np
import sqlite3
import argparse
import os
from datetime import datetime, timedelta
import pytz
import json
import math
from flask import Flask, jsonify, render_template_string, request
import threading
import webbrowser
import time

# 引入现有的工具函数
from get_coin_history import get_table_name

# 北京时区
BEIJING_TZ = pytz.timezone('Asia/Shanghai')

class DynamicDataProvider:
    """动态数据提供者"""
    
    def __init__(self, db_path, coin, interval, market, backtest_log_path):
        self.db_path = db_path
        self.coin = coin
        self.interval = interval
        self.market = market
        self.backtest_log_path = backtest_log_path
        
        # 将币种名称转换为完整的交易对名称
        if coin.upper() in ['BTC', 'ETH', 'DOT', 'ENA', 'LINK', 'SUI', 'UNI']:
            self.symbol = f"{coin.upper()}USDT"
        else:
            self.symbol = coin.upper()
        
        self.table_name = get_table_name(self.symbol, interval, market)
        self.backtest_df = None
        self._load_backtest_data()
        
    def _load_backtest_data(self):
        """加载回测数据"""
        if os.path.exists(self.backtest_log_path):
            df = pd.read_csv(self.backtest_log_path)
            df['StartTime'] = pd.to_datetime(df['StartTimestamp'].str.replace(' UTC+8', '', regex=False))
            df['EndTime'] = pd.to_datetime(df['EndTimestamp'].str.replace(' UTC+8', '', regex=False))
            df['StartTime'] = df['StartTime'].dt.tz_localize('Asia/Shanghai').dt.tz_convert('UTC').dt.tz_localize(None)
            df['EndTime'] = df['EndTime'].dt.tz_localize('Asia/Shanghai').dt.tz_convert('UTC').dt.tz_localize(None)
            self.backtest_df = df
            print(f"✅ 加载了 {len(df)} 条回测记录")
        else:
            print(f"⚠️ 回测日志文件不存在: {self.backtest_log_path}")
    
    def get_kline_data(self, start_time, end_time, limit=1000):
        """获取指定时间范围的K线数据"""
        conn = sqlite3.connect(self.db_path)
        
        query = f"""
        SELECT timestamp, open, high, low, close, volume 
        FROM {self.table_name} 
        WHERE timestamp >= ? AND timestamp <= ?
        ORDER BY timestamp ASC
        LIMIT ?
        """
        
        start_ts = int(start_time.timestamp()) if start_time else 0
        end_ts = int(end_time.timestamp()) if end_time else int(datetime.now().timestamp())
        
        df = pd.read_sql_query(query, conn, params=[start_ts, end_ts, limit])
        conn.close()
        
        if df.empty:
            return []
        
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s', utc=True)
        
        # 转换为前端需要的格式
        kline_data = []
        for _, row in df.iterrows():
            kline_data.append({
                'x': row['timestamp'].isoformat(),
                'open': float(row['open']),
                'high': float(row['high']),
                'low': float(row['low']),
                'close': float(row['close']),
                'volume': float(row['volume'])
            })
        
        return kline_data
    
    def get_signals_data(self, start_time, end_time):
        """获取指定时间范围的交易信号"""
        if self.backtest_df is None:
            return []
        
        # 过滤时间范围
        mask = (self.backtest_df['StartTime'] >= start_time) & (self.backtest_df['StartTime'] <= end_time)
        filtered_df = self.backtest_df[mask]
        
        signals = []
        for _, row in filtered_df.iterrows():
            signal = {
                'id': row['PredictionID'],
                'startTime': row['StartTime'].isoformat(),
                'endTime': row['EndTime'].isoformat(),
                'startPrice': float(row['StartPrice']),
                'endPrice': float(row['EndPrice']),
                'prediction': int(row['Prediction']),
                'result': int(row['Result']),
                'confidence': float(row['Confidence']),
                'profitLoss': float(row['ProfitLoss']),
                'capitalAfter': float(row['CapitalAfter']) if 'CapitalAfter' in row else 0
            }
            signals.append(signal)
        
        return signals
    
    def get_time_range(self):
        """获取数据的时间范围"""
        conn = sqlite3.connect(self.db_path)
        query = f"SELECT MIN(timestamp), MAX(timestamp) FROM {self.table_name}"
        result = conn.execute(query).fetchone()
        conn.close()
        
        if result[0] and result[1]:
            start_time = pd.Timestamp(result[0], unit='s', tz='UTC').tz_localize(None)
            end_time = pd.Timestamp(result[1], unit='s', tz='UTC').tz_localize(None)
            return start_time, end_time
        
        return None, None

# Flask应用
app = Flask(__name__)
data_provider = None

@app.route('/')
def index():
    """主页面"""
    return render_template_string(HTML_TEMPLATE)

@app.route('/api/timerange')
def get_time_range():
    """获取数据时间范围"""
    start_time, end_time = data_provider.get_time_range()
    if start_time and end_time:
        return jsonify({
            'start': start_time.isoformat(),
            'end': end_time.isoformat()
        })
    return jsonify({'error': 'No data found'})

@app.route('/api/kline')
def get_kline():
    """获取K线数据"""
    start = request.args.get('start')
    end = request.args.get('end')
    limit = int(request.args.get('limit', 1000))
    
    start_time = pd.Timestamp(start) if start else None
    end_time = pd.Timestamp(end) if end else None
    
    kline_data = data_provider.get_kline_data(start_time, end_time, limit)
    return jsonify(kline_data)

@app.route('/api/signals')
def get_signals():
    """获取交易信号"""
    start = request.args.get('start')
    end = request.args.get('end')
    
    start_time = pd.Timestamp(start) if start else pd.Timestamp('2020-01-01')
    end_time = pd.Timestamp(end) if end else pd.Timestamp.now()
    
    signals_data = data_provider.get_signals_data(start_time, end_time)
    return jsonify(signals_data)

@app.route('/api/config')
def get_config():
    """获取配置信息"""
    return jsonify({
        'coin': data_provider.coin,
        'interval': data_provider.interval,
        'market': data_provider.market
    })

# HTML模板
HTML_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <title>动态TradingView图表</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body { 
            margin: 0; 
            padding: 20px; 
            background: #1e1e1e; 
            color: white; 
            font-family: Arial, sans-serif;
        }
        .controls {
            margin-bottom: 20px;
            padding: 10px;
            background: #2d2d2d;
            border-radius: 5px;
        }
        .control-group {
            display: inline-block;
            margin-right: 20px;
        }
        label {
            display: inline-block;
            width: 80px;
            margin-right: 10px;
        }
        input, select, button {
            padding: 5px;
            margin-right: 10px;
            background: #3d3d3d;
            color: white;
            border: 1px solid #555;
            border-radius: 3px;
        }
        button {
            background: #4CAF50;
            cursor: pointer;
        }
        button:hover {
            background: #45a049;
        }
        #chart {
            width: 100%;
            height: 800px;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            background: #2d2d2d;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>动态TradingView回测可视化</h1>
    
    <div class="controls">
        <div class="control-group">
            <label>开始时间:</label>
            <input type="datetime-local" id="startTime">
        </div>
        <div class="control-group">
            <label>结束时间:</label>
            <input type="datetime-local" id="endTime">
        </div>
        <div class="control-group">
            <label>数据量:</label>
            <select id="dataLimit">
                <option value="500">500条</option>
                <option value="1000" selected>1000条</option>
                <option value="2000">2000条</option>
                <option value="5000">5000条</option>
            </select>
        </div>
        <button onclick="loadData()">加载数据</button>
        <button onclick="loadFullRange()">加载全部</button>
        <button onclick="autoRefresh()">自动刷新</button>
    </div>
    
    <div id="chart"></div>
    
    <div class="status">
        <div id="status">准备就绪</div>
        <div id="dataInfo"></div>
    </div>

    <script>
        let currentData = {
            kline: [],
            signals: []
        };
        let autoRefreshInterval = null;
        
        // 初始化
        async function init() {
            try {
                const config = await fetch('/api/config').then(r => r.json());
                document.title = `${config.coin} ${config.interval} 动态图表`;
                
                const timeRange = await fetch('/api/timerange').then(r => r.json());
                if (timeRange.start && timeRange.end) {
                    document.getElementById('startTime').value = timeRange.start.slice(0, 16);
                    document.getElementById('endTime').value = timeRange.end.slice(0, 16);
                    
                    // 默认加载最近7天的数据
                    const endTime = new Date(timeRange.end);
                    const startTime = new Date(endTime.getTime() - 7 * 24 * 60 * 60 * 1000);
                    document.getElementById('startTime').value = startTime.toISOString().slice(0, 16);
                    
                    await loadData();
                }
            } catch (error) {
                updateStatus('初始化失败: ' + error.message);
            }
        }
        
        // 加载数据
        async function loadData() {
            const startTime = document.getElementById('startTime').value;
            const endTime = document.getElementById('endTime').value;
            const limit = document.getElementById('dataLimit').value;

            if (!startTime || !endTime) {
                alert('请选择时间范围');
                return;
            }

            updateStatus('正在加载数据...');

            try {
                // 并行加载K线和信号数据
                const [klineData, signalsData] = await Promise.all([
                    fetch(`/api/kline?start=${startTime}&end=${endTime}&limit=${limit}`).then(r => r.json()),
                    fetch(`/api/signals?start=${startTime}&end=${endTime}`).then(r => r.json())
                ]);

                currentData.kline = klineData;
                currentData.signals = signalsData;

                updateChart();
                updateDataInfo(klineData, signalsData);
                updateStatus(`数据加载完成 - K线: ${klineData.length}条, 信号: ${signalsData.length}个`);

            } catch (error) {
                updateStatus('数据加载失败: ' + error.message);
            }
        }

        // 更新数据信息
        function updateDataInfo(klineData, signalsData) {
            if (klineData.length === 0) return;

            const successful = signalsData.filter(s => s.result === 1).length;
            const failed = signalsData.filter(s => s.result === 0).length;
            const timeout = signalsData.filter(s => s.result === -1).length;
            const winRate = signalsData.length > 0 ? (successful / signalsData.length * 100).toFixed(1) : 0;

            const totalProfit = signalsData.reduce((sum, s) => sum + s.profitLoss, 0);

            const info = `
                <strong>数据统计:</strong><br>
                K线数据: ${klineData.length} 条<br>
                交易信号: ${signalsData.length} 个 (成功: ${successful}, 失败: ${failed}, 超时: ${timeout})<br>
                胜率: ${winRate}%<br>
                总盈亏: $${totalProfit.toFixed(2)}
            `;

            document.getElementById('dataInfo').innerHTML = info;
        }
        
        // 加载全部数据范围
        async function loadFullRange() {
            try {
                const timeRange = await fetch('/api/timerange').then(r => r.json());
                if (timeRange.start && timeRange.end) {
                    document.getElementById('startTime').value = timeRange.start.slice(0, 16);
                    document.getElementById('endTime').value = timeRange.end.slice(0, 16);
                    await loadData();
                }
            } catch (error) {
                updateStatus('加载全部数据失败: ' + error.message);
            }
        }
        
        // 自动刷新
        function autoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
                updateStatus('自动刷新已停止');
            } else {
                autoRefreshInterval = setInterval(loadData, 30000); // 30秒刷新一次
                updateStatus('自动刷新已启动 (30秒间隔)');
            }
        }
        
        // 更新图表
        function updateChart() {
            if (currentData.kline.length === 0) {
                updateStatus('没有K线数据');
                return;
            }
            
            // 准备K线数据
            const candlestickData = {
                x: currentData.kline.map(d => d.x),
                open: currentData.kline.map(d => d.open),
                high: currentData.kline.map(d => d.high),
                low: currentData.kline.map(d => d.low),
                close: currentData.kline.map(d => d.close),
                type: 'candlestick',
                name: 'K线',
                increasing: {line: {color: '#26a69a'}},
                decreasing: {line: {color: '#ef5350'}},
                xaxis: 'x',
                yaxis: 'y'
            };
            
            // 准备成交量数据
            const volumeData = {
                x: currentData.kline.map(d => d.x),
                y: currentData.kline.map(d => d.volume),
                type: 'bar',
                name: '成交量',
                marker: {
                    color: currentData.kline.map(d => d.close >= d.open ? '#26a69a' : '#ef5350'),
                    opacity: 0.7
                },
                xaxis: 'x',
                yaxis: 'y2'
            };
            
            const traces = [candlestickData, volumeData];
            
            // 添加交易信号
            if (currentData.signals.length > 0) {
                // 计算价格范围用于信号位置调整
                const prices = currentData.kline.map(d => [d.high, d.low]).flat();
                const priceRange = Math.max(...prices) - Math.min(...prices);
                const offset = priceRange * 0.02; // 2%的偏移量

                // 成功的买入信号 - 显示在K线下方
                const successfulBuys = currentData.signals.filter(s => s.prediction === 1 && s.result === 1);
                if (successfulBuys.length > 0) {
                    traces.push({
                        x: successfulBuys.map(s => s.startTime),
                        y: successfulBuys.map(s => {
                            // 找到对应时间的K线数据
                            const klineData = currentData.kline.find(k =>
                                Math.abs(new Date(k.x) - new Date(s.startTime)) < 60000 * 15 // 15分钟容差
                            );
                            return klineData ? klineData.low - offset : s.startPrice;
                        }),
                        mode: 'markers',
                        type: 'scatter',
                        name: '成功买入',
                        marker: {
                            symbol: 'triangle-up',
                            size: 14,
                            color: '#4CAF50',
                            line: {width: 2, color: 'white'}
                        },
                        text: successfulBuys.map(s => `买入信号<br>ID: ${s.id}<br>价格: $${s.startPrice.toFixed(2)}<br>收益: $${s.profitLoss.toFixed(2)}<br>信心: ${(s.confidence * 100).toFixed(1)}%`),
                        hovertemplate: '%{text}<extra></extra>',
                        xaxis: 'x',
                        yaxis: 'y'
                    });
                }

                // 成功的卖出信号 - 显示在K线上方
                const successfulSells = currentData.signals.filter(s => s.prediction === 0 && s.result === 1);
                if (successfulSells.length > 0) {
                    traces.push({
                        x: successfulSells.map(s => s.startTime),
                        y: successfulSells.map(s => {
                            // 找到对应时间的K线数据
                            const klineData = currentData.kline.find(k =>
                                Math.abs(new Date(k.x) - new Date(s.startTime)) < 60000 * 15 // 15分钟容差
                            );
                            return klineData ? klineData.high + offset : s.startPrice;
                        }),
                        mode: 'markers',
                        type: 'scatter',
                        name: '成功卖出',
                        marker: {
                            symbol: 'triangle-down',
                            size: 14,
                            color: '#F44336',
                            line: {width: 2, color: 'white'}
                        },
                        text: successfulSells.map(s => `卖出信号<br>ID: ${s.id}<br>价格: $${s.startPrice.toFixed(2)}<br>收益: $${s.profitLoss.toFixed(2)}<br>信心: ${(s.confidence * 100).toFixed(1)}%`),
                        hovertemplate: '%{text}<extra></extra>',
                        xaxis: 'x',
                        yaxis: 'y'
                    });
                }

                // 失败的信号 - 用较小的标记显示
                const failedSignals = currentData.signals.filter(s => s.result !== 1);
                if (failedSignals.length > 0) {
                    const failedBuys = failedSignals.filter(s => s.prediction === 1);
                    const failedSells = failedSignals.filter(s => s.prediction === 0);

                    if (failedBuys.length > 0) {
                        traces.push({
                            x: failedBuys.map(s => s.startTime),
                            y: failedBuys.map(s => {
                                const klineData = currentData.kline.find(k =>
                                    Math.abs(new Date(k.x) - new Date(s.startTime)) < 60000 * 15
                                );
                                return klineData ? klineData.low - offset : s.startPrice;
                            }),
                            mode: 'markers',
                            type: 'scatter',
                            name: '失败买入',
                            marker: {
                                symbol: 'triangle-up',
                                size: 10,
                                color: '#FFC107',
                                opacity: 0.7,
                                line: {width: 1, color: 'white'}
                            },
                            text: failedBuys.map(s => `失败买入<br>ID: ${s.id}<br>价格: $${s.startPrice.toFixed(2)}<br>亏损: $${s.profitLoss.toFixed(2)}`),
                            hovertemplate: '%{text}<extra></extra>',
                            xaxis: 'x',
                            yaxis: 'y'
                        });
                    }

                    if (failedSells.length > 0) {
                        traces.push({
                            x: failedSells.map(s => s.startTime),
                            y: failedSells.map(s => {
                                const klineData = currentData.kline.find(k =>
                                    Math.abs(new Date(k.x) - new Date(s.startTime)) < 60000 * 15
                                );
                                return klineData ? klineData.high + offset : s.startPrice;
                            }),
                            mode: 'markers',
                            type: 'scatter',
                            name: '失败卖出',
                            marker: {
                                symbol: 'triangle-down',
                                size: 10,
                                color: '#FF9800',
                                opacity: 0.7,
                                line: {width: 1, color: 'white'}
                            },
                            text: failedSells.map(s => `失败卖出<br>ID: ${s.id}<br>价格: $${s.startPrice.toFixed(2)}<br>亏损: $${s.profitLoss.toFixed(2)}`),
                            hovertemplate: '%{text}<extra></extra>',
                            xaxis: 'x',
                            yaxis: 'y'
                        });
                    }
                }
            }
            
            // 布局配置
            const layout = {
                title: '动态TradingView回测图表',
                template: 'plotly_dark',
                xaxis: {
                    domain: [0, 1],
                    rangeslider: {visible: false},
                    type: 'date'
                },
                yaxis: {
                    domain: [0.3, 1],
                    title: '价格'
                },
                yaxis2: {
                    domain: [0, 0.25],
                    title: '成交量'
                },
                showlegend: true,
                legend: {
                    orientation: 'h',
                    y: 1.02
                },
                margin: {l: 50, r: 50, t: 80, b: 50}
            };
            
            // 绘制图表
            Plotly.newPlot('chart', traces, layout, {responsive: true});

            // 添加缩放事件监听器
            document.getElementById('chart').on('plotly_relayout', function(eventData) {
                if (eventData['xaxis.range[0]'] && eventData['xaxis.range[1]']) {
                    const startTime = new Date(eventData['xaxis.range[0]']).toISOString().slice(0, 16);
                    const endTime = new Date(eventData['xaxis.range[1]']).toISOString().slice(0, 16);

                    // 检查是否需要加载更多数据
                    const currentStart = new Date(currentData.kline[0]?.x);
                    const currentEnd = new Date(currentData.kline[currentData.kline.length - 1]?.x);
                    const zoomStart = new Date(eventData['xaxis.range[0]']);
                    const zoomEnd = new Date(eventData['xaxis.range[1]']);

                    // 如果缩放范围超出当前数据范围，自动加载更多数据
                    if (zoomStart < currentStart || zoomEnd > currentEnd) {
                        document.getElementById('startTime').value = startTime;
                        document.getElementById('endTime').value = endTime;
                        loadData();
                    }
                }
            });
        }
        
        // 更新状态
        function updateStatus(message) {
            document.getElementById('status').textContent = message;
            console.log(message);
        }
        
        // 页面加载完成后初始化
        window.onload = init;
    </script>
</body>
</html>
'''

def start_server(host='127.0.0.1', port=5000, debug=False):
    """启动Flask服务器"""
    app.run(host=host, port=port, debug=debug, threaded=True)

def main():
    global data_provider
    
    parser = argparse.ArgumentParser(description="动态加载TradingView风格回测可视化工具")
    parser.add_argument("--coin", default="ETH", help="交易对，例如 BTC")
    parser.add_argument("--interval", default="15m", help="K线间隔，例如 15m")
    parser.add_argument("--market", default="spot", choices=["spot", "futures"], help="市场类型")
    parser.add_argument("--db", default="coin_data.db", help="SQLite数据库路径")
    parser.add_argument("--log", default="backtest_money_log_quick.csv", help="回测日志文件路径")
    parser.add_argument("--port", type=int, default=5000, help="Web服务器端口")
    parser.add_argument("--host", default="127.0.0.1", help="Web服务器地址")
    parser.add_argument("--no-browser", action='store_true', help="不自动打开浏览器")
    
    args = parser.parse_args()
    
    # 初始化数据提供者
    print("正在初始化动态数据提供者...")
    data_provider = DynamicDataProvider(args.db, args.coin, args.interval, args.market, args.log)
    
    # 启动Web服务器
    print(f"正在启动Web服务器 http://{args.host}:{args.port}")
    
    if not args.no_browser:
        # 延迟打开浏览器
        def open_browser():
            time.sleep(1.5)
            webbrowser.open(f'http://{args.host}:{args.port}')
        
        threading.Thread(target=open_browser, daemon=True).start()
    
    try:
        start_server(args.host, args.port, debug=False)
    except KeyboardInterrupt:
        print("\n服务器已停止")

if __name__ == '__main__':
    main()
