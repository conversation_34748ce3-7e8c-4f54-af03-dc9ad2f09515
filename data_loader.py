# data_loader.py
# 公共数据加载模块 - 提取公共的数据加载逻辑

from typing import Optional, Dict, Any
from data_providers import create_data_provider, DataProvider
from model_utils import get_coin_config
import pandas as pd


def parse_time_string(time_str: str) -> pd.Timestamp:
    """
    解析时间字符串为pandas Timestamp

    Args:
        time_str: 时间字符串，支持格式:
                 - '2024-01-01'
                 - '2024-01-01 12:00:00'
                 - '01-01' (当年)
                 - '01-01 12:00' (当年)

    Returns:
        pandas Timestamp对象
    """
    import datetime
    current_year = datetime.datetime.now().year

    # 如果只有月-日格式，添加当前年份
    if len(time_str.split('-')) == 2 and not time_str.startswith('20'):
        time_str = f"{current_year}-{time_str}"

    # 如果只有日期没有时间，添加默认时间
    if ' ' not in time_str:
        time_str = f"{time_str} 00:00:00"
    elif len(time_str.split(' ')[1].split(':')) == 2:
        # 如果只有小时:分钟，添加秒
        time_str = f"{time_str}:00"

    return pd.to_datetime(time_str)


def create_data_source_config(coin_name: str, db_path: Optional[str] = None, 
                             symbol: Optional[str] = None, interval: Optional[str] = None,
                             market: str = 'spot') -> Dict[str, Any]:
    """
    根据币种名称和参数创建数据源配置
    
    Args:
        coin_name: 币种名称 (如 'ETH')
        db_path: SQLite数据库路径，如果为None则从配置文件读取
        symbol: 交易对符号，如果为None则从配置文件读取
        interval: 时间间隔，如果为None则从配置文件读取
        market: 市场类型 ('spot' 或 'futures')
    
    Returns:
        数据源配置字典
    """
    # 获取币种配置
    coin_config = get_coin_config(coin_name)
    if coin_config is None:
        raise ValueError(f"未找到币种 '{coin_name}' 的配置")
    
    # 设置默认值
    db_path = db_path or coin_config.get('db_path', 'coin_data.db')
    symbol = symbol or coin_config.get('api_symbol', f"{coin_name}USDT")
    interval = interval or f"{coin_config['timeframe_minutes']}m"
    
    data_source = {
        'type': 'sqlite',
        'db_path': db_path,
        'symbol': symbol,
        'interval': interval,
        'market': market
    }
    
    # 可选的时间范围参数
    if 'start_time' in coin_config:
        data_source['start_time'] = coin_config['start_time']
    if 'end_time' in coin_config:
        data_source['end_time'] = coin_config['end_time']
    
    return data_source


def load_data_for_training(coin_name: str, db_path: Optional[str] = None,
                          symbol: Optional[str] = None, interval: Optional[str] = None,
                          market: str = 'spot', initial_count: int = 1000000,
                          start_time: Optional[str] = None, end_time: Optional[str] = None) -> Optional[pd.DataFrame]:
    """
    为训练加载数据的统一接口

    Args:
        coin_name: 币种名称
        db_path: SQLite数据库路径
        symbol: 交易对符号
        interval: 时间间隔
        market: 市场类型
        initial_count: 初始加载的数据条数
        start_time: 开始时间字符串，格式: '2024-01-01' 或 '2024-01-01 12:00:00'
        end_time: 结束时间字符串，格式: '2024-12-31' 或 '2024-12-31 23:59:59'

    Returns:
        加载的DataFrame或None
    """
    try:
        # 获取币种配置
        coin_config = get_coin_config(coin_name)
        if coin_config is None:
            return None
        
        # 创建数据源配置
        data_source = create_data_source_config(coin_name, db_path, symbol, interval, market)
        
        # 创建数据提供者
        price_multiplier = coin_config.get('price_multiplier', 1.0)
        data_provider = create_data_provider(data_source, price_multiplier)
        
        # 加载数据
        df = data_provider.get_initial_data(initial_count=initial_count)
        if df is None or len(df) == 0:
            print("❌ 无法加载数据")
            return None

        # 应用时间过滤
        if start_time is not None or end_time is not None:
            original_count = len(df)

            if start_time is not None:
                start_ts = parse_time_string(start_time)
                df = df[df.index >= start_ts]
                print(f"📅 应用开始时间过滤: {start_time} -> {len(df)} 条记录")

            if end_time is not None:
                end_ts = parse_time_string(end_time)
                df = df[df.index <= end_ts]
                print(f"📅 应用结束时间过滤: {end_time} -> {len(df)} 条记录")

            if len(df) == 0:
                print("❌ 时间过滤后没有数据")
                return None

            print(f"📊 时间过滤: {original_count} -> {len(df)} 条记录")
            if len(df) > 0:
                print(f"📅 最终时间范围: {df.index[0]} 到 {df.index[-1]}")

        return df
        
    except Exception as e:
        print(f"❌ 加载数据时出错: {e}")
        return None


def load_data_for_backtest(coin_name: str, db_path: Optional[str] = None,
                          symbol: Optional[str] = None, interval: Optional[str] = None,
                          market: str = 'spot') -> Optional[DataProvider]:
    """
    为回测加载数据提供者的统一接口
    
    Args:
        coin_name: 币种名称
        db_path: SQLite数据库路径
        symbol: 交易对符号
        interval: 时间间隔
        market: 市场类型
    
    Returns:
        数据提供者实例或None
    """
    try:
        # 获取币种配置
        coin_config = get_coin_config(coin_name)
        if coin_config is None:
            return None
        
        # 创建数据源配置
        data_source = create_data_source_config(coin_name, db_path, symbol, interval, market)
        
        # 创建数据提供者
        price_multiplier = coin_config.get('price_multiplier', 1.0)
        data_provider = create_data_provider(data_source, price_multiplier)
        
        return data_provider
        
    except Exception as e:
        print(f"❌ 创建数据提供者时出错: {e}")
        return None


def print_data_source_info(coin_name: str, data_source: Dict[str, Any]):
    """
    打印数据源信息的统一接口
    
    Args:
        coin_name: 币种名称
        data_source: 数据源配置
    """
    coin_config = get_coin_config(coin_name)
    if coin_config:
        print(f"=== {coin_config['display_name']} 数据源信息 ===")
    else:
        print(f"=== {coin_name} 数据源信息 ===")
    
    print(f"数据源类型: SQLite")
    print(f"数据库: {data_source['db_path']}")
    print(f"表: {data_source['symbol']}_{data_source['interval']}_{data_source['market']}")
    
    if coin_config:
        print(f"时间周期: {coin_config['timeframe_minutes']}分钟")
        if coin_config.get('price_multiplier', 1.0) != 1.0:
            print(f"价格缩放: {coin_config['price_multiplier']}x")
