#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TradingView可视化工具使用示例
演示如何使用tradingview_backtest_visualizer.py来可视化回测结果
"""

import subprocess
import sys
import os
from datetime import datetime, timedelta

def run_visualization_example():
    """运行可视化示例"""
    
    print("🚀 TradingView回测可视化工具使用示例")
    print("="*50)
    
    # 检查必要文件是否存在
    required_files = [
        "tradingview_backtest_visualizer.py",
        "coin_data.db",
        "backtest_money_log_quick.csv"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file in missing_files:
            print(f"   - {file}")
        print("\n请确保:")
        print("1. 已运行 backtest_money_quick.py 生成回测日志")
        print("2. 数据库文件 coin_data.db 存在")
        return
    
    # 示例1: 基本可视化
    print("\n📊 示例1: 基本可视化 (最近7天)")
    end_time = datetime.now()
    start_time = end_time - timedelta(days=7)
    
    cmd1 = [
        sys.executable, "tradingview_backtest_visualizer.py",
        "--coin", "ETH",
        "--interval", "15m",
        "--start-time", start_time.strftime("%Y-%m-%d"),
        "--end-time", end_time.strftime("%Y-%m-%d"),
        "--output", "tradingview_eth_15m_7days.html"
    ]
    
    print(f"运行命令: {' '.join(cmd1)}")
    try:
        result = subprocess.run(cmd1, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 基本可视化完成")
        else:
            print(f"❌ 错误: {result.stderr}")
    except Exception as e:
        print(f"❌ 运行失败: {e}")
    
    # 示例2: 完整数据可视化
    print("\n📊 示例2: 完整数据可视化")
    
    cmd2 = [
        sys.executable, "tradingview_backtest_visualizer.py",
        "--coin", "ETH",
        "--interval", "15m",
        "--output", "tradingview_eth_15m_full.html"
    ]
    
    print(f"运行命令: {' '.join(cmd2)}")
    try:
        result = subprocess.run(cmd2, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 完整数据可视化完成")
        else:
            print(f"❌ 错误: {result.stderr}")
    except Exception as e:
        print(f"❌ 运行失败: {e}")
    
    # 示例3: 高性能模式（大数据量）
    print("\n📊 示例3: 高性能模式（适合大数据量）")

    cmd3 = [
        sys.executable, "tradingview_fast_visualizer.py",
        "--coin", "ETH",
        "--interval", "15m",
        "--max-days", "60",
        "--output", "tradingview_eth_15m_fast.html"
    ]

    print(f"运行命令: {' '.join(cmd3)}")

    try:
        result = subprocess.run(cmd3, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 高性能模式完成")
        else:
            print(f"❌ 错误: {result.stderr}")
    except Exception as e:
        print(f"❌ 运行失败: {e}")

    # 示例4: 快速模式
    print("\n📊 示例4: 快速模式（最大性能优化）")

    cmd4 = [
        sys.executable, "tradingview_backtest_visualizer.py",
        "--coin", "ETH",
        "--interval", "15m",
        "--start-time", (end_time - timedelta(days=7)).strftime("%Y-%m-%d"),
        "--fast",
        "--max-points", "1000",
        "--output", "tradingview_eth_15m_ultra_fast.html"
    ]

    print(f"运行命令: {' '.join(cmd4)}")

    try:
        result = subprocess.run(cmd4, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 快速模式完成")
        else:
            print(f"❌ 错误: {result.stderr}")
    except Exception as e:
        print(f"❌ 运行失败: {e}")
    
    print("\n" + "="*50)
    print("📋 使用说明:")
    print("1. 生成的HTML文件可以在任何现代浏览器中打开")
    print("2. 图表支持缩放、平移和悬停查看详细信息")
    print("3. 可以使用时间范围选择器快速跳转到不同时间段")
    print("4. 绿色三角形表示成功的买入信号，红色三角形表示成功的卖出信号")
    print("5. 黄色/橙色标记表示失败的交易信号")
    print("6. 虚线连接交易的开始和结束点")

    print("\n⚡ 性能优化模式:")
    print("• 标准模式: tradingview_backtest_visualizer.py (完整功能)")
    print("• 快速模式: 添加 --fast 参数 (激进采样)")
    print("• 高性能模式: tradingview_fast_visualizer.py (专为大数据优化)")
    print("• 数据量建议:")
    print("  - 小于1万条K线: 使用标准模式")
    print("  - 1-5万条K线: 使用快速模式")
    print("  - 大于5万条K线: 使用高性能模式")

    print("\n🎯 命令行参数说明:")
    print("--coin: 交易对 (如 ETH, BTC)")
    print("--interval: K线间隔 (如 5m, 15m, 1h)")
    print("--market: 市场类型 (spot 或 futures)")
    print("--db: 数据库文件路径")
    print("--log: 回测日志文件路径")
    print("--start-time: 开始时间 (YYYY-MM-DD HH:MM)")
    print("--end-time: 结束时间 (YYYY-MM-DD HH:MM)")
    print("--output: 输出HTML文件路径")
    print("--show: 生成后自动在浏览器中显示")
    print("--max-points: 最大K线数据点数 (默认5000)")
    print("--fast: 启用快速模式")
    print("--max-days: 最大数据天数 (高性能模式)")
    print("--max-signals: 最大信号数量 (高性能模式)")

def main():
    """主函数"""
    run_visualization_example()

if __name__ == '__main__':
    main()
