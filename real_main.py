# main.py

import argparse
import os
import sys
import logging
from datetime import datetime
import json
import pytz

# Assume these utility functions are in a shared file.
from model_utils_815 import get_coin_config, get_output_dir

# Import the three core modules with their new names.
from real_data import MarketDataFetcher
from real_signal import SignalGenerator
from real_manager import PortfolioManager

def setup_logging(log_file: str, coin_name: str) -> logging.Logger:
    """Sets up the logger to output to both console and file."""
    logger = logging.getLogger(f'{coin_name}Predictor')
    logger.setLevel(logging.INFO)
    logger.propagate = False
    if logger.hasHandlers():
        logger.handlers.clear()
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    
    # File Handler
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    # Console Handler
    stream_handler = logging.StreamHandler(sys.stdout)
    stream_handler.setFormatter(formatter)
    logger.addHandler(stream_handler)
    return logger

def main():
    parser = argparse.ArgumentParser(description="分层式多币种实时预测器")
    parser.add_argument("--coin", default="DOT", help="币种名称 (如: DOT, SEI)")
    parser.add_argument("--update-interval", type=int, default=60, help="数据获取和预测的更新间隔（秒）")
    parser.add_argument("--no-sound", action="store_true", help="禁用报警声")
    args = parser.parse_args()

    try:
        # --- 1. Initialize configuration and logging ---
        coin_name = args.coin.upper()
        coin_config = get_coin_config(coin_name)
        if coin_config is None:
            raise ValueError(f"无法获取币种 {coin_name} 的配置")

        local_tz = pytz.timezone('Asia/Shanghai')
        output_dir = get_output_dir()
        model_file = os.path.join(output_dir, f"{coin_config['model_basename']}_model.joblib")
        config_file = os.path.join(output_dir, f"{coin_config['model_basename']}_config.json")
        log_file = os.path.join(output_dir, f"{coin_name.lower()}_prediction_log.txt")

        if not os.path.exists(model_file) or not os.path.exists(config_file):
            print(f"❌ 错误: 模型文件 '{model_file}' 或配置文件 '{config_file}' 不存在。")
            return

        logger = setup_logging(log_file, coin_name)
        
        # Log initial setup information
        logger.info("="*20 + f" {coin_config['display_name']} 预测器启动 (简化版) " + "="*20)
        logger.info(f"币种: {coin_config['display_name']}")
        logger.info(f"API符号: {coin_config['api_symbol']}")
        logger.info(f"模型文件: {model_file}")
        logger.info(f"配置文件: {config_file}")
        logger.info(f"日志文件: {log_file}")
        logger.info(f"更新间隔: {args.update_interval}秒")
        logger.info(f"报警声: {'启用' if not args.no_sound else '禁用'}")
        logger.info("按 Ctrl+C 停止预测\n")
        
        with open(config_file, 'r') as f:
            loaded_config = json.load(f)

        # --- 2. Instantiate the three modules, passing necessary info ---
        portfolio_mgr = PortfolioManager(
            config=loaded_config, logger=logger, display_name=coin_config['display_name'],
            local_tz=local_tz, enable_sound=not args.no_sound
        )
        
        signal_gen = SignalGenerator(
            model_file=model_file, config_file=config_file, logger=logger,
            display_name=coin_config['display_name'], local_tz=local_tz
        )
        
        market_fetcher = MarketDataFetcher(
            api_symbol=coin_config['api_symbol'], timeframe_minutes=coin_config['timeframe_minutes'],
            update_interval=args.update_interval, logger=logger,
            display_name=coin_config['display_name'], local_tz=local_tz
        )

        # --- 3. Register callbacks to connect the modules ---
        signal_gen.register_handlers(
            signal_handler=portfolio_mgr.on_new_signal,
            price_update_handler=portfolio_mgr.on_price_update
        )
        market_fetcher.register_kline_handler(
            handler=signal_gen.on_new_kline_data
        )

        # --- 4. Start the program ---
        logger.info("启动主预测循环...")
        market_fetcher.run()

    except (ValueError, RuntimeError) as e:
        print(f"❌ 启动错误: {e}")
        return
    finally:
        if 'portfolio_mgr' in locals():
            print("\n" + "="*20 + " 实时预测总结 " + "="*20)
            portfolio_mgr.print_status()
            logger.info("[系统] 预测器已停止")
        print("程序已退出。")

if __name__ == "__main__":
    main()