#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动态加载可视化工具使用示例
演示如何使用真正的动态加载方案，零数据丢失
"""

import subprocess
import sys
import os
import time
import threading
import webbrowser
from datetime import datetime

def check_requirements():
    """检查必要的依赖和文件"""
    print("🔍 检查系统要求...")
    
    required_files = [
        "tradingview_dynamic_visualizer.py",
        "coin_data.db",
        "backtest_money_log_quick.csv"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    # 检查Flask是否安装
    try:
        import flask
        print("✅ Flask已安装")
    except ImportError:
        print("❌ Flask未安装，请运行: uv add flask")
        return False
    
    print("✅ 系统要求检查通过")
    return True

def start_dynamic_server(coin="ETH", interval="15m", port=5000):
    """启动动态加载服务器"""
    print(f"🚀 启动动态加载服务器 - {coin} {interval}")
    print(f"📡 服务器地址: http://127.0.0.1:{port}")
    
    cmd = [
        sys.executable, "tradingview_dynamic_visualizer.py",
        "--coin", coin,
        "--interval", interval,
        "--port", str(port),
        "--no-browser"  # 我们手动控制浏览器
    ]
    
    try:
        process = subprocess.Popen(
            cmd, 
            stdout=subprocess.PIPE, 
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        # 等待服务器启动
        print("⏳ 等待服务器启动...")
        time.sleep(3)
        
        if process.poll() is None:
            print("✅ 服务器启动成功")
            return process, port
        else:
            stdout, stderr = process.communicate()
            print(f"❌ 服务器启动失败:")
            print(f"stdout: {stdout}")
            print(f"stderr: {stderr}")
            return None, None
            
    except Exception as e:
        print(f"❌ 启动服务器时出错: {e}")
        return None, None

def open_browser_delayed(url, delay=2):
    """延迟打开浏览器"""
    def open_browser():
        time.sleep(delay)
        print(f"🌐 打开浏览器: {url}")
        webbrowser.open(url)
    
    threading.Thread(target=open_browser, daemon=True).start()

def demo_single_coin():
    """演示单币种动态加载"""
    print("\n" + "="*60)
    print("📊 演示1: 单币种动态加载 (ETH 15分钟)")
    print("="*60)
    
    process, port = start_dynamic_server("ETH", "15m", 5000)
    if not process:
        return None
    
    url = f"http://127.0.0.1:{port}"
    open_browser_delayed(url, 2)
    
    print(f"""
🎯 动态加载功能演示:

1. 📅 时间范围选择
   - 默认显示最近7天数据
   - 可以选择任意时间范围
   - 支持精确到分钟的时间选择

2. 📊 数据量控制
   - 500条: 快速加载，适合概览
   - 1000条: 平衡模式，推荐使用
   - 2000条: 详细模式，适合深入分析
   - 5000条: 最大模式，适合大屏显示

3. 🔍 智能缩放
   - 缩放图表时自动加载更多数据
   - 无需手动调整时间范围
   - 保持数据的完整性

4. 🔄 实时刷新
   - 点击"自动刷新"启用30秒自动更新
   - 适合监控最新的交易信号

5. 📈 交易信号
   - 绿色三角形: 成功的买入信号
   - 红色三角形: 成功的卖出信号
   - 悬停查看详细信息

💡 操作建议:
- 先点击"加载全部"查看完整时间范围
- 然后选择感兴趣的时间段进行详细分析
- 使用鼠标滚轮缩放查看更多细节
""")
    
    return process

def demo_multi_timeframe():
    """演示多时间框架分析"""
    print("\n" + "="*60)
    print("📊 演示2: 多时间框架动态分析")
    print("="*60)
    
    timeframes = [
        ("5m", 5001, "短期分析"),
        ("15m", 5002, "中期分析"), 
        ("1h", 5003, "长期分析")
    ]
    
    processes = []
    
    for interval, port, description in timeframes:
        print(f"\n🚀 启动 {description} - ETH {interval}")
        process, actual_port = start_dynamic_server("ETH", interval, port)
        if process:
            processes.append((process, actual_port, interval, description))
            url = f"http://127.0.0.1:{actual_port}"
            open_browser_delayed(url, len(processes) * 2)
    
    if processes:
        print(f"""
🎯 多时间框架分析演示:

已启动 {len(processes)} 个动态服务器:
""")
        for _, port, interval, desc in processes:
            print(f"- {desc} (ETH {interval}): http://127.0.0.1:{port}")
        
        print(f"""
💡 分析建议:
1. 🔍 从长期图表(1h)开始，识别主要趋势
2. 📊 使用中期图表(15m)确认信号质量
3. ⚡ 在短期图表(5m)寻找精确的入场点

🎨 对比分析:
- 观察同一时间段在不同时间框架下的表现
- 比较各时间框架的信号密度和成功率
- 识别跨时间框架的信号确认模式
""")
    
    return processes

def demo_performance_comparison():
    """演示性能对比"""
    print("\n" + "="*60)
    print("⚡ 演示3: 性能对比 - 动态加载 vs 静态采样")
    print("="*60)
    
    print("""
🏁 性能对比测试:

1. 📊 静态采样方案 (传统方法)
   - 一次性加载所有数据
   - 对大数据进行采样处理
   - 可能丢失重要的价格波动

2. 🚀 动态加载方案 (新方法)
   - 按需加载数据
   - 保留所有原始数据
   - 智能缓存和自动扩展

测试场景: ETH 15分钟数据，包含大量交易信号
""")
    
    # 启动动态加载版本
    print("\n🚀 启动动态加载版本...")
    dynamic_process, dynamic_port = start_dynamic_server("ETH", "15m", 5010)
    
    if dynamic_process:
        dynamic_url = f"http://127.0.0.1:{dynamic_port}"
        open_browser_delayed(dynamic_url, 2)
        
        print(f"""
✅ 动态加载版本已启动: {dynamic_url}

🎯 性能测试步骤:
1. 点击"加载全部"按钮
2. 观察初始加载时间
3. 尝试缩放和平移操作
4. 注意响应速度和流畅度

📊 预期结果:
- 初始加载: < 3秒
- 缩放响应: < 0.5秒  
- 内存占用: < 100MB
- 数据完整性: 100%

💡 对比要点:
- 加载速度: 动态加载明显更快
- 内存使用: 动态加载显著更少
- 数据完整性: 动态加载保留所有细节
- 用户体验: 动态加载更流畅
""")
        
        return [dynamic_process]
    
    return []

def interactive_demo():
    """交互式演示"""
    print("\n" + "="*60)
    print("🎮 交互式演示菜单")
    print("="*60)
    
    while True:
        print("""
请选择演示模式:

1. 📊 单币种动态加载演示
2. 📈 多时间框架分析演示  
3. ⚡ 性能对比演示
4. 🔧 自定义配置演示
5. ❌ 退出演示

""")
        
        choice = input("请输入选择 (1-5): ").strip()
        
        if choice == "1":
            process = demo_single_coin()
            if process:
                input("\n按回车键停止服务器并继续...")
                process.terminate()
        
        elif choice == "2":
            processes = demo_multi_timeframe()
            if processes:
                input("\n按回车键停止所有服务器并继续...")
                for process, _, _, _ in processes:
                    process.terminate()
        
        elif choice == "3":
            processes = demo_performance_comparison()
            if processes:
                input("\n按回车键停止服务器并继续...")
                for process in processes:
                    process.terminate()
        
        elif choice == "4":
            custom_demo()
        
        elif choice == "5":
            print("👋 感谢使用动态加载可视化演示！")
            break
        
        else:
            print("❌ 无效选择，请重新输入")

def custom_demo():
    """自定义配置演示"""
    print("\n🔧 自定义配置演示")
    
    # 获取用户输入
    coin = input("请输入币种 (默认ETH): ").strip().upper() or "ETH"
    interval = input("请输入时间间隔 (默认15m): ").strip() or "15m"
    port = input("请输入端口号 (默认5020): ").strip()
    
    try:
        port = int(port) if port else 5020
    except ValueError:
        port = 5020
        print("⚠️ 端口号无效，使用默认端口5020")
    
    print(f"\n🚀 启动自定义配置: {coin} {interval} 端口{port}")
    
    process, actual_port = start_dynamic_server(coin, interval, port)
    if process:
        url = f"http://127.0.0.1:{actual_port}"
        open_browser_delayed(url, 2)
        print(f"✅ 自定义服务器已启动: {url}")
        input("\n按回车键停止服务器...")
        process.terminate()

def main():
    """主函数"""
    print("🚀 动态加载TradingView可视化工具演示")
    print("="*60)
    print("这个演示将展示真正的动态加载方案，零数据丢失！")
    
    # 检查系统要求
    if not check_requirements():
        print("\n❌ 系统要求检查失败，请解决上述问题后重试")
        return
    
    print(f"""
🎯 演示内容:
1. 真正的动态数据加载 (类似交易所K线图)
2. 零数据丢失的高性能方案
3. 多时间框架分析
4. 智能缓存和自动扩展
5. 专业的交易信号可视化

⚡ 核心优势:
- 📊 保留所有原始数据，无采样损失
- 🚀 按需加载，极致性能
- 🔄 智能缓存，流畅体验
- 🎨 专业界面，符合交易者习惯

💡 使用提示:
- 服务器启动后会自动打开浏览器
- 可以同时运行多个服务器实例
- 按Ctrl+C可以停止服务器
""")
    
    # 启动交互式演示
    interactive_demo()

if __name__ == '__main__':
    main()
