import pandas as pd
import numpy as np
from datetime import datetime, timedelta, timezone
import lightgbm as lgb
from sklearn.calibration import CalibratedClassifierCV
from sklearn.model_selection import TimeSeriesSplit, ParameterGrid
from sklearn.metrics import roc_auc_score, accuracy_score
import joblib
import json
import argparse
import os
import pickle
from model_utils import calculate_features, get_coin_config, get_output_dir, get_feature_list
from data_loader import load_data_for_training, create_data_source_config, print_data_source_info

# --- 配置将从命令行参数和配置文件中获取 ---
# 全局变量，将在main函数中根据币种配置设置
TIMEFRAME_MINUTES = None
UP_THRESHOLD = None
DOWN_THRESHOLD = None
MAX_LOOKFORWARD_MINUTES = None
DATA_SOURCE = None  # 新增：数据源配置
MODEL_BASENAME = None
PRICE_MULTIPLIER = None

# 获取北京时间字符串
def now_beijing_str():
    return ""
    # utc_now = datetime.utcnow().replace(tzinfo=timezone.utc)
    # beijing_now = utc_now.astimezone(timezone(timedelta(hours=8)))
    # return beijing_now.strftime('%Y-%m-%d %H:%M:%S')

def create_percentage_target(df, up_threshold, down_threshold, max_lookforward_minutes, timeframe):
    """
    创建新的目标标签：判断在指定时间内是先涨 X% 还是先跌 Y%
    """
    max_lookforward_candles = max_lookforward_minutes // timeframe

    print(f"创建目标标签：先涨{up_threshold*100:.1f}%还是先跌{down_threshold*100:.1f}%")
    print(f"最大前瞻时间：{max_lookforward_minutes}分钟 ({max_lookforward_candles}根 {timeframe}-min K线)")

    labels = []
    valid_indices = []

    for i in range(len(df)):
        if i % 10000 == 0:
            print(f"处理进度: {i}/{len(df)} ({i/len(df)*100:.1f}%) [{now_beijing_str()}]")

        current_price = df.iloc[i]['close']
        up_target = current_price * (1 + up_threshold)
        down_target = current_price * (1 - down_threshold)

        label = None
        for j in range(1, min(max_lookforward_candles + 1, len(df) - i)):
            future_price = df.iloc[i + j]['close']
            if future_price >= up_target:
                label = 1
                break
            elif future_price <= down_target:
                label = 0
                break

        if label is not None:
            labels.append(label)
            valid_indices.append(i)

    print(f"有效标签数量: {len(labels)}/{len(df)} ({len(labels)/len(df)*100:.1f}%)")

    label_series = pd.Series(index=df.index[valid_indices], data=labels)
    if len(labels) > 0:
        up_count = sum(labels)
        down_count = len(labels) - up_count
        print(f"标签分布: 先涨 = {up_count} ({up_count/len(labels)*100:.1f}%), 先跌 = {down_count} ({down_count/len(labels)*100:.1f}%)")
    else:
        print("未生成任何有效标签。")

    return label_series

def prepare_features_and_labels(df, db_path='coin_data.db', symbol='ETHUSDT', market='spot'):
    """
    修正后的准备特征和标签的函数。
    先在完整数据上计算特征，再合并标签并筛选。
    """
    # 1. 在完整的、连续的原始数据上计算特征
    print("在完整数据集上计算增强特征...")
    # df_with_features = calculate_features(df.copy(), timeframe=TIMEFRAME_MINUTES,
    #                                     db_path=db_path, symbol=symbol, market=market)
    df_with_features = calculate_features(df.copy(), timeframe=TIMEFRAME_MINUTES)
    # 2. 仍然在原始的、连续的数据上创建标签
    print("创建目标标签...")
    target_labels = create_percentage_target(df, UP_THRESHOLD, DOWN_THRESHOLD, MAX_LOOKFORWARD_MINUTES, TIMEFRAME_MINUTES)

    # 3. 将带有完整特征的数据集与标签Series合并。
    #    - 使用 .rename() 给标签Series一个列名
    #    - 使用 .join() 和 how='inner' 会自动保留两者索引重合的行，
    #      即只保留那些既成功计算出特征、又拥有有效标签的行。
    print("合并特征与标签...")
    df_combined = df_with_features.join(target_labels.rename('label'), how='inner')

    # 4. 清理特征计算早期产生的NaN值。
    #    因为join已经是'inner'，所以dropna只会处理特征NaN。
    df_clean = df_combined.dropna()

    print(f"清理NaN后剩余 {len(df_clean)} 条记录")
    return df_clean

def split_data(df_clean):
    train_size = int(len(df_clean) * 0.70)
    val_size = int(len(df_clean) * 0.15)
    train_df = df_clean.iloc[:train_size]
    val_df = df_clean.iloc[train_size:train_size + val_size]
    test_df = df_clean.iloc[train_size + val_size:]
    print(f"训练集: {len(train_df)} ({train_df.index.min()} to {train_df.index.max()}) [{now_beijing_str()}]")
    print(f"验证集: {len(val_df)} ({val_df.index.min()} to {val_df.index.max()}) [{now_beijing_str()}]")
    print(f"测试集: {len(test_df)} ({test_df.index.min()} to {test_df.index.max()}) [{now_beijing_str()}]")
    return train_df, val_df, test_df

def time_series_cross_validation(df_clean, features, target='label', n_splits=5):
    """
    使用时序交叉验证评估模型性能和选择最佳超参数
    """
    print(f"\n=== 开始时序交叉验证 (n_splits={n_splits}) === [{now_beijing_str()}]")

    # 定义超参数搜索空间
    param_grid = {
        'n_estimators': [1000, 1500, 2000],
        'learning_rate': [0.03, 0.05, 0.08],
        'max_depth': [6, 8, 10],
        'num_leaves': [31, 63, 127],
        'min_child_samples': [20, 50, 100],
        'subsample': [0.8, 0.9, 1.0],
        'colsample_bytree': [0.8, 0.9, 1.0]
    }

    # 创建时序分割器
    tscv = TimeSeriesSplit(n_splits=n_splits)

    X = df_clean[features]
    y = df_clean[target]

    best_score = -np.inf
    best_params = None
    cv_results = []

    print(f"搜索空间大小: {len(list(ParameterGrid(param_grid)))} 种参数组合")
    print("开始网格搜索...")

    # 网格搜索
    for i, params in enumerate(ParameterGrid(param_grid)):
        if i % 10 == 0:
            print(f"进度: {i}/{len(list(ParameterGrid(param_grid)))} [{now_beijing_str()}]")

        fold_scores = []
        fold_aucs = []

        # 时序交叉验证
        for fold, (train_idx, val_idx) in enumerate(tscv.split(X)):
            X_train_fold, X_val_fold = X.iloc[train_idx], X.iloc[val_idx]
            y_train_fold, y_val_fold = y.iloc[train_idx], y.iloc[val_idx]

            # 检查训练集和验证集是否都包含两个类别
            train_classes = set(y_train_fold.unique())
            val_classes = set(y_val_fold.unique())

            # 如果训练集或验证集只有一个类别，跳过这个fold
            if len(train_classes) < 2 or len(val_classes) < 2:
                print(f"  Fold {fold+1}: 跳过（类别不足）- 训练集类别: {train_classes}, 验证集类别: {val_classes}")
                fold_scores.append(-np.inf)  # 给一个很低的分数
                fold_aucs.append(0.5)  # AUC默认值
                continue

            # 检查是否有验证集中的类别在训练集中不存在
            if not val_classes.issubset(train_classes):
                print(f"  Fold {fold+1}: 跳过（验证集包含训练集未见过的类别）")
                fold_scores.append(-np.inf)
                fold_aucs.append(0.5)
                continue

            try:
                # 训练模型
                lgbm = lgb.LGBMClassifier(
                    objective='binary',
                    metric='auc',
                    random_state=42,
                    verbose=-1,
                    **params
                )

                lgbm.fit(
                    X_train_fold, y_train_fold,
                    eval_set=[(X_val_fold, y_val_fold)],
                    eval_metric='auc',
                    callbacks=[lgb.early_stopping(stopping_rounds=50, verbose=False)]
                )

                # 预测和评估
                y_pred_proba = lgbm.predict_proba(X_val_fold)[:, 1]

                # 计算AUC，处理只有一个类别的情况
                try:
                    auc_score = roc_auc_score(y_val_fold, y_pred_proba)
                except ValueError:
                    # 如果只有一个类别，AUC无法计算
                    auc_score = 0.5

            except Exception as e:
                print(f"  Fold {fold+1}: 训练失败 - {str(e)}")
                fold_scores.append(-np.inf)
                fold_aucs.append(0.5)
                continue

            # 计算自定义得分（基于阈值的交易得分）
            thresholds_to_test = np.arange(0.52, 0.80, 0.02)
            best_fold_score = -np.inf

            for threshold in thresholds_to_test:
                predictions = np.where(y_pred_proba > threshold, 1,
                                     np.where(y_pred_proba < (1 - threshold), 0, -1))
                correct_trades = (predictions == y_val_fold) & (predictions != -1)
                incorrect_trades = (predictions != y_val_fold) & (predictions != -1)
                current_score = correct_trades.sum() - incorrect_trades.sum()

                if current_score > best_fold_score:
                    best_fold_score = current_score

            fold_scores.append(best_fold_score)
            fold_aucs.append(auc_score)

        # 计算平均得分，排除失败的fold
        valid_scores = [s for s in fold_scores if s != -np.inf]
        valid_aucs = [a for a in fold_aucs if not np.isnan(a)]

        if len(valid_scores) > 0:
            mean_score = np.mean(valid_scores)
            std_score = np.std(valid_scores)
        else:
            mean_score = -np.inf
            std_score = 0

        if len(valid_aucs) > 0:
            mean_auc = np.mean(valid_aucs)
        else:
            mean_auc = 0.5

        cv_results.append({
            'params': params,
            'mean_score': mean_score,
            'std_score': std_score,
            'mean_auc': mean_auc,
            'fold_scores': fold_scores,
            'fold_aucs': fold_aucs,
            'valid_folds': len(valid_scores)
        })

        # 更新最佳参数（只考虑有效的结果）
        if mean_score > best_score and len(valid_scores) > 0:
            best_score = mean_score
            best_params = params

    print(f"\n=== 时序交叉验证完成 === [{now_beijing_str()}]")

    if best_params is None:
        print("⚠️  警告: 未找到有效的参数组合，将使用默认参数")
        best_params = {
            'n_estimators': 1000,
            'learning_rate': 0.05,
            'max_depth': 6,
            'num_leaves': 31,
            'min_child_samples': 20,
            'subsample': 0.8,
            'colsample_bytree': 0.8
        }
        best_score = -np.inf
    else:
        print(f"最佳平均得分: {best_score:.2f}")
        print(f"最佳参数: {best_params}")

    # 保存交叉验证结果
    cv_results_file = f'cv_results_{MODEL_BASENAME}.json'
    output_dir = get_output_dir()
    if output_dir:
        cv_results_file = os.path.join(output_dir, cv_results_file)

    # 转换numpy类型为Python原生类型以便JSON序列化
    cv_results_serializable = []
    for result in cv_results:
        result_copy = result.copy()
        # 处理可能的无穷大值
        fold_scores = []
        for x in result['fold_scores']:
            if x == -np.inf:
                fold_scores.append(-999999)  # 用一个很大的负数代替
            else:
                fold_scores.append(float(x))

        result_copy['fold_scores'] = fold_scores
        result_copy['fold_aucs'] = [float(x) for x in result['fold_aucs']]

        # 处理可能的无穷大值
        if result['mean_score'] == -np.inf:
            result_copy['mean_score'] = -999999
        else:
            result_copy['mean_score'] = float(result['mean_score'])

        result_copy['std_score'] = float(result['std_score'])
        result_copy['mean_auc'] = float(result['mean_auc'])
        result_copy['valid_folds'] = result['valid_folds']
        cv_results_serializable.append(result_copy)

    with open(cv_results_file, 'w') as f:
        json.dump(cv_results_serializable, f, indent=2)

    print(f"交叉验证结果已保存到: {cv_results_file}")

    return best_params, best_score, cv_results

def save_processed_data(df_clean, train_df, val_df, test_df, features, data_file):
    print(f"保存预处理数据到 {data_file}... [{now_beijing_str()}]")
    data_dict = {'df_clean': df_clean, 'train_df': train_df, 'val_df': val_df, 'test_df': test_df, 'features': features, 'save_time': now_beijing_str()}
    with open(data_file, 'wb') as f: pickle.dump(data_dict, f)
    print(f"数据已保存到 {data_file}")

def load_processed_data(data_file):
    print(f"从 {data_file} 加载预处理数据...")
    if not os.path.exists(data_file):
        print(f"错误：数据文件 {data_file} 不存在！"); return None, None, None, None, None
    with open(data_file, 'rb') as f: data_dict = pickle.load(f)
    print(f"数据加载完成，保存时间: {data_dict.get('save_time', 'Unknown')} (北京时间)")
    return data_dict['df_clean'], data_dict['train_df'], data_dict['val_df'], data_dict['test_df'], data_dict['features']



def train_model(save_data=False, load_data=False, data_file=f'{MODEL_BASENAME}.pkl',
                coin_name=None, db_path=None, symbol=None, interval=None, market='spot',
                start_time=None, end_time=None, use_time_series_cv=True, cv_splits=5):
    print(f"开始训练LightGBM模型 - {MODEL_BASENAME.replace('_', ' ').title()} [{now_beijing_str()}]")

    # 获取币种配置
    from model_utils import get_coin_config
    coin_config = get_coin_config(coin_name)

    if load_data:
        df_clean, train_df, val_df, test_df, features = load_processed_data(data_file)
        if df_clean is None: load_data = False
    if not load_data:
        # 使用公共数据加载模块加载数据
        df = load_data_for_training(coin_name, db_path, symbol, interval, market, start_time=start_time, end_time=end_time)
        if df is None:
            print("❌ 数据加载失败，退出训练")
            return
        # 确保符号参数正确
        actual_symbol = symbol or (coin_config.get('api_symbol', f"{coin_name}USDT") if coin_config else f"{coin_name}USDT")
        df_clean = prepare_features_and_labels(df, db_path, actual_symbol, market)
        train_df, val_df, test_df = split_data(df_clean)
        features = get_feature_list(df_clean)
        if save_data:
            save_processed_data(df_clean, train_df, val_df, test_df, features, data_file)

    target = 'label'

    # 使用时序交叉验证进行超参数优化
    if use_time_series_cv:
        print(f"\n🔍 使用时序交叉验证进行超参数优化...")
        # 使用训练集+验证集进行交叉验证
        train_val_df = pd.concat([train_df, val_df])
        best_params, best_cv_score, cv_results = time_series_cross_validation(
            train_val_df, features, target, n_splits=cv_splits
        )

        print(f"\n✅ 时序交叉验证完成，使用最佳参数训练最终模型...")
        print(f"最佳参数: {best_params}")
        print(f"交叉验证得分: {best_cv_score:.2f}")

        # 使用最佳参数训练最终模型
        X_train, y_train = train_df[features], train_df[target]
        X_val, y_val = val_df[features], val_df[target]
        X_test, y_test = test_df[features], test_df[target]

        lgbm = lgb.LGBMClassifier(
            objective='binary',
            metric='auc',
            n_jobs=-1,
            random_state=42,
            verbose=-1,
            **best_params
        )
    else:
        print(f"\n⚠️  使用默认参数训练模型（未启用时序交叉验证）...")
        X_train, y_train = train_df[features], train_df[target]
        X_val, y_val = val_df[features], val_df[target]
        X_test, y_test = test_df[features], test_df[target]

        lgbm = lgb.LGBMClassifier(
            objective='binary',
            metric='auc',
            n_estimators=2000,
            learning_rate=0.05,
            n_jobs=-1,
            random_state=42,
            verbose=-1
        )

    print(f"开始训练LightGBM模型... [{now_beijing_str()}]")
    lgbm.fit(X_train, y_train, eval_set=[(X_val, y_val)], eval_metric='auc',
             callbacks=[lgb.early_stopping(stopping_rounds=100, verbose=False)])

    print(f"基础模型训练完成，开始概率校准... [{now_beijing_str()}]")
    calibrated_model = CalibratedClassifierCV(lgbm, method='isotonic', cv='prefit')
    calibrated_model.fit(X_val, y_val)

    print(f"概率校准完成，开始阈值优化... [{now_beijing_str()}]")
    val_probabilities = calibrated_model.predict_proba(X_val)[:, 1]
    thresholds_to_test = np.arange(0.52, 0.80, 0.01)
    best_score, best_threshold = -np.inf, 0.5
    for threshold in thresholds_to_test:
        predictions = np.where(val_probabilities > threshold, 1, np.where(val_probabilities < (1 - threshold), 0, -1))
        correct_trades = (predictions == y_val) & (predictions != -1)
        incorrect_trades = (predictions != y_val) & (predictions != -1)
        current_score = correct_trades.sum() - incorrect_trades.sum()
        if current_score > best_score:
            best_score, best_threshold = current_score, threshold

    print(f"验证集上的最优得分: {best_score:.0f}, 最优信心阈值: {best_threshold:.3f} [{now_beijing_str()}]")
    evaluate_on_test_set(calibrated_model, X_test, y_test, test_df, best_threshold)

    print(f"最终信心阈值: {best_threshold:.3f}")

    # 保存模型配置时包含交叉验证信息
    extra_config = {}
    if use_time_series_cv:
        extra_config.update({
            'time_series_cv_used': True,
            'cv_splits': cv_splits,
            'best_cv_score': float(best_cv_score),
            'best_cv_params': best_params
        })
    else:
        extra_config['time_series_cv_used'] = False

    save_model_and_config(calibrated_model, features, best_threshold, len(X_train), len(X_val), len(X_test), extra_config)
    analyze_feature_importance(lgbm, features)

# ... 其余函数保持不变 ...
def evaluate_on_test_set(model, X_test, y_test, test_df, best_threshold):
    """
    在测试集上评估模型, 并将详细结果保存到CSV文件。
    """
    # best_threshold=0.7
    print(f"\n--- 测试集评估 (使用阈值: {best_threshold:.3f}) --- [{now_beijing_str()}]")

    test_probabilities = model.predict_proba(X_test)[:, 1]

    # 用于保存每一行详细结果的列表
    results_log = []

    for i in range(len(test_probabilities)):
        prob = test_probabilities[i]
        actual_result = y_test.iloc[i]
        current_price = test_df.iloc[i]['close']
        timestamp = test_df.index[i]

        guess = -1  # -1 代表放弃
        action_str = "放弃"
        result_str = "-"
        score_for_this_trade = 0

        if prob > best_threshold:
            guess = 1
            action_str = "\033[92m猜先涨\033[0m"
        elif prob < (1 - best_threshold):
            guess = 0
            action_str = "\033[91m猜先跌\033[0m"

        if guess != -1:
            if guess == actual_result:
                score_for_this_trade = 1
                result_str = "成功✅"
            else:
                score_for_this_trade = -1
                result_str = "失败❌"

        # 转换为北京时间字符串
        if isinstance(timestamp, pd.Timestamp):
            if timestamp.tzinfo is None:
                # 假设为UTC
                timestamp = timestamp.tz_localize('UTC')
            beijing_time = timestamp.tz_convert('Asia/Shanghai').strftime('%Y-%m-%d %H:%M:%S')
        else:
            # 兼容datetime或str
            beijing_time = str(timestamp)

        # 创建日志条目
        log_entry = {
            'BeijingTime': beijing_time,
            'Timestamp': timestamp,
            'ClosePrice': current_price,
            'ConfidenceUp': prob,
            'Action': action_str.replace('\033[92m', '').replace('\033[91m', '').replace('\033[0m', ''), # 清除颜色代码
            'Prediction': guess, # 1=涨, 0=跌, -1=放弃
            'ActualResult': actual_result,
            'Outcome': result_str,
            'Score': score_for_this_trade
        }
        results_log.append(log_entry)

    # 将日志转换为DataFrame
    results_df = pd.DataFrame(results_log)

    # 调整列顺序
    col_order = ['BeijingTime', 'Timestamp', 'ClosePrice', 'ConfidenceUp', 'Action', 'Prediction', 'ActualResult', 'Outcome', 'Score']
    results_df = results_df[col_order]

    # 计算最终统计数据
    trades_made = (results_df['Prediction'] != -1).sum()
    wins = (results_df['Score'] == 1).sum()
    losses = (results_df['Score'] == -1).sum()
    test_score = wins - losses

    # 打印最终评估结果
    print(f"总样本数: {len(test_df)} [{now_beijing_str()}]")
    print(f"猜测次数: {trades_made} (占比: {trades_made/len(test_df)*100:.2f}%) [{now_beijing_str()}]")
    if trades_made > 0:
        print(f"胜率: {wins/trades_made*100:.2f}% [{now_beijing_str()}]")
    print(f"总得分: {test_score:+d} [{now_beijing_str()}]")

    # 保存到CSV文件
    results_filename = f'test_results_{MODEL_BASENAME}.csv'
    output_dir = get_output_dir()
    if output_dir:
        results_filename = os.path.join(output_dir, results_filename)
    try:
        results_df.to_csv(results_filename, index=False, float_format='%.4f')
        print(f"\n详细测试结果已保存到文件: {results_filename} [{now_beijing_str()}]")
    except Exception as e:
        print(f"\n保存测试结果到CSV时出错: {e}")


def save_model_and_config(model, features, best_threshold, train_size, val_size, test_size, extra_config=None):
    print(f"\n保存模型和配置... [{now_beijing_str()}]")
    model_file = f'{MODEL_BASENAME}_model.joblib'
    config_file = f'{MODEL_BASENAME}_config.json'
    output_dir = get_output_dir()
    if output_dir:
        model_file = os.path.join(output_dir, model_file)
        config_file = os.path.join(output_dir, config_file)
    joblib.dump(model, model_file)
    config = {
        'best_threshold': best_threshold, 'feature_list': features,
        'model_type': f'LGBM_{MODEL_BASENAME}',
        'target_description': f'predict_first_{UP_THRESHOLD*100}%_move_within_{MAX_LOOKFORWARD_MINUTES}_minutes',
        'training_date': now_beijing_str(),
        'train_size': train_size, 'val_size': val_size, 'test_size': test_size,
        'up_threshold': UP_THRESHOLD, 'down_threshold': DOWN_THRESHOLD,
        'max_lookforward_minutes': MAX_LOOKFORWARD_MINUTES, 'timeframe_minutes': TIMEFRAME_MINUTES
    }

    # 添加额外配置信息
    if extra_config:
        config.update(extra_config)

    with open(config_file, 'w') as f: json.dump(config, f, indent=2)
    print(f"模型文件: {model_file}\n配置文件: {config_file} [{now_beijing_str()}]")

def analyze_feature_importance(lgbm, features):
    print("\n" + "="*60 + f"\n特征重要性分析 (北京时间: {now_beijing_str()})\n" + "="*60)
    importance_df = pd.DataFrame({'feature': features, 'importance': lgbm.feature_importances_}).sort_values('importance', ascending=False)
    print(importance_df.head(20).to_string(index=False))
    importance_file = f'feature_importance_{MODEL_BASENAME}.csv'
    output_dir = get_output_dir()
    if output_dir:
        importance_file = os.path.join(output_dir, importance_file)
    importance_df.to_csv(importance_file, index=False)
    print(f"\n特征重要性已保存到 {importance_file} [{now_beijing_str()}]")


def validate_model(model_file, config_file, load_data=False, data_file=f'{MODEL_BASENAME}.pkl',
                  coin_name=None, db_path=None, symbol=None, interval=None, market='spot',
                  start_time=None, end_time=None):
    print(f"加载模型: {model_file}\n加载配置: {config_file}")
    if not os.path.exists(model_file) or not os.path.exists(config_file):
        print(f"错误：模型或配置文件不存在！"); return
    model = joblib.load(model_file)
    with open(config_file, 'r') as f: config = json.load(f)
    print(f"模型类型: {config.get('model_type', 'Unknown')}, 训练日期: {config.get('training_date', 'Unknown')}")

    # 获取币种配置
    from model_utils import get_coin_config
    coin_config = get_coin_config(coin_name)

    if load_data:
        df_clean, _, _, test_df, _ = load_processed_data(data_file)
        if df_clean is None: load_data = False
    if not load_data:
        # 使用公共数据加载模块加载数据
        df = load_data_for_training(coin_name, db_path, symbol, interval, market, start_time=start_time, end_time=end_time)
        if df is None:
            print("❌ 数据加载失败，退出验证")
            return
        # 确保符号参数正确
        actual_symbol = symbol or (coin_config.get('api_symbol', f"{coin_name}USDT") if coin_config else f"{coin_name}USDT")
        df_clean = prepare_features_and_labels(df, db_path, actual_symbol, market)
        _, _, test_df = split_data(df_clean)
    features = config.get('feature_list', [])
    missing_features = [f for f in features if f not in df_clean.columns]
    if missing_features:
        print(f"错误：以下特征在数据中不存在: {missing_features}"); return
    target = 'label'
    X_test, y_test = test_df[features], test_df[target]
    print(f"测试集大小: {len(X_test)}")
    evaluate_on_test_set(model, X_test, y_test, test_df, config.get('best_threshold', 0.5))
    print("\n验证完成！")

def main():
    parser = argparse.ArgumentParser(description="多币种 LGBM 模型训练器")
    parser.add_argument("--coin", default="ETH", help="币种名称 (如: DOT, SEI)")
    parser.add_argument("--mode", choices=['train', 'validate'], default='train', help="运行模式")
    parser.add_argument("--model-file", help="模型文件路径 - 可选，会根据币种自动生成")
    parser.add_argument("--config-file", help="配置文件路径 - 可选，会根据币种自动生成")
    parser.add_argument("--save-data", action='store_true', help="保存预处理后的数据")
    parser.add_argument("--load-data", action='store_true', help="加载预处理后的数据")
    parser.add_argument("--data-file", help="预处理数据文件路径 - 可选，会根据币种自动生成")

    # 时序交叉验证参数
    parser.add_argument("--use-time-series-cv", action='store_true', default=True,
                       help="使用时序交叉验证进行超参数优化 (默认启用)")
    parser.add_argument("--no-time-series-cv", action='store_true',
                       help="禁用时序交叉验证，使用默认参数")
    parser.add_argument("--cv-splits", type=int, default=5,
                       help="时序交叉验证的分割数量 (默认: 5)")

    # SQLite数据源参数
    parser.add_argument("--db-path", help="SQLite数据库路径", default='coin_data.db')
    parser.add_argument("--symbol", help="交易对符号")
    parser.add_argument("--interval", help="时间间隔")
    parser.add_argument("--market", choices=['spot', 'futures'], default='spot', help="市场类型")

    # 时间范围参数
    parser.add_argument("--start-time", help="训练数据开始时间，格式: 2024-01-01 或 2024-01-01 12:00:00")
    parser.add_argument("--end-time", help="训练数据结束时间，格式: 2024-12-31 或 2024-12-31 23:59:59")

    args = parser.parse_args()

    # 获取币种配置
    coin_config = get_coin_config(args.coin)
    if coin_config is None:
        exit(1)

    # 设置全局变量
    global TIMEFRAME_MINUTES, UP_THRESHOLD, DOWN_THRESHOLD, MAX_LOOKFORWARD_MINUTES, DATA_SOURCE, MODEL_BASENAME, PRICE_MULTIPLIER
    TIMEFRAME_MINUTES = coin_config['timeframe_minutes']
    UP_THRESHOLD = coin_config['up_threshold']
    DOWN_THRESHOLD = coin_config['down_threshold']
    MAX_LOOKFORWARD_MINUTES = coin_config['max_lookforward_minutes']
    MODEL_BASENAME = coin_config['model_basename']
    PRICE_MULTIPLIER = coin_config.get('price_multiplier', 1.0)

    # 创建数据源配置
    data_source = create_data_source_config(args.coin, args.db_path, args.symbol, args.interval, args.market)

    # 设置默认文件路径
    model_file = args.model_file or f"{MODEL_BASENAME}_model.joblib"
    config_file = args.config_file or f"{MODEL_BASENAME}_config.json"
    data_file = args.data_file or f"{MODEL_BASENAME}.pkl"

    # 处理时序交叉验证参数
    use_time_series_cv = args.use_time_series_cv and not args.no_time_series_cv

    # 打印训练信息
    print(f"=== {coin_config['display_name']} 模型训练 ===")
    print_data_source_info(args.coin, data_source)
    print(f"目标涨幅: {UP_THRESHOLD*100:.1f}%")
    print(f"目标跌幅: {DOWN_THRESHOLD*100:.1f}%")
    print(f"最大等待: {MAX_LOOKFORWARD_MINUTES}分钟")
    print(f"模型基名: {MODEL_BASENAME}")
    if args.mode == 'train':
        print(f"时序交叉验证: {'启用' if use_time_series_cv else '禁用'}")
        if use_time_series_cv:
            print(f"交叉验证分割数: {args.cv_splits}")
    print()

    if args.mode == 'train':
        train_model(args.save_data, args.load_data, data_file, args.coin, args.db_path,
                   args.symbol, args.interval, args.market, args.start_time, args.end_time,
                   use_time_series_cv, args.cv_splits)
    else:
        validate_model(model_file, config_file, args.load_data, data_file, args.coin,
                      args.db_path, args.symbol, args.interval, args.market, args.start_time, args.end_time)

if __name__ == '__main__':
    main()