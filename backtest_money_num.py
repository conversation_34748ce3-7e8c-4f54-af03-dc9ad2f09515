# backtest_money_num_sqlite.py
# 使用已保存的模型，对SQLite中的历史数据进行带资金管理的真实环境模拟回测
# 支持 --start-time 和 --end-time 过滤数据
# (已根据用户建议优化止损逻辑)

import pandas as pd
import numpy as np
import joblib
import json
import argparse
import os
import sqlite3
from datetime import datetime, timedelta
import pytz
from typing import Dict, List

# 引入 get_coin_history 中的表名生成工具
from get_coin_history import get_table_name

try:
    from model_utils import calculate_features, get_coin_config, get_output_dir
    from analyze_money import analyze_and_plot_results
except ImportError:
    print("错误: 请确保 model_utils.py 和 analyze_money.py 文件存在。")
    def calculate_features(df, timeframe): print("计算特征..."); return df
    def get_coin_config(coin): print(f"获取 {coin} 配置..."); return {'model_basename': f"{coin}_model"}
    def get_output_dir(basename): os.makedirs('./output', exist_ok=True); return './output'
    def analyze_and_plot_results(df, basename): print(f"分析结果并绘图: {basename}")

# 北京时区
BEIJING_TZ = pytz.timezone('Asia/Shanghai')

def to_beijing_time(timestamp):
    if isinstance(timestamp, pd.Timestamp):
        if timestamp.tz is None:
            timestamp = timestamp.tz_localize('UTC')
        return timestamp.tz_convert(BEIJING_TZ)
    elif isinstance(timestamp, datetime):
        if timestamp.tzinfo is None:
            timestamp = pytz.UTC.localize(timestamp)
        return timestamp.astimezone(BEIJING_TZ)
    return timestamp

def format_beijing_time(timestamp):
    beijing_time = to_beijing_time(timestamp)
    return beijing_time.strftime('%Y-%m-%d %H:%M:%S UTC+8')

def parse_time_input(time_str):
    if not time_str:
        return None
    try:
        formats = ['%Y-%m-%d %H:%M:%S', '%Y-%m-%d %H:%M', '%Y-%m-%d', '%m-%d %H:%M', '%m-%d']
        dt = None
        for fmt in formats:
            try:
                dt = datetime.strptime(time_str, fmt)
                break
            except ValueError:
                continue
        if dt is None:
            raise ValueError(f"无法解析时间格式: {time_str}")
        if dt.year == 1900:
            dt = dt.replace(year=datetime.now().year)
        beijing_dt = BEIJING_TZ.localize(dt)
        utc_dt = beijing_dt.astimezone(pytz.UTC)
        return pd.Timestamp(utc_dt).tz_localize(None)
    except Exception as e:
        print(f"时间解析错误: {e}")
        return None

def load_data_from_sqlite(db_path, coin, interval, market, price_multiplier=1.0, start_time=None, end_time=None):
    """从SQLite读取数据"""
    table_name = get_table_name(coin, interval, market)
    conn = sqlite3.connect(db_path)
    query = f"SELECT timestamp, open, high, low, close, volume FROM {table_name}"
    conditions = []
    params = []
    if start_time:
        conditions.append("timestamp >= ?")
        params.append(int(start_time.timestamp()))
    if end_time:
        conditions.append("timestamp <= ?")
        params.append(int(end_time.timestamp()))
    if conditions:
        query += " WHERE " + " AND ".join(conditions)
    query += " ORDER BY timestamp ASC"
    df = pd.read_sql_query(query, conn, params=params)
    conn.close()
    if df.empty:
        print("❌ 数据库中无符合条件的数据")
        return None
    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s', utc=True)
    df.set_index('timestamp', inplace=True)
    df = df.astype(float)
    if price_multiplier != 1.0:
        df[['open', 'high', 'low', 'close']] *= price_multiplier
    return df

class HistoricalBacktester:
    def __init__(self, model_file: str, config_file: str, initial_capital: float, risk_per_trade_pct: float, price_multiplier: float = 1.0, stop_loss_pct: float = None):
        self.model_file = model_file
        self.config_file = config_file
        self.price_multiplier = price_multiplier
        self.stop_loss_pct = stop_loss_pct
        self.model = joblib.load(model_file)
        with open(config_file, 'r') as f:
            self.config = json.load(f)

        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.risk_per_trade_pct = risk_per_trade_pct / 100.0

        self.active_predictions: Dict[str, dict] = {}
        self.completed_predictions: List[dict] = []
        self.prediction_counter = 0

        self.total_predictions = 0
        self.successful_predictions = 0
        self.failed_predictions = 0
        self.timeout_predictions = 0
        self.stop_loss_predictions = 0

        print(f"回测器初始化完成")
        print(f"模型阈值: {self.config['best_threshold']:.3f}, 最大等待: {self.config['max_lookforward_minutes']}分钟")
        if stop_loss_pct is not None:
            print(f"止损触发条件: {stop_loss_pct:.1f}%")
        print(f"初始资金: ${initial_capital:,.2f}")
        print(f"单次风险比例: {risk_per_trade_pct:.2f}%")

    def make_prediction(self, current_window_df: pd.DataFrame) -> tuple:
        try:
            features_df = calculate_features(current_window_df, timeframe=self.config['timeframe_minutes'])
            features_df_clean = features_df.dropna()
            if features_df_clean.empty:
                return None, 0.0, 0.0
            feature_list = self.config['feature_list']
            latest_features_series = features_df_clean.iloc[-1]
            if any(f not in latest_features_series.index for f in feature_list):
                return None, 0.0, 0.0
            latest_features_df = latest_features_series[feature_list].to_frame().T
            current_price = latest_features_series['close']
            probability = self.model.predict_proba(latest_features_df)[0, 1]
            best_threshold = self.config['best_threshold']
            guess = None
            if probability > best_threshold:
                guess = 1
            elif probability < (1 - best_threshold):
                guess = 0
            return guess, probability, current_price
        except Exception as e:
            print(f"预测时发生错误: {e}")
            return None, 0.0, 0.0

    def add_prediction(self, guess: int, probability: float, price: float, timestamp: pd.Timestamp, current_idx: int):
        self.prediction_counter += 1
        prediction_id = f"pred_{self.prediction_counter:06d}"
        trade_risk_capital = self.current_capital * self.risk_per_trade_pct
        max_wait_candles = self.config['max_lookforward_minutes'] // self.config['timeframe_minutes']
        prediction = {
            'id': prediction_id, 'guess': guess, 'probability': probability,
            'start_price': price, 'start_timestamp': timestamp,
            'start_idx': current_idx, 'expire_idx': current_idx + max_wait_candles,
            'up_target': price * (1 + self.config['up_threshold']),
            'down_target': price * (1 - self.config['down_threshold']),
            'status': 'active', 'trade_risk_capital': trade_risk_capital,
            'max_loss_pct': 0.0, 'max_loss_price': price, 'max_loss_timestamp': timestamp
        }
        self.active_predictions[prediction_id] = prediction
        self.total_predictions += 1
        direction_str = f"先涨{self.config['up_threshold']*100:.1f}%" if guess == 1 else f"先跌{self.config['down_threshold']*100:.1f}%"
        print(f"[{format_beijing_time(timestamp)}] 新预测: {direction_str}, 信心: {probability:.3f}, 价格: {price:.4f}, 风险暴露: ${trade_risk_capital:,.2f}")

    def check_predictions(self, current_price: float, current_timestamp: pd.Timestamp, current_idx: int):
        completed_ids = []
        for pred_id, pred in self.active_predictions.items():
            if pred['status'] != 'active':
                continue
            current_price_change_pct = (current_price - pred['start_price']) / pred['start_price'] * 100
            if (pred['guess'] == 1 and current_price_change_pct < 0) or (pred['guess'] == 0 and current_price_change_pct > 0):
                loss_pct = abs(current_price_change_pct)
                if loss_pct > pred['max_loss_pct']:
                    pred.update({'max_loss_pct': loss_pct, 'max_loss_price': current_price, 'max_loss_timestamp': current_timestamp})
            if self.stop_loss_pct is not None:
                should_stop_loss = (pred['guess'] == 1 and current_price_change_pct < -self.stop_loss_pct) or \
                                   (pred['guess'] == 0 and current_price_change_pct > self.stop_loss_pct)
                if should_stop_loss:
                    reason = f"止损(触发点:{-self.stop_loss_pct:.1f}%)"
                    self.complete_prediction(pred_id, -2, current_price, current_timestamp, current_idx, reason)
                    completed_ids.append(pred_id)
                    continue
            if current_price >= pred['up_target']:
                result, reason = (1, "达到上涨目标") if pred['guess'] == 1 else (0, "达到上涨目标(预测错误)")
                self.complete_prediction(pred_id, result, current_price, current_timestamp, current_idx, reason)
                completed_ids.append(pred_id)
            elif current_price <= pred['down_target']:
                result, reason = (1, "达到下跌目标") if pred['guess'] == 0 else (0, "达到下跌目标(预测错误)")
                self.complete_prediction(pred_id, result, current_price, current_timestamp, current_idx, reason)
                completed_ids.append(pred_id)
            elif current_idx >= pred['expire_idx']:
                self.complete_prediction(pred_id, -1, current_price, current_timestamp, current_idx, "超时")
                completed_ids.append(pred_id)
        for pred_id in completed_ids:
            if pred_id in self.active_predictions:
                del self.active_predictions[pred_id]

    def calculate_score(self, prediction: int, start_price: float, end_price: float, result: int):
        price_change_pct = (end_price - start_price) / start_price
        threshold = self.config.get('up_threshold', 0.01)
        if result == 1: return 1.0
        if result == 0: return -1.0
        score = price_change_pct / threshold if prediction == 1 else -price_change_pct / threshold
        return max(-1.0, min(1.0, score))

    def complete_prediction(self, pred_id: str, result: int, final_price: float, end_timestamp: pd.Timestamp, end_idx: int, reason: str, custom_score: float = None):
        pred = self.active_predictions[pred_id]
        pred['status'] = 'completed'
        score = custom_score if custom_score is not None else self.calculate_score(pred['guess'], pred['start_price'], final_price, result)
        profit_loss = pred['trade_risk_capital'] * score
        self.current_capital += profit_loss
        if result == 1: self.successful_predictions += 1; status_str = "成功✅"
        elif result == 0: self.failed_predictions += 1; status_str = "失败❌"
        elif result == -2: self.stop_loss_predictions += 1; status_str = "止损🛑"
        else: self.timeout_predictions += 1; status_str = "超时⏰"
        completed_pred = {
            'PredictionID': pred['id'], 'StartTimestamp': format_beijing_time(pred['start_timestamp']),
            'EndTimestamp': format_beijing_time(end_timestamp), 'StartPrice': pred['start_price'],
            'EndPrice': final_price, 'PriceChangePct': ((final_price - pred['start_price']) / pred['start_price']) * 100,
            'MaxLossPct': pred['max_loss_pct'], 'MaxLossPrice': pred['max_loss_price'],
            'MaxLossTimestamp': format_beijing_time(pred['max_loss_timestamp']), 'Confidence': pred['probability'],
            'Prediction': pred['guess'], 'Result': result, 'Score': score,
            'ProfitLoss': profit_loss, 'CapitalAfter': self.current_capital,
            'Status': status_str, 'Reason': reason,
            'DurationMinutes': (end_idx - pred['start_idx']) * self.config['timeframe_minutes'],
            'UpTarget': pred['up_target'], 'DownTarget': pred['down_target']
        }
        self.completed_predictions.append(completed_pred)
        direction_str = f"先涨..." if pred['guess'] == 1 else f"先跌..."
        max_loss_info = f", 最大亏损: {pred['max_loss_pct']:.2f}%" if pred['max_loss_pct'] > 0 else ""
        print(f"[{format_beijing_time(end_timestamp)}] 预测完成: {direction_str} -> {status_str}, "
              f"得分: {score:+.2f}, 盈亏: ${profit_loss:+.2f}, "
              f"当前资金: ${self.current_capital:,.2f}{max_loss_info}")

def run_backtest(model_file, config_file, df,
                 initial_capital=10000, risk_per_trade_pct=1.0,
                 price_multiplier=1.0, stop_loss_pct=None, max_active_predictions=10000):
    print("=== 开始真实环境模拟回测 (含资金管理) ===")
    backtester = HistoricalBacktester(model_file, config_file, initial_capital, risk_per_trade_pct, price_multiplier, stop_loss_pct)
    min_history = 720 // backtester.config.get('timeframe_minutes', 5) + 50
    prediction_window_size = 1000
    if prediction_window_size < min_history:
        prediction_window_size = min_history
    actual_start_pos = prediction_window_size
    print(f"\n从索引 {actual_start_pos} 开始预测 (时间: {format_beijing_time(df.index[actual_start_pos])})")
    print(f"将同时保持最多 {max_active_predictions} 笔活跃的投资。")
    for i in range(actual_start_pos, len(df)):
        current_timestamp, current_price = df.index[i], df.iloc[i]['close']
        backtester.check_predictions(current_price, current_timestamp, i)
        active_count = len([p for p in backtester.active_predictions.values() if p['status'] == 'active'])
        if active_count < max_active_predictions:
            start_slice_index = i - prediction_window_size + 1
            current_window_df = df.iloc[start_slice_index : i+1].copy()
            guess, probability, pred_price = backtester.make_prediction(current_window_df)
            if guess is not None:
                backtester.add_prediction(guess, probability, pred_price, current_timestamp, i)
    final_timestamp, final_price, final_idx = df.index[-1], df.iloc[-1]['close'], len(df) - 1
    for pred_id in list(backtester.active_predictions.keys()):
        backtester.complete_prediction(pred_id, -1, final_price, final_timestamp, final_idx, "数据结束-超时")
    print("\n=== 回测结果摘要 ===")
    print(f"总预测数: {backtester.total_predictions}, 成功: {backtester.successful_predictions}, 失败: {backtester.failed_predictions}, 超时: {backtester.timeout_predictions}", end="")
    if stop_loss_pct is not None: print(f", 止损: {backtester.stop_loss_predictions}")
    else: print()
    final_capital = backtester.current_capital
    total_return_pct = (final_capital - initial_capital) / initial_capital * 100
    print(f"\n初始资金: ${initial_capital:,.2f}")
    print(f"最终资金: ${final_capital:,.2f}")
    print(f"总收益率: {total_return_pct:+.2f}%")
    if backtester.completed_predictions:
        results_df = pd.DataFrame(backtester.completed_predictions)
        results_df['_sort_time'] = pd.to_datetime(results_df['StartTimestamp'].str.replace(' UTC+8', ''))
        results_df = results_df.sort_values('_sort_time').drop('_sort_time', axis=1)
        results_df.to_csv("backtest_money_log.csv", index=False, float_format='%.4f')
        print(f"\n详细回测日志已保存到: backtest_money_log.csv")
        analyze_and_plot_results(results_df, "backtest_money")
    else:
        print("\n没有生成任何预测结果。")

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="使用资金管理模型对SQLite历史数据进行回测。")
    parser.add_argument("--coin", default="ETH", help="交易对，例如 BTC")
    parser.add_argument("--interval", default="15m", help="K线间隔，例如 15m")
    parser.add_argument("--market", default="spot", choices=["spot", "futures"], help="市场类型")
    parser.add_argument("--db", default="coin_data.db", help="SQLite数据库路径")
    parser.add_argument("--start-time", help="回测开始时间(北京时间)")
    parser.add_argument("--end-time", help="回测结束时间(北京时间)")
    parser.add_argument("--stop-loss", type=float, help="止损百分比")
    parser.add_argument("--max-active-predictions", type=int, default=10000, help="最大同时活跃预测数")
    parser.add_argument("--initial-capital", type=float, default=10000, help="初始资金")
    parser.add_argument("--risk-per-trade", type=float, default=1.0, help="单次交易风险比例(%)")
    parser.add_argument("--model-file",  help="模型文件路径 (.joblib)")
    parser.add_argument("--config-file",  help="配置文件路径 (.json)")
    args = parser.parse_args()

    start_time = parse_time_input(args.start_time) if args.start_time else None
    end_time = parse_time_input(args.end_time) if args.end_time else None

        # 获取币种配置
    coin_config = get_coin_config(args.coin)
    if coin_config is None:
        exit(1)

    model_file = args.model_file if args.model_file else f"models/{coin_config['model_basename']}_model.joblib"
    config_file = args.config_file if args.config_file else f"models/{coin_config['model_basename']}_config.json"
    df = load_data_from_sqlite(args.db, coin_config['api_symbol'], args.interval, args.market, price_multiplier=1.0, start_time=start_time, end_time=end_time)
    if df is None:
        exit(1)

    run_backtest(
        model_file, config_file, df,
        initial_capital=args.initial_capital,
        risk_per_trade_pct=args.risk_per_trade,
        price_multiplier=1.0,
        stop_loss_pct=args.stop_loss,
        max_active_predictions=args.max_active_predictions
    )

