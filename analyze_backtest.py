# analyze_backtest.py
# 独立的回测分析工具
# 用于分析已保存的回测日志CSV文件，生成统计报告和图表

import pandas as pd
import numpy as np
import argparse
import os
from datetime import datetime
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib import rcParams
import pytz

# 尝试导入plotly，如果失败则跳过交互式图表
try:
    import plotly.graph_objects as go
    from plotly.subplots import make_subplots
    import plotly.express as px
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False
    print("⚠️ plotly未安装，将跳过交互式图表生成")

# 北京时区
BEIJING_TZ = pytz.timezone('Asia/Shanghai')

def format_beijing_time(timestamp):
    """格式化北京时间为字符串"""
    if isinstance(timestamp, pd.Timestamp):
        if timestamp.tz is None:
            timestamp = timestamp.tz_localize('UTC')
        beijing_time = timestamp.tz_convert(BEIJING_TZ)
        return beijing_time.strftime('%Y-%m-%d %H:%M:%S UTC+8')
    return timestamp

def create_plotly_charts(results_df, fund_df, output_prefix, window_size, rolling_win_rate, rolling_times, avg_fund_usage):
    """使用Plotly创建交互式图表"""
    print(f"\n🚀 生成交互式图表...")

    # 创建子图
    fig = make_subplots(
        rows=2, cols=2,
        subplot_titles=('📈 累计收益曲线', '💰 资金占用情况', '📊 单笔交易收益分布', f'📈 滚动胜率 (最近{window_size}笔)'),
        specs=[[{"secondary_y": False}, {"secondary_y": False}],
               [{"secondary_y": False}, {"secondary_y": False}]]
    )

    # 1. 累计收益曲线
    fig.add_trace(
        go.Scatter(
            x=results_df['EndTime'],
            y=results_df['CumulativeReturn'],
            mode='lines',
            name='累计收益',
            line=dict(color='#1f77b4', width=3),
            hovertemplate='<b>时间</b>: %{x}<br><b>累计收益</b>: %{y:.2f}元<extra></extra>'
        ),
        row=1, col=1
    )

    # 添加盈亏平衡线
    fig.add_hline(y=0, line_dash="dash", line_color="red",
                  annotation_text="盈亏平衡线", row=1, col=1)

    # 添加填充区域（盈利/亏损）
    positive_mask = results_df['CumulativeReturn'] >= 0
    if positive_mask.any():
        fig.add_trace(
            go.Scatter(
                x=results_df[positive_mask]['EndTime'],
                y=results_df[positive_mask]['CumulativeReturn'],
                fill='tozeroy',
                fillcolor='rgba(0,255,0,0.2)',
                line=dict(color='rgba(255,255,255,0)'),
                showlegend=False,
                hoverinfo='skip',
                name='盈利区域'
            ),
            row=1, col=1
        )

    negative_mask = results_df['CumulativeReturn'] < 0
    if negative_mask.any():
        fig.add_trace(
            go.Scatter(
                x=results_df[negative_mask]['EndTime'],
                y=results_df[negative_mask]['CumulativeReturn'],
                fill='tozeroy',
                fillcolor='rgba(255,0,0,0.2)',
                line=dict(color='rgba(255,255,255,0)'),
                showlegend=False,
                hoverinfo='skip',
                name='亏损区域'
            ),
            row=1, col=1
        )

    # 2. 资金占用情况
    fig.add_trace(
        go.Scatter(
            x=fund_df['Time'],
            y=fund_df['ActiveTrades'],
            mode='lines',
            name='同时进行的交易数',
            line=dict(color='#2ca02c', width=3),
            fill='tozeroy',
            fillcolor='rgba(44,160,44,0.2)',
            hovertemplate='<b>时间</b>: %{x}<br><b>同时交易数</b>: %{y}<extra></extra>'
        ),
        row=1, col=2
    )

    # 添加平均占用线
    fig.add_hline(y=avg_fund_usage, line_dash="dash", line_color="orange",
                  annotation_text=f"平均占用: {avg_fund_usage:.1f}", row=1, col=2)

    # 3. 单笔交易收益分布
    fig.add_trace(
        go.Histogram(
            x=results_df['Score'],
            nbinsx=50,
            name='收益分布',
            marker_color='skyblue',
            marker_line_color='black',
            marker_line_width=1,
            hovertemplate='<b>收益区间</b>: %{x}<br><b>交易次数</b>: %{y}<extra></extra>'
        ),
        row=2, col=1
    )

    # 添加平均收益线和盈亏平衡线
    avg_score = results_df['Score'].mean()
    fig.add_vline(x=0, line_dash="dash", line_color="red",
                  annotation_text="盈亏平衡", row=2, col=1)
    fig.add_vline(x=avg_score, line_dash="dash", line_color="orange",
                  annotation_text=f"平均: {avg_score:+.3f}", row=2, col=1)

    # 4. 滚动胜率
    if rolling_win_rate:
        fig.add_trace(
            go.Scatter(
                x=rolling_times,
                y=rolling_win_rate,
                mode='lines',
                name=f'滚动胜率',
                line=dict(color='#9467bd', width=3),
                hovertemplate='<b>时间</b>: %{x}<br><b>胜率</b>: %{y:.1f}%<extra></extra>'
            ),
            row=2, col=2
        )

        # 添加50%基准线
        fig.add_hline(y=50, line_dash="dash", line_color="red",
                      annotation_text="50%基准", row=2, col=2)

        # 添加填充区域（胜率高于/低于50%）
        above_50 = [rate if rate >= 50 else 50 for rate in rolling_win_rate]
        below_50 = [rate if rate < 50 else 50 for rate in rolling_win_rate]

        fig.add_trace(
            go.Scatter(
                x=rolling_times,
                y=above_50,
                fill='tonexty',
                fillcolor='rgba(0,255,0,0.2)',
                line=dict(color='rgba(255,255,255,0)'),
                showlegend=False,
                hoverinfo='skip',
                name='胜率>50%'
            ),
            row=2, col=2
        )

        fig.add_trace(
            go.Scatter(
                x=rolling_times,
                y=below_50,
                fill='tonexty',
                fillcolor='rgba(255,0,0,0.2)',
                line=dict(color='rgba(255,255,255,0)'),
                showlegend=False,
                hoverinfo='skip',
                name='胜率<50%'
            ),
            row=2, col=2
        )

    # 更新布局
    fig.update_layout(
        title_text="🎯 回测结果交互式分析仪表板",
        title_x=0.5,
        title_font_size=24,
        height=900,
        showlegend=True,
        hovermode='x unified',
        template='plotly_white',
        font=dict(size=12)
    )

    # 更新x轴标签
    fig.update_xaxes(title_text="⏰ 时间", row=1, col=1)
    fig.update_xaxes(title_text="⏰ 时间", row=1, col=2)
    fig.update_xaxes(title_text="💰 单笔收益 (元)", row=2, col=1)
    fig.update_xaxes(title_text="⏰ 时间", row=2, col=2)

    # 更新y轴标签
    fig.update_yaxes(title_text="💰 累计收益 (元)", row=1, col=1)
    fig.update_yaxes(title_text="📊 同时进行的交易数", row=1, col=2)
    fig.update_yaxes(title_text="📊 交易次数", row=2, col=1)
    fig.update_yaxes(title_text="📈 胜率 (%)", row=2, col=2, range=[0, 100])

    # 保存交互式图表
    html_file = f"{output_prefix}_interactive.html"
    fig.write_html(html_file, config={'displayModeBar': True, 'displaylogo': False})
    print(f"🎉 交互式图表已保存到: {html_file}")
    print("💡 用浏览器打开HTML文件即可查看交互式图表")
    print("✨ 支持功能: 缩放、平移、悬停查看详情、图例切换、数据选择等")

    return fig

def analyze_max_loss_patterns(results_df, output_prefix):
    """分析最大亏损模式，帮助制定止损策略"""
    print("\n=== 最大亏损模式分析 ===")
    
    # 检查是否有MaxLossPct列
    if 'MaxLossPct' not in results_df.columns:
        print("⚠️ 警告: CSV文件中没有MaxLossPct列，无法进行最大亏损分析")
        print("请使用最新版本的backtest.py重新生成回测结果")
        return
    
    # 分类交易
    successful_trades = results_df[results_df['Result'] == 1]  # 成功达到目标的交易
    timeout_profitable = results_df[(results_df['Result'] == -1) & (results_df['Score'] > 0)]  # 超时但盈利的交易
    timeout_loss = results_df[(results_df['Result'] == -1) & (results_df['Score'] < 0)]  # 超时且亏损的交易
    failed_trades = results_df[results_df['Result'] == 0]  # 预测失败的交易
    
    print(f"📊 交易分类统计:")
    print(f"  成功交易: {len(successful_trades)} 笔")
    print(f"  超时盈利: {len(timeout_profitable)} 笔")
    print(f"  超时亏损: {len(timeout_loss)} 笔")
    print(f"  预测失败: {len(failed_trades)} 笔")
    print(f"  总计: {len(results_df)} 笔")
    
    # 创建图表
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('最大亏损模式分析', fontsize=16, fontweight='bold')
    
    # 1. 各类交易的最大亏损分布
    ax1.hist(successful_trades['MaxLossPct'], bins=30, alpha=0.7, label='成功交易', color='green', density=True)
    ax1.hist(timeout_profitable['MaxLossPct'], bins=30, alpha=0.7, label='超时盈利', color='orange', density=True)
    ax1.hist(timeout_loss['MaxLossPct'], bins=30, alpha=0.7, label='超时亏损', color='red', density=True)
    ax1.hist(failed_trades['MaxLossPct'], bins=30, alpha=0.7, label='预测失败', color='purple', density=True)
    ax1.set_xlabel('最大亏损百分比 (%)')
    ax1.set_ylabel('密度')
    ax1.set_title('各类交易的最大亏损分布')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 最大亏损与最终收益的关系散点图
    ax2.scatter(successful_trades['MaxLossPct'], successful_trades['Score'], alpha=0.6, label='成功交易', color='green', s=20)
    ax2.scatter(timeout_profitable['MaxLossPct'], timeout_profitable['Score'], alpha=0.6, label='超时盈利', color='orange', s=20)
    ax2.scatter(timeout_loss['MaxLossPct'], timeout_loss['Score'], alpha=0.6, label='超时亏损', color='red', s=20)
    ax2.scatter(failed_trades['MaxLossPct'], failed_trades['Score'], alpha=0.6, label='预测失败', color='purple', s=20)
    ax2.set_xlabel('最大亏损百分比 (%)')
    ax2.set_ylabel('最终收益')
    ax2.set_title('最大亏损与最终收益的关系')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. 最大亏损统计箱线图
    max_loss_data = []
    labels = []
    colors = []
    
    if len(successful_trades) > 0:
        max_loss_data.append(successful_trades['MaxLossPct'].values)
        labels.append('成功交易')
        colors.append('green')
    
    if len(timeout_profitable) > 0:
        max_loss_data.append(timeout_profitable['MaxLossPct'].values)
        labels.append('超时盈利')
        colors.append('orange')
    
    if len(timeout_loss) > 0:
        max_loss_data.append(timeout_loss['MaxLossPct'].values)
        labels.append('超时亏损')
        colors.append('red')
    
    if len(failed_trades) > 0:
        max_loss_data.append(failed_trades['MaxLossPct'].values)
        labels.append('预测失败')
        colors.append('purple')
    
    if max_loss_data:
        bp = ax3.boxplot(max_loss_data, labels=labels, patch_artist=True)
        for patch, color in zip(bp['boxes'], colors):
            patch.set_facecolor(color)
            patch.set_alpha(0.7)
        ax3.set_ylabel('最大亏损百分比 (%)')
        ax3.set_title('各类交易的最大亏损统计')
        ax3.grid(True, alpha=0.3)
    
    # 4. 止损建议分析
    # 计算不同止损水平下的效果
    stop_loss_levels = [0.5, 1.0, 1.5, 2.0, 2.5, 3.0, 3.5, 4.0, 4.5, 5.0]
    stop_loss_analysis = []
    
    for level in stop_loss_levels:
        # 模拟在某个止损水平下的效果
        would_stop_loss = results_df[results_df['MaxLossPct'] >= level]
        would_continue = results_df[results_df['MaxLossPct'] < level]
        
        # 计算止损的交易
        stopped_trades = len(would_stop_loss)
        continued_trades = len(would_continue)
        
        # 计算止损交易的平均收益（按比例计算，5%对应1分）
        if stopped_trades > 0:
            # 止损时的损失按比例计算：亏损百分比 / 5% * -1
            stopped_avg_score = -(level / 5.0)  # 例如：止损2.5%时损失为-0.5分
        else:
            stopped_avg_score = 0
        
        # 计算继续交易的平均收益
        continued_avg_score = would_continue['Score'].mean() if continued_trades > 0 else 0
        
        # 计算总体平均收益
        total_trades = stopped_trades + continued_trades
        if total_trades > 0:
            overall_avg_score = (stopped_trades * stopped_avg_score + continued_trades * continued_avg_score) / total_trades
        else:
            overall_avg_score = 0
        
        stop_loss_analysis.append({
            'level': level,
            'stopped_trades': stopped_trades,
            'continued_trades': continued_trades,
            'stopped_pct': stopped_trades / len(results_df) * 100 if len(results_df) > 0 else 0,
            'stopped_avg_score': stopped_avg_score,
            'continued_avg_score': continued_avg_score,
            'overall_avg_score': overall_avg_score
        })
    
    # 绘制止损分析
    levels = [item['level'] for item in stop_loss_analysis]
    stopped_pcts = [item['stopped_pct'] for item in stop_loss_analysis]
    continued_avg_scores = [item['continued_avg_score'] for item in stop_loss_analysis]
    overall_avg_scores = [item['overall_avg_score'] for item in stop_loss_analysis]
    
    ax4_twin = ax4.twinx()
    
    # 止损比例
    ax4.plot(levels, stopped_pcts, 'r-', linewidth=2, label='止损比例', marker='o')
    ax4.set_xlabel('止损水平 (%)')
    ax4.set_ylabel('止损比例 (%)', color='red')
    ax4.tick_params(axis='y', labelcolor='red')
    
    # 平均收益
    ax4_twin.plot(levels, continued_avg_scores, 'b-', linewidth=2, label='继续交易平均收益', marker='s')
    ax4_twin.plot(levels, overall_avg_scores, 'g-', linewidth=2, label='总体平均收益', marker='^')
    ax4_twin.set_ylabel('平均收益', color='blue')
    ax4_twin.tick_params(axis='y', labelcolor='blue')
    
    ax4.set_title('不同止损水平的效果分析')
    ax4.grid(True, alpha=0.3)
    
    # 合并图例
    lines1, labels1 = ax4.get_legend_handles_labels()
    lines2, labels2 = ax4_twin.get_legend_handles_labels()
    ax4.legend(lines1 + lines2, labels1 + labels2, loc='upper left')
    
    plt.tight_layout()
    
    # 保存图表
    chart_file = f"{output_prefix}_max_loss_analysis.png"
    plt.savefig(chart_file, dpi=300, bbox_inches='tight')
    print(f"📊 最大亏损分析图表已保存到: {chart_file}")
    
    # 打印详细统计
    print(f"\n📈 各类交易的最大亏损统计:")
    
    if len(successful_trades) > 0:
        print(f"  成功交易 ({len(successful_trades)} 笔):")
        print(f"    平均最大亏损: {successful_trades['MaxLossPct'].mean():.2f}%")
        print(f"    中位数最大亏损: {successful_trades['MaxLossPct'].median():.2f}%")
        print(f"    最大亏损范围: {successful_trades['MaxLossPct'].min():.2f}% - {successful_trades['MaxLossPct'].max():.2f}%")
        print(f"    75%分位数: {successful_trades['MaxLossPct'].quantile(0.75):.2f}%")
    
    if len(timeout_profitable) > 0:
        print(f"  超时盈利 ({len(timeout_profitable)} 笔):")
        print(f"    平均最大亏损: {timeout_profitable['MaxLossPct'].mean():.2f}%")
        print(f"    中位数最大亏损: {timeout_profitable['MaxLossPct'].median():.2f}%")
        print(f"    最大亏损范围: {timeout_profitable['MaxLossPct'].min():.2f}% - {timeout_profitable['MaxLossPct'].max():.2f}%")
        print(f"    75%分位数: {timeout_profitable['MaxLossPct'].quantile(0.75):.2f}%")
    
    if len(timeout_loss) > 0:
        print(f"  超时亏损 ({len(timeout_loss)} 笔):")
        print(f"    平均最大亏损: {timeout_loss['MaxLossPct'].mean():.2f}%")
        print(f"    中位数最大亏损: {timeout_loss['MaxLossPct'].median():.2f}%")
        print(f"    最大亏损范围: {timeout_loss['MaxLossPct'].min():.2f}% - {timeout_loss['MaxLossPct'].max():.2f}%")
        print(f"    75%分位数: {timeout_loss['MaxLossPct'].quantile(0.75):.2f}%")
    
    if len(failed_trades) > 0:
        print(f"  预测失败 ({len(failed_trades)} 笔):")
        print(f"    平均最大亏损: {failed_trades['MaxLossPct'].mean():.2f}%")
        print(f"    中位数最大亏损: {failed_trades['MaxLossPct'].median():.2f}%")
        print(f"    最大亏损范围: {failed_trades['MaxLossPct'].min():.2f}% - {failed_trades['MaxLossPct'].max():.2f}%")
        print(f"    75%分位数: {failed_trades['MaxLossPct'].quantile(0.75):.2f}%")
    
    # 止损建议
    print(f"\n🎯 止损策略建议:")
    
    # 找到最优止损水平
    best_level = None
    best_score = float('-inf')
    
    for analysis in stop_loss_analysis:
        if analysis['overall_avg_score'] > best_score:
            best_score = analysis['overall_avg_score']
            best_level = analysis['level']
    
    if best_level is not None:
        print(f"  推荐止损水平: {best_level:.1f}%")
        print(f"  预期平均收益: {best_score:+.4f}")
        
        # 找到对应的分析
        best_analysis = next(item for item in stop_loss_analysis if item['level'] == best_level)
        print(f"  止损比例: {best_analysis['stopped_pct']:.1f}%")
        print(f"  止损损失: {best_analysis['stopped_avg_score']:+.4f} (按比例计算)")
        print(f"  继续交易平均收益: {best_analysis['continued_avg_score']:+.4f}")
    
    # 风险分析
    high_risk_trades = results_df[results_df['MaxLossPct'] >= 3.0]  # 最大亏损超过3%的交易
    if len(high_risk_trades) > 0:
        print(f"\n⚠️ 高风险交易分析 (最大亏损 >= 3%):")
        print(f"  高风险交易数量: {len(high_risk_trades)} 笔 ({len(high_risk_trades)/len(results_df)*100:.1f}%)")
        print(f"  高风险交易平均收益: {high_risk_trades['Score'].mean():+.4f}")
        print(f"  高风险交易胜率: {len(high_risk_trades[high_risk_trades['Score'] > 0])/len(high_risk_trades)*100:.1f}%")
    
    # 保存详细报告
    report_file = f"{output_prefix}_max_loss_report.txt"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("=== 最大亏损模式分析报告 ===\n\n")
        
        f.write("📊 交易分类统计:\n")
        f.write(f"  成功交易: {len(successful_trades)} 笔\n")
        f.write(f"  超时盈利: {len(timeout_profitable)} 笔\n")
        f.write(f"  超时亏损: {len(timeout_loss)} 笔\n")
        f.write(f"  预测失败: {len(failed_trades)} 笔\n")
        f.write(f"  总计: {len(results_df)} 笔\n\n")
        
        f.write("📈 各类交易的最大亏损统计:\n")
        if len(successful_trades) > 0:
            f.write(f"  成功交易 ({len(successful_trades)} 笔):\n")
            f.write(f"    平均最大亏损: {successful_trades['MaxLossPct'].mean():.2f}%\n")
            f.write(f"    中位数最大亏损: {successful_trades['MaxLossPct'].median():.2f}%\n")
            f.write(f"    最大亏损范围: {successful_trades['MaxLossPct'].min():.2f}% - {successful_trades['MaxLossPct'].max():.2f}%\n")
            f.write(f"    75%分位数: {successful_trades['MaxLossPct'].quantile(0.75):.2f}%\n\n")
        
        if len(timeout_profitable) > 0:
            f.write(f"  超时盈利 ({len(timeout_profitable)} 笔):\n")
            f.write(f"    平均最大亏损: {timeout_profitable['MaxLossPct'].mean():.2f}%\n")
            f.write(f"    中位数最大亏损: {timeout_profitable['MaxLossPct'].median():.2f}%\n")
            f.write(f"    最大亏损范围: {timeout_profitable['MaxLossPct'].min():.2f}% - {timeout_profitable['MaxLossPct'].max():.2f}%\n")
            f.write(f"    75%分位数: {timeout_profitable['MaxLossPct'].quantile(0.75):.2f}%\n\n")
        
        if len(timeout_loss) > 0:
            f.write(f"  超时亏损 ({len(timeout_loss)} 笔):\n")
            f.write(f"    平均最大亏损: {timeout_loss['MaxLossPct'].mean():.2f}%\n")
            f.write(f"    中位数最大亏损: {timeout_loss['MaxLossPct'].median():.2f}%\n")
            f.write(f"    最大亏损范围: {timeout_loss['MaxLossPct'].min():.2f}% - {timeout_loss['MaxLossPct'].max():.2f}%\n")
            f.write(f"    75%分位数: {timeout_loss['MaxLossPct'].quantile(0.75):.2f}%\n\n")
        
        if len(failed_trades) > 0:
            f.write(f"  预测失败 ({len(failed_trades)} 笔):\n")
            f.write(f"    平均最大亏损: {failed_trades['MaxLossPct'].mean():.2f}%\n")
            f.write(f"    中位数最大亏损: {failed_trades['MaxLossPct'].median():.2f}%\n")
            f.write(f"    最大亏损范围: {failed_trades['MaxLossPct'].min():.2f}% - {failed_trades['MaxLossPct'].max():.2f}%\n")
            f.write(f"    75%分位数: {failed_trades['MaxLossPct'].quantile(0.75):.2f}%\n\n")
        
        f.write("🎯 止损策略建议:\n")
        if best_level is not None:
            f.write(f"  推荐止损水平: {best_level:.1f}%\n")
            f.write(f"  预期平均收益: {best_score:+.4f}\n")
            best_analysis = next(item for item in stop_loss_analysis if item['level'] == best_level)
            f.write(f"  止损比例: {best_analysis['stopped_pct']:.1f}%\n")
            f.write(f"  止损损失: {best_analysis['stopped_avg_score']:+.4f} (按比例计算)\n")
            f.write(f"  继续交易平均收益: {best_analysis['continued_avg_score']:+.4f}\n\n")
        
        if len(high_risk_trades) > 0:
            f.write("⚠️ 高风险交易分析 (最大亏损 >= 3%):\n")
            f.write(f"  高风险交易数量: {len(high_risk_trades)} 笔 ({len(high_risk_trades)/len(results_df)*100:.1f}%)\n")
            f.write(f"  高风险交易平均收益: {high_risk_trades['Score'].mean():+.4f}\n")
            f.write(f"  高风险交易胜率: {len(high_risk_trades[high_risk_trades['Score'] > 0])/len(high_risk_trades)*100:.1f}%\n")
    
    print(f"详细最大亏损分析报告已保存到: {report_file}")

def analyze_and_plot_results(results_df, output_prefix):
    """分析回测结果并生成收益曲线图"""
    print("\n=== 收益分析 ===")

    # 设置中文字体
    rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    rcParams['axes.unicode_minus'] = False

    # 1. 计算累计收益曲线（假设每次投入1元）
    results_df['EndTime'] = pd.to_datetime(results_df['EndTimestamp'].str.replace(' UTC+8', ''))
    results_df = results_df.sort_values('EndTime')

    # 计算累计收益
    results_df['CumulativeReturn'] = results_df['Score'].cumsum()
    results_df['CumulativeBalance'] = 1000 + results_df['CumulativeReturn']  # 假设初始资金1000元

    # 2. 计算资金使用情况
    # 为每笔交易分配开始和结束时间
    results_df['StartTime'] = pd.to_datetime(results_df['StartTimestamp'].str.replace(' UTC+8', ''))

    # 计算同时进行的交易数量（资金占用）
    time_points = []
    fund_usage = []

    # 创建时间序列来跟踪资金使用
    all_times = sorted(set(results_df['StartTime'].tolist() + results_df['EndTime'].tolist()))

    for time_point in all_times:
        # 计算在这个时间点有多少交易在进行
        active_trades = results_df[
            (results_df['StartTime'] <= time_point) &
            (results_df['EndTime'] > time_point)
        ]
        time_points.append(time_point)
        fund_usage.append(len(active_trades))

    fund_df = pd.DataFrame({
        'Time': time_points,
        'ActiveTrades': fund_usage
    })

    # 3. 计算关键统计指标
    total_trades = len(results_df)
    total_return = results_df['Score'].sum()
    max_return = results_df['CumulativeReturn'].max()
    min_return = results_df['CumulativeReturn'].min()
    max_drawdown = max_return - min_return
    max_fund_usage = fund_df['ActiveTrades'].max()
    avg_fund_usage = fund_df['ActiveTrades'].mean()

    # 计算胜率
    winning_trades = len(results_df[results_df['Score'] > 0])
    win_rate = winning_trades / total_trades * 100

    # 计算最大连续亏损
    consecutive_losses = 0
    max_consecutive_losses = 0
    for score in results_df['Score']:
        if score < 0:
            consecutive_losses += 1
            max_consecutive_losses = max(max_consecutive_losses, consecutive_losses)
        else:
            consecutive_losses = 0

    # 按预测方向分类统计
    bullish_trades = results_df[results_df['Prediction'] == 1]  # 看涨预测
    bearish_trades = results_df[results_df['Prediction'] == 0]  # 看跌预测

    # 看涨统计
    bullish_count = len(bullish_trades)
    bullish_winning = len(bullish_trades[bullish_trades['Score'] > 0])
    bullish_win_rate = bullish_winning / bullish_count * 100 if bullish_count > 0 else 0
    bullish_return = bullish_trades['Score'].sum()
    bullish_avg_score = bullish_trades['Score'].mean() if bullish_count > 0 else 0

    # 看跌统计
    bearish_count = len(bearish_trades)
    bearish_winning = len(bearish_trades[bearish_trades['Score'] > 0])
    bearish_win_rate = bearish_winning / bearish_count * 100 if bearish_count > 0 else 0
    bearish_return = bearish_trades['Score'].sum()
    bearish_avg_score = bearish_trades['Score'].mean() if bearish_count > 0 else 0

    # 打印总体统计结果
    print(f"总交易次数: {total_trades}")
    print(f"总收益: {total_return:+.2f} 元")
    print(f"胜率: {win_rate:.2f}% ({winning_trades}/{total_trades})")
    print(f"最大收益: {max_return:+.2f} 元")
    print(f"最大回撤: {max_drawdown:.2f} 元")
    print(f"最大连续亏损: {max_consecutive_losses} 次")
    print(f"最大资金占用: {max_fund_usage} 元 (同时进行 {max_fund_usage} 笔交易)")
    print(f"平均资金占用: {avg_fund_usage:.1f} 元")
    print(f"资金利用率: {avg_fund_usage/max_fund_usage*100:.1f}%")

    # 打印分类统计结果
    print(f"\n=== 按预测方向分类统计 ===")
    print(f"📈 看涨预测 (Prediction=1):")
    print(f"  交易次数: {bullish_count}")
    print(f"  总收益: {bullish_return:+.2f} 元")
    print(f"  胜率: {bullish_win_rate:.2f}% ({bullish_winning}/{bullish_count})")
    print(f"  平均得分: {bullish_avg_score:+.4f}")

    print(f"📉 看跌预测 (Prediction=0):")
    print(f"  交易次数: {bearish_count}")
    print(f"  总收益: {bearish_return:+.2f} 元")
    print(f"  胜率: {bearish_win_rate:.2f}% ({bearish_winning}/{bearish_count})")
    print(f"  平均得分: {bearish_avg_score:+.4f}")

    # 预测方向分布
    if bullish_count > 0 and bearish_count > 0:
        bullish_pct = bullish_count / total_trades * 100
        bearish_pct = bearish_count / total_trades * 100
        print(f"\n📊 预测方向分布:")
        print(f"  看涨预测占比: {bullish_pct:.1f}% ({bullish_count}/{total_trades})")
        print(f"  看跌预测占比: {bearish_pct:.1f}% ({bearish_count}/{total_trades})")
    elif bullish_count > 0:
        print(f"\n📊 预测方向分布: 100% 看涨预测 ({bullish_count}/{total_trades})")
    elif bearish_count > 0:
        print(f"\n📊 预测方向分布: 100% 看跌预测 ({bearish_count}/{total_trades})")

    # 4. 绘制图表
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('回测结果分析:'+output_prefix, fontsize=16, fontweight='bold')

    # 4.1 累计收益曲线
    ax1.plot(results_df['EndTime'], results_df['CumulativeReturn'], 'b-', linewidth=2, label='累计收益')
    ax1.axhline(y=0, color='r', linestyle='--', alpha=0.7, label='盈亏平衡线')
    ax1.fill_between(results_df['EndTime'], results_df['CumulativeReturn'], 0,
                     where=(results_df['CumulativeReturn'] >= 0), alpha=0.3, color='green', label='盈利区域')
    ax1.fill_between(results_df['EndTime'], results_df['CumulativeReturn'], 0,
                     where=(results_df['CumulativeReturn'] < 0), alpha=0.3, color='red', label='亏损区域')
    ax1.set_title('累计收益曲线')
    ax1.set_xlabel('时间')
    ax1.set_ylabel('累计收益 (元)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
    ax1.xaxis.set_major_locator(mdates.DayLocator(interval=1))
    plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45)

    # 4.2 资金占用情况
    ax2.plot(fund_df['Time'], fund_df['ActiveTrades'], 'g-', linewidth=2, label='同时进行的交易数')
    ax2.axhline(y=avg_fund_usage, color='orange', linestyle='--', alpha=0.7, label=f'平均占用: {avg_fund_usage:.1f}')
    ax2.fill_between(fund_df['Time'], fund_df['ActiveTrades'], alpha=0.3, color='green')
    ax2.set_title('资金占用情况')
    ax2.set_xlabel('时间')
    ax2.set_ylabel('同时进行的交易数')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
    ax2.xaxis.set_major_locator(mdates.DayLocator(interval=1))
    plt.setp(ax2.xaxis.get_majorticklabels(), rotation=45)

    # 4.3 单笔交易收益分布
    ax3.hist(results_df['Score'], bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    ax3.axvline(x=0, color='r', linestyle='--', alpha=0.7, label='盈亏平衡线')
    ax3.axvline(x=results_df['Score'].mean(), color='orange', linestyle='--', alpha=0.7,
                label=f'平均收益: {results_df["Score"].mean():+.3f}')
    ax3.set_title('单笔交易收益分布')
    ax3.set_xlabel('单笔收益 (元)')
    ax3.set_ylabel('交易次数')
    ax3.legend()
    ax3.grid(True, alpha=0.3)

    # 4.4 滚动胜率（最近100笔交易）
    window_size = min(100, len(results_df))
    rolling_win_rate = []
    rolling_times = []

    for i in range(window_size-1, len(results_df)):
        window_data = results_df.iloc[i-window_size+1:i+1]
        win_count = len(window_data[window_data['Score'] > 0])
        win_rate_pct = win_count / window_size * 100
        rolling_win_rate.append(win_rate_pct)
        rolling_times.append(window_data['EndTime'].iloc[-1])

    if rolling_win_rate:
        ax4.plot(rolling_times, rolling_win_rate, 'purple', linewidth=2, label=f'滚动胜率 (最近{window_size}笔)')
        ax4.axhline(y=50, color='r', linestyle='--', alpha=0.7, label='50%基准线')
        ax4.fill_between(rolling_times, rolling_win_rate, 50,
                         where=(np.array(rolling_win_rate) >= 50), alpha=0.3, color='green')
        ax4.fill_between(rolling_times, rolling_win_rate, 50,
                         where=(np.array(rolling_win_rate) < 50), alpha=0.3, color='red')
        ax4.set_title(f'滚动胜率 (窗口: {window_size}笔交易)')
        ax4.set_xlabel('时间')
        ax4.set_ylabel('胜率 (%)')
        ax4.set_ylim(0, 100)
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        ax4.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
        ax4.xaxis.set_major_locator(mdates.DayLocator(interval=1))
        plt.setp(ax4.xaxis.get_majorticklabels(), rotation=45)

    plt.tight_layout()

    # 5. 保存图表
    chart_file = f"{output_prefix}_analysis.png"
    plt.savefig(chart_file, dpi=300, bbox_inches='tight')
    print(f"\n📊 静态图表已保存到: {chart_file}")

    # 6. 生成Plotly交互式图表
    if PLOTLY_AVAILABLE:
        try:
            create_plotly_charts(results_df, fund_df, output_prefix, window_size, rolling_win_rate, rolling_times, avg_fund_usage)
        except Exception as e:
            print(f"⚠️ 生成交互式图表时出错: {e}")
            print("📊 静态图表已成功生成，可以继续使用")
    else:
        print("📊 静态图表已成功生成，但交互式图表由于plotly未安装而跳过。")

    # 7. 保存详细统计报告
    report_file = f"{output_prefix}_report.txt"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("=== 回测结果统计报告 ===\n\n")
        f.write(f"回测期间: {results_df['StartTime'].min().strftime('%Y-%m-%d %H:%M')} 到 {results_df['EndTime'].max().strftime('%Y-%m-%d %H:%M')}\n")
        f.write(f"总交易次数: {total_trades}\n")
        f.write(f"总收益: {total_return:+.2f} 元\n")
        f.write(f"平均每笔收益: {total_return/total_trades:+.4f} 元\n")
        f.write(f"胜率: {win_rate:.2f}% ({winning_trades}/{total_trades})\n")
        f.write(f"最大收益: {max_return:+.2f} 元\n")
        f.write(f"最大回撤: {max_drawdown:.2f} 元\n")
        f.write(f"最大连续亏损: {max_consecutive_losses} 次\n")
        f.write(f"最大资金占用: {max_fund_usage} 元\n")
        f.write(f"平均资金占用: {avg_fund_usage:.1f} 元\n")
        f.write(f"资金利用率: {avg_fund_usage/max_fund_usage*100:.1f}%\n\n")

        # 按预测方向分类统计
        f.write("=== 按预测方向分类统计 ===\n")
        f.write(f"📈 看涨预测 (Prediction=1):\n")
        f.write(f"  交易次数: {bullish_count}\n")
        f.write(f"  总收益: {bullish_return:+.2f} 元\n")
        f.write(f"  胜率: {bullish_win_rate:.2f}% ({bullish_winning}/{bullish_count})\n")
        f.write(f"  平均得分: {bullish_avg_score:+.4f}\n\n")

        f.write(f"📉 看跌预测 (Prediction=0):\n")
        f.write(f"  交易次数: {bearish_count}\n")
        f.write(f"  总收益: {bearish_return:+.2f} 元\n")
        f.write(f"  胜率: {bearish_win_rate:.2f}% ({bearish_winning}/{bearish_count})\n")
        f.write(f"  平均得分: {bearish_avg_score:+.4f}\n\n")

        # 预测方向分布
        if bullish_count > 0 and bearish_count > 0:
            bullish_pct = bullish_count / total_trades * 100
            bearish_pct = bearish_count / total_trades * 100
            f.write(f"📊 预测方向分布:\n")
            f.write(f"  看涨预测占比: {bullish_pct:.1f}% ({bullish_count}/{total_trades})\n")
            f.write(f"  看跌预测占比: {bearish_pct:.1f}% ({bearish_count}/{total_trades})\n\n")
        elif bullish_count > 0:
            f.write(f"📊 预测方向分布: 100% 看涨预测 ({bullish_count}/{total_trades})\n\n")
        elif bearish_count > 0:
            f.write(f"📊 预测方向分布: 100% 看跌预测 ({bearish_count}/{total_trades})\n\n")

        # 按结果类型统计
        f.write("=== 按交易结果分类 ===\n")
        result_stats = results_df.groupby('Status').agg({
            'Score': ['count', 'sum', 'mean'],
            'DurationMinutes': 'mean'
        }).round(4)
        f.write(str(result_stats))
        f.write("\n\n")

        # 按时间段统计
        f.write("=== 按小时统计 ===\n")
        results_df['Hour'] = results_df['StartTime'].dt.hour
        hourly_stats = results_df.groupby('Hour').agg({
            'Score': ['count', 'sum', 'mean']
        }).round(4)
        f.write(str(hourly_stats))

    print(f"详细统计报告已保存到: {report_file}")

    # 8. 进行最大亏损模式分析
    analyze_max_loss_patterns(results_df, output_prefix)

    # 显示图表
    # plt.show()

def analyze_backtest_csv(csv_file, output_prefix=None):
    """分析回测CSV文件"""
    print(f"=== 分析回测文件: {csv_file} ===")
    
    # 检查文件是否存在
    if not os.path.exists(csv_file):
        print(f"错误: 文件 '{csv_file}' 不存在")
        return
    
    # 读取CSV文件
    try:
        results_df = pd.read_csv(csv_file)
        print(f"成功读取 {len(results_df)} 条交易记录")
    except Exception as e:
        print(f"读取CSV文件时出错: {e}")
        return
    
    # 检查必要的列是否存在
    required_columns = ['StartTimestamp', 'EndTimestamp', 'Score', 'Prediction', 'Status']
    missing_columns = [col for col in required_columns if col not in results_df.columns]
    if missing_columns:
        print(f"错误: CSV文件缺少必要的列: {missing_columns}")
        print(f"当前列: {list(results_df.columns)}")
        return
    
    # 设置输出前缀
    if output_prefix is None:
        output_prefix = csv_file.replace('.csv', '')
    
    # 分析结果
    analyze_and_plot_results(results_df, output_prefix)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="分析回测CSV文件，生成统计报告和图表")
    parser.add_argument("csv_file", help="回测日志CSV文件路径")
    parser.add_argument("--output-prefix", help="输出文件前缀（可选，默认使用CSV文件名）")
    
    args = parser.parse_args()
    
    analyze_backtest_csv(args.csv_file, args.output_prefix)

if __name__ == '__main__':
    main() 