# TradingView风格回测信号可视化工具

这个工具可以将 `backtest_money_quick.py` 生成的回测日志在TradingView风格的K线图上可视化，帮助你直观地分析交易策略的表现。

## 功能特点

- 📈 **TradingView风格K线图**: 专业的金融图表界面
- 🎯 **交易信号标记**: 清晰显示买入/卖出信号
- 💰 **资金曲线**: 实时显示账户资金变化
- 📊 **成交量显示**: 配合K线分析市场活跃度
- 🔍 **交互式操作**: 支持缩放、平移、悬停查看详情
- 📋 **详细统计**: 显示胜率、收益率等关键指标
- 🎨 **颜色编码**: 成功/失败交易用不同颜色区分

## 安装依赖

确保已安装必要的Python包：

```bash
pip install plotly pandas numpy sqlite3
```

或者如果使用uv：

```bash
uv add plotly
```

## 使用方法

### 基本用法

```bash
python tradingview_backtest_visualizer.py --coin ETH --interval 15m
```

### 指定时间范围

```bash
python tradingview_backtest_visualizer.py \
    --coin ETH \
    --interval 15m \
    --start-time "2025-08-01" \
    --end-time "2025-08-25" \
    --output eth_backtest_analysis.html
```

### 生成并自动显示图表

```bash
python tradingview_backtest_visualizer.py \
    --coin ETH \
    --interval 15m \
    --show
```

## 命令行参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--coin` | ETH | 交易对名称 (如 BTC, ETH) |
| `--interval` | 15m | K线时间间隔 (如 5m, 15m, 1h) |
| `--market` | spot | 市场类型 (spot 或 futures) |
| `--db` | coin_data.db | SQLite数据库文件路径 |
| `--log` | backtest_money_log_quick.csv | 回测日志文件路径 |
| `--start-time` | - | 开始时间 (北京时间, YYYY-MM-DD HH:MM) |
| `--end-time` | - | 结束时间 (北京时间, YYYY-MM-DD HH:MM) |
| `--output` | 自动生成 | 输出HTML文件路径 |
| `--show` | False | 生成后自动在浏览器中显示 |

## 图表说明

### 信号标记

- 🔺 **绿色向上三角形**: 成功的买入信号 (预测上涨且正确)
- 🔻 **红色向下三角形**: 成功的卖出信号 (预测下跌且正确)
- 🔺 **黄色向上三角形**: 失败的买入信号
- 🔻 **橙色向下三角形**: 失败的卖出信号
- ⭐ **金色星星**: 成功交易的结束点
- 📏 **虚线**: 连接交易开始和结束点

### 图表布局

1. **主图 (上方)**: K线图 + 交易信号
2. **中图**: 成交量柱状图
3. **下图**: 账户资金曲线

### 交互功能

- **缩放**: 鼠标滚轮或拖拽选择区域
- **平移**: 按住鼠标左键拖拽
- **悬停**: 鼠标悬停查看详细信息
- **时间选择**: 使用底部的时间范围按钮
- **图例**: 点击图例项目显示/隐藏对应数据

## 统计信息

工具会自动计算并显示以下统计信息：

- 📊 **总交易数量**: 回测期间的总交易次数
- 🎯 **胜率**: 成功交易占总交易的百分比
- 💰 **总收益率**: 相对于初始资金的收益百分比
- 📉 **最大回撤**: 资金曲线的最大下跌幅度
- 🔍 **信心度分析**: 成功/失败交易的平均信心度
- 📈 **方向分析**: 买入/卖出信号的成功率

## 示例输出

运行工具后，你会看到类似以下的统计输出：

```
============================================================
📊 回测结果摘要
============================================================
总交易数量: 2257
成功交易: 1205 (53.38%)
失败交易: 623
超时交易: 429
止损交易: 0

💰 资金情况:
初始资金: $10,000.00
最终资金: $12,847.32
总收益: $+2,847.32 (+28.47%)
最大回撤: 8.45%
平均每笔收益: $+1.26

🎯 信心度分析:
平均信心度: 0.311
成功交易平均信心度: 0.312
失败交易平均信心度: 0.310

📈 预测方向分析:
买入信号: 1128 (成功率: 53.5%)
卖出信号: 1129 (成功率: 53.3%)
============================================================
```

## 快速开始示例

运行示例脚本来快速体验：

```bash
python example_tradingview_visualization.py
```

这个脚本会演示几种常见的使用场景。

## 注意事项

1. **数据要求**: 确保 `coin_data.db` 包含对应币种和时间间隔的K线数据
2. **回测日志**: 需要先运行 `backtest_money_quick.py` 生成回测日志
3. **时间格式**: 输入时间使用北京时间，格式为 YYYY-MM-DD 或 YYYY-MM-DD HH:MM
4. **浏览器兼容**: 生成的HTML文件需要现代浏览器支持 (Chrome, Firefox, Safari, Edge)
5. **文件大小**: 大量数据可能导致HTML文件较大，建议使用时间范围过滤

## 故障排除

### 常见问题

1. **"数据库中无符合条件的K线数据"**
   - 检查数据库文件是否存在
   - 确认币种名称和时间间隔是否正确
   - 验证时间范围内是否有数据

2. **"回测日志文件不存在"**
   - 先运行 `backtest_money_quick.py` 生成回测日志
   - 检查日志文件路径是否正确

3. **图表显示异常**
   - 确保浏览器支持JavaScript
   - 检查网络连接 (Plotly需要加载在线资源)
   - 尝试使用不同的浏览器

### 性能优化

- 对于大量数据，建议使用 `--start-time` 和 `--end-time` 限制时间范围
- 较短的时间间隔 (如1m) 会产生更多数据点，可能影响图表性能
- 可以考虑对数据进行采样以提高渲染速度

## 扩展功能

你可以根据需要修改 `tradingview_backtest_visualizer.py` 来添加：

- 更多技术指标 (移动平均线、RSI等)
- 自定义信号标记样式
- 额外的统计图表
- 导出功能 (PNG, PDF等)
- 实时数据更新

## 相关文件

- `tradingview_backtest_visualizer.py`: 主要可视化工具
- `example_tradingview_visualization.py`: 使用示例
- `backtest_money_quick.py`: 回测脚本 (生成数据源)
- `coin_data.db`: K线数据库
- `backtest_money_log_quick.csv`: 回测日志 (数据源)
