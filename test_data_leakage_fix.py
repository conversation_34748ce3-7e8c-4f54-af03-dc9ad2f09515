#!/usr/bin/env python3
"""
测试数据泄露修复

验证日线特征是否正确使用前一日数据，避免数据泄露
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from model_utils import load_daily_data, calculate_market_regime_features, calculate_features
from data_loader import load_data_for_training

def test_data_leakage_prevention():
    """测试数据泄露防护"""
    print("=== 测试数据泄露防护 ===")
    
    # 加载测试数据
    print("📊 加载测试数据...")
    df = load_data_for_training('ETH', 'coin_data.db', 'ETHUSDT', '15m', 'spot', 
                               start_time='2024-12-01', end_time='2024-12-05')  # 只用几天数据测试
    
    if df is None or len(df) < 100:
        print("❌ 测试数据不足")
        return False
    
    print(f"✅ 加载了 {len(df)} 条15分钟数据")
    print(f"📅 时间范围: {df.index[0]} 到 {df.index[-1]}")
    
    # 加载日线数据
    daily_df = load_daily_data('coin_data.db', 'ETHUSDT', 'spot')
    if daily_df is None:
        print("❌ 无法加载日线数据")
        return False
    
    # 计算特征
    print("\n🔧 计算增强特征...")
    df_features = calculate_features(df, timeframe=15, db_path='coin_data.db', 
                                   symbol='ETHUSDT', market='spot')
    
    if df_features is None or len(df_features) == 0:
        print("❌ 特征计算失败")
        return False
    
    # 检查日线特征
    daily_feature_cols = [col for col in df_features.columns if col.startswith('daily_')]
    
    if not daily_feature_cols:
        print("⚠️  没有找到日线特征")
        return False
    
    print(f"📊 找到 {len(daily_feature_cols)} 个日线特征")
    
    # 验证数据泄露防护
    print("\n🔍 验证数据泄露防护...")
    
    # 选择几个测试日期
    test_dates = []
    for i in range(min(3, len(df_features))):
        if i * 96 < len(df_features):  # 每天大约96个15分钟K线
            test_dates.append(df_features.index[i * 96])
    
    leakage_detected = False
    
    for test_timestamp in test_dates:
        test_date = test_timestamp.date()
        prev_date = test_date - timedelta(days=1)
        
        print(f"\n📅 测试日期: {test_date}")
        print(f"📅 应使用的前一日: {prev_date}")
        
        # 获取该时间点的日线特征值
        if test_timestamp in df_features.index:
            sample_feature = daily_feature_cols[0]  # 取第一个日线特征作为示例
            feature_value = df_features.loc[test_timestamp, sample_feature]
            
            print(f"🔍 特征 {sample_feature} 的值: {feature_value}")
            
            # 检查日线数据中对应日期的值
            daily_df_filtered = daily_df[daily_df.index.date == test_date]
            daily_df_prev = daily_df[daily_df.index.date == prev_date]
            
            if len(daily_df_filtered) > 0 and len(daily_df_prev) > 0:
                # 获取原始特征名（去掉daily_前缀）
                original_feature = sample_feature.replace('daily_', '')
                
                if original_feature in daily_df.columns:
                    current_day_value = daily_df_filtered[original_feature].iloc[0]
                    prev_day_value = daily_df_prev[original_feature].iloc[0]
                    
                    print(f"📊 当日日线值: {current_day_value}")
                    print(f"📊 前日日线值: {prev_day_value}")
                    
                    # 检查是否使用了当日数据（数据泄露）
                    if not pd.isna(feature_value):
                        if abs(feature_value - current_day_value) < 1e-6:
                            print("❌ 检测到数据泄露！使用了当日的日线数据")
                            leakage_detected = True
                        elif abs(feature_value - prev_day_value) < 1e-6:
                            print("✅ 正确使用了前一日的日线数据")
                        else:
                            print("⚠️  特征值与日线数据不匹配，可能是计算特征")
                    else:
                        print("ℹ️  特征值为NaN（前一日数据不可用）")
    
    if leakage_detected:
        print("\n❌ 数据泄露测试失败！")
        return False
    else:
        print("\n✅ 数据泄露测试通过！")
        return True

def test_feature_availability():
    """测试特征可用性"""
    print("\n=== 测试特征可用性 ===")
    
    # 加载更长时间的数据
    df = load_data_for_training('ETH', 'coin_data.db', 'ETHUSDT', '15m', 'spot', 
                               start_time='2024-11-01', end_time='2024-12-31')
    
    if df is None:
        print("❌ 无法加载数据")
        return False
    
    # 计算特征
    df_features = calculate_features(df, timeframe=15, db_path='coin_data.db', 
                                   symbol='ETHUSDT', market='spot')
    
    if df_features is None:
        print("❌ 特征计算失败")
        return False
    
    # 分析日线特征的可用性
    daily_feature_cols = [col for col in df_features.columns if col.startswith('daily_')]
    
    if not daily_feature_cols:
        print("⚠️  没有日线特征")
        return True
    
    print(f"📊 分析 {len(daily_feature_cols)} 个日线特征的可用性...")
    
    total_samples = len(df_features)
    
    for col in daily_feature_cols[:5]:  # 只分析前5个特征
        nan_count = df_features[col].isna().sum()
        available_ratio = (total_samples - nan_count) / total_samples
        
        print(f"  {col}: {available_ratio:.1%} 可用 ({total_samples - nan_count}/{total_samples})")
    
    # 整体统计
    overall_nan = df_features[daily_feature_cols].isna().sum().sum()
    total_values = len(df_features) * len(daily_feature_cols)
    overall_available = (total_values - overall_nan) / total_values
    
    print(f"\n📈 整体日线特征可用性: {overall_available:.1%}")
    
    if overall_available > 0.8:
        print("✅ 日线特征可用性良好")
        return True
    else:
        print("⚠️  日线特征可用性较低，可能影响模型性能")
        return True

def test_temporal_consistency():
    """测试时间一致性"""
    print("\n=== 测试时间一致性 ===")
    
    # 加载数据
    df = load_data_for_training('ETH', 'coin_data.db', 'ETHUSDT', '15m', 'spot', 
                               start_time='2024-12-01', end_time='2024-12-03')
    
    if df is None or len(df) < 50:
        print("❌ 测试数据不足")
        return False
    
    # 计算特征
    df_features = calculate_features(df, timeframe=15, db_path='coin_data.db', 
                                   symbol='ETHUSDT', market='spot')
    
    daily_feature_cols = [col for col in df_features.columns if col.startswith('daily_')]
    
    if not daily_feature_cols:
        print("⚠️  没有日线特征")
        return True
    
    # 检查同一天内的日线特征是否一致
    print("🔍 检查同一天内日线特征的一致性...")
    
    sample_feature = daily_feature_cols[0]
    
    # 按日期分组
    df_features['date'] = df_features.index.date
    
    inconsistent_days = 0
    total_days = 0
    
    for date, group in df_features.groupby('date'):
        if len(group) > 1:  # 一天有多个数据点
            total_days += 1
            feature_values = group[sample_feature].dropna()
            
            if len(feature_values) > 1:
                # 检查是否所有值都相同（同一天应该使用相同的前一日特征）
                if not feature_values.nunique() == 1:
                    print(f"⚠️  {date} 日内特征值不一致: {feature_values.unique()}")
                    inconsistent_days += 1
    
    if inconsistent_days == 0:
        print("✅ 时间一致性测试通过：同一天内使用相同的前一日特征")
        return True
    else:
        print(f"❌ 时间一致性测试失败：{inconsistent_days}/{total_days} 天存在不一致")
        return False

def main():
    """主函数"""
    print("🧪 数据泄露修复验证测试\n")
    
    tests = [
        ("数据泄露防护", test_data_leakage_prevention),
        ("特征可用性", test_feature_availability),
        ("时间一致性", test_temporal_consistency),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*60}")
            print(f"🔬 {test_name}")
            print('='*60)
            
            success = test_func()
            results.append((test_name, success))
            
            if success:
                print(f"✅ {test_name} - 通过")
            else:
                print(f"❌ {test_name} - 失败")
                
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n{'='*60}")
    print("📋 测试总结")
    print('='*60)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！数据泄露修复成功。")
    else:
        print("⚠️  部分测试失败，请检查数据泄露修复。")

if __name__ == "__main__":
    main()
