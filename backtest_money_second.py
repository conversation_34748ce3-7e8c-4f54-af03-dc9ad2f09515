# backtest_money_quick.py
# 使用已保存的模型，对SQLite中的历史数据进行带资金管理的真实环境模拟回测
# 支持 --start-time 和 --end-time 过滤数据
# (已根据用户建议优化止损逻辑)
# 新增 --quick 模式，预先计算所有特征以大幅加快回测速度
# 新增 --use-secondary-model 功能，使用一个副模型来过滤主模型信号

import pandas as pd
import numpy as np
import joblib
import json
import argparse
import os
import sqlite3
from datetime import datetime, timedelta
import pytz
from typing import Dict, List, Tuple, Optional

# 引入 get_coin_history 中的表名生成工具
from get_coin_history import get_table_name

try:
    from model_utils_815 import calculate_features, get_coin_config, get_output_dir
    from analyze_money import analyze_and_plot_results
except ImportError:
    print("错误: 请确保 model_utils.py 和 analyze_money.py 文件存在。")
    # 提供备用函数以便脚本能独立运行
    def calculate_features(df, timeframe): print("正在计算特征..."); return df
    def get_coin_config(coin): print(f"获取 {coin} 配置..."); return {'model_basename': f"{coin}_model", 'api_symbol': coin.upper()}
    def get_output_dir(basename): os.makedirs('./output', exist_ok=True); return './output'
    def analyze_and_plot_results(df, basename): print(f"分析结果并绘图: {basename}")

# 北京时区
BEIJING_TZ = pytz.timezone('Asia/Shanghai')

def to_beijing_time(timestamp):
    if isinstance(timestamp, pd.Timestamp):
        if timestamp.tz is None:
            timestamp = timestamp.tz_localize('UTC')
        return timestamp.tz_convert(BEIJING_TZ)
    elif isinstance(timestamp, datetime):
        if timestamp.tzinfo is None:
            timestamp = pytz.UTC.localize(timestamp)
        return timestamp.astimezone(BEIJING_TZ)
    return timestamp

def format_beijing_time(timestamp):
    beijing_time = to_beijing_time(timestamp)
    return beijing_time.strftime('%Y-%m-%d %H:%M:%S UTC+8')

def parse_time_input(time_str):
    if not time_str:
        return None
    try:
        formats = ['%Y-%m-%d %H:%M:%S', '%Y-%m-%d %H:%M', '%Y-%m-%d', '%m-%d %H:%M', '%m-%d']
        dt = None
        for fmt in formats:
            try:
                dt = datetime.strptime(time_str, fmt)
                break
            except ValueError:
                continue
        if dt is None:
            raise ValueError(f"无法解析时间格式: {time_str}")
        if dt.year == 1900:
            dt = dt.replace(year=datetime.now().year)
        beijing_dt = BEIJING_TZ.localize(dt)
        utc_dt = beijing_dt.astimezone(pytz.UTC)
        return pd.Timestamp(utc_dt).tz_localize(None)
    except Exception as e:
        print(f"时间解析错误: {e}")
        return None

def load_data_from_sqlite(db_path, coin, interval, market, price_multiplier=1.0, start_time=None, end_time=None):
    """从SQLite读取数据"""
    table_name = get_table_name(coin, interval, market)
    conn = sqlite3.connect(db_path)
    query = f"SELECT timestamp, open, high, low, close, volume FROM {table_name}"
    conditions = []
    params = []
    if start_time:
        conditions.append("timestamp >= ?")
        params.append(int(start_time.timestamp()))
    if end_time:
        conditions.append("timestamp <= ?")
        params.append(int(end_time.timestamp()))
    if conditions:
        query += " WHERE " + " AND ".join(conditions)
    query += " ORDER BY timestamp ASC"
    
    try:
        df = pd.read_sql_query(query, conn, params=params)
    except pd.io.sql.DatabaseError:
        print(f"❌ 数据库中找不到表: {table_name}")
        conn.close()
        return None

    conn.close()
    if df.empty:
        print(f"❌ 数据库表 '{table_name}' 中无符合条件的数据")
        return None
    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s', utc=True)
    df.set_index('timestamp', inplace=True)
    df = df.astype(float)
    if price_multiplier != 1.0:
        df[['open', 'high', 'low', 'close']] *= price_multiplier
    return df

class HistoricalBacktester:
    def __init__(self, model_file: str, config_file: str, initial_capital: float, risk_per_trade_pct: float, price_multiplier: float = 1.0, stop_loss_pct: float = None, secondary_model_file: str = None, secondary_config_file: str = None):
        self.price_multiplier = price_multiplier
        self.stop_loss_pct = stop_loss_pct

        # 加载主模型
        self.model = joblib.load(model_file)
        with open(config_file, 'r') as f:
            self.config = json.load(f)

        # 加载副模型 (如果提供)
        self.secondary_model = None
        self.secondary_config = None
        if secondary_model_file and secondary_config_file:
            self.secondary_model = joblib.load(secondary_model_file)
            with open(secondary_config_file, 'r') as f:
                self.secondary_config = json.load(f)
            print("✅ 副模型已成功加载。")

        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.risk_per_trade_pct = risk_per_trade_pct / 100.0

        self.active_predictions: Dict[str, dict] = {}
        self.completed_predictions: List[dict] = []
        self.prediction_counter = 0

        self.total_predictions = 0
        self.successful_predictions = 0
        self.failed_predictions = 0
        self.timeout_predictions = 0
        self.stop_loss_predictions = 0

        print(f"回测器初始化完成")
        print(f"主模型 ({self.config['timeframe_minutes']}m) 阈值: {self.config['best_threshold']:.3f}, 最大等待: {self.config['max_lookforward_minutes']}分钟")
        if self.secondary_model:
            print(f"副模型 ({self.secondary_config['timeframe_minutes']}m) 阈值: {self.secondary_config['best_threshold']:.3f}")
        if stop_loss_pct is not None:
            print(f"止损触发条件: {stop_loss_pct:.1f}%")
        print(f"初始资金: ${initial_capital:,.2f}")
        print(f"单次风险比例: {risk_per_trade_pct:.2f}%")

    def _make_single_prediction(self, latest_features_series: pd.Series, model, config: dict) -> Tuple[Optional[int], float, float]:
        """使用指定的模型和配置进行单次预测的内部辅助函数"""
        try:
            feature_list = config['feature_list']
            # 检查所需特征是否存在且不为NaN
            if any(f not in latest_features_series.index for f in feature_list) or latest_features_series[feature_list].isnull().any():
                return None, 0.0, 0.0
            
            latest_features_df = latest_features_series[feature_list].to_frame().T
            current_price = latest_features_series['close']
            probability = model.predict_proba(latest_features_df)[0, 1]
            best_threshold = config['best_threshold']
            
            guess = None
            if probability > best_threshold:
                guess = 1
            elif probability < (1 - best_threshold):
                guess = 0
            
            return guess, probability, current_price
        except Exception as e:
            print(f"从特征预测时发生错误: {e}")
            return None, 0.0, 0.0
            
    def get_main_prediction(self, latest_features_series: pd.Series) -> Tuple[Optional[int], float, float]:
        """使用主模型进行预测"""
        return self._make_single_prediction(latest_features_series, self.model, self.config)

    def get_secondary_prediction(self, latest_features_series: pd.Series) -> Tuple[Optional[int], float, float]:
        """使用副模型进行预测"""
        if not self.secondary_model:
            return None, 0.0, 0.0
        return self._make_single_prediction(latest_features_series, self.secondary_model, self.secondary_config)

    def add_prediction(self, guess: int, probability: float, price: float, timestamp: pd.Timestamp, current_idx: int):
        self.prediction_counter += 1
        prediction_id = f"pred_{self.prediction_counter:06d}"
        trade_risk_capital = self.current_capital * self.risk_per_trade_pct
        max_wait_candles = self.config['max_lookforward_minutes'] // self.config['timeframe_minutes']
        prediction = {
            'id': prediction_id, 'guess': guess, 'probability': probability,
            'start_price': price, 'start_timestamp': timestamp,
            'start_idx': current_idx, 'expire_idx': current_idx + max_wait_candles,
            'up_target': price * (1 + self.config['up_threshold']),
            'down_target': price * (1 - self.config['down_threshold']),
            'status': 'active', 'trade_risk_capital': trade_risk_capital,
            'max_loss_pct': 0.0, 'max_loss_price': price, 'max_loss_timestamp': timestamp
        }
        self.active_predictions[prediction_id] = prediction
        self.total_predictions += 1
        direction_str = f"先涨{self.config['up_threshold']*100:.1f}%" if guess == 1 else f"先跌{self.config['down_threshold']*100:.1f}%"
        print(f"[{format_beijing_time(timestamp)}] 新预测: {direction_str}, 信心: {probability:.3f}, 价格: {price:.4f}, 风险暴露: ${trade_risk_capital:,.2f}")

    def check_predictions(self, current_price: float, current_timestamp: pd.Timestamp, current_idx: int):
        completed_ids = []
        for pred_id, pred in self.active_predictions.items():
            if pred['status'] != 'active':
                continue
            current_price_change_pct = (current_price - pred['start_price']) / pred['start_price'] * 100
            if (pred['guess'] == 1 and current_price_change_pct < 0) or \
               (pred['guess'] == 0 and current_price_change_pct > 0):
                loss_pct = abs(current_price_change_pct)
                if loss_pct > pred['max_loss_pct']:
                    pred.update({'max_loss_pct': loss_pct, 'max_loss_price': current_price, 'max_loss_timestamp': current_timestamp})
            
            if self.stop_loss_pct is not None:
                should_stop_loss = (pred['guess'] == 1 and current_price_change_pct < -self.stop_loss_pct) or \
                                   (pred['guess'] == 0 and current_price_change_pct > self.stop_loss_pct)
                if should_stop_loss:
                    reason = f"止损(触发点:{-self.stop_loss_pct:.1f}%)"
                    self.complete_prediction(pred_id, -2, current_price, current_timestamp, current_idx, reason)
                    completed_ids.append(pred_id)
                    continue

            if current_price >= pred['up_target']:
                result, reason = (1, "达到上涨目标") if pred['guess'] == 1 else (0, "达到上涨目标(预测错误)")
                self.complete_prediction(pred_id, result, current_price, current_timestamp, current_idx, reason)
                completed_ids.append(pred_id)
            elif current_price <= pred['down_target']:
                result, reason = (1, "达到下跌目标") if pred['guess'] == 0 else (0, "达到下跌目标(预测错误)")
                self.complete_prediction(pred_id, result, current_price, current_timestamp, current_idx, reason)
                completed_ids.append(pred_id)
            elif current_idx >= pred['expire_idx']:
                self.complete_prediction(pred_id, -1, current_price, current_timestamp, current_idx, "超时")
                completed_ids.append(pred_id)
        
        for pred_id in completed_ids:
            if pred_id in self.active_predictions:
                del self.active_predictions[pred_id]

    def calculate_score(self, prediction: int, start_price: float, end_price: float, result: int):
        price_change_pct = (end_price - start_price) / start_price
        threshold = self.config.get('up_threshold', 0.01) if prediction == 1 else self.config.get('down_threshold', 0.01)
        
        score = price_change_pct / threshold if prediction == 1 else -price_change_pct / threshold
        return score

    def complete_prediction(self, pred_id: str, result: int, final_price: float, end_timestamp: pd.Timestamp, end_idx: int, reason: str, custom_score: float = None):
        if pred_id not in self.active_predictions: return
        pred = self.active_predictions[pred_id]
        pred['status'] = 'completed'
        
        score = custom_score if custom_score is not None else self.calculate_score(pred['guess'], pred['start_price'], final_price, result)
        profit_loss = pred['trade_risk_capital'] * score 
        self.current_capital += profit_loss

        if result == 1: self.successful_predictions += 1; status_str = "成功✅"
        elif result == 0: self.failed_predictions += 1; status_str = "失败❌"
        elif result == -2: self.stop_loss_predictions += 1; status_str = "止损🛑"
        else: self.timeout_predictions += 1; status_str = "超时⏰"

        completed_pred = {
            'PredictionID': pred['id'], 'StartTimestamp': format_beijing_time(pred['start_timestamp']),
            'EndTimestamp': format_beijing_time(end_timestamp), 'StartPrice': pred['start_price'],
            'EndPrice': final_price, 'PriceChangePct': ((final_price - pred['start_price']) / pred['start_price']) * 100,
            'MaxLossPct': pred['max_loss_pct'], 'MaxLossPrice': pred['max_loss_price'],
            'MaxLossTimestamp': format_beijing_time(pred['max_loss_timestamp']), 'Confidence': pred['probability'],
            'Prediction': pred['guess'], 'Result': result, 'Score': score,
            'ProfitLoss': profit_loss, 'CapitalAfter': self.current_capital,
            'Status': status_str, 'Reason': reason,
            'DurationMinutes': (end_idx - pred['start_idx']) * self.config['timeframe_minutes'],
            'UpTarget': pred['up_target'], 'DownTarget': pred['down_target']
        }
        self.completed_predictions.append(completed_pred)
        direction_str = f"先涨..." if pred['guess'] == 1 else f"先跌..."
        max_loss_info = f", 最大亏损: {pred['max_loss_pct']:.2f}%" if pred['max_loss_pct'] > 0 else ""
        print(f"[{format_beijing_time(end_timestamp)}] 预测完成: {direction_str} -> {status_str}, "
              f"得分: {score:+.2f}, 盈亏: ${profit_loss:+.2f}, "
              f"当前资金: ${self.current_capital:,.2f}{max_loss_info}")

def finalize_and_report(backtester: HistoricalBacktester, initial_capital: float, stop_loss_pct: float | None):
    """通用报告函数"""
    print("\n=== 回测结果摘要 ===")
    print(f"总预测数: {backtester.total_predictions}, 成功: {backtester.successful_predictions}, "
          f"失败: {backtester.failed_predictions}, 超时: {backtester.timeout_predictions}", end="")
    if stop_loss_pct is not None: print(f", 止损: {backtester.stop_loss_predictions}")
    else: print()
    
    final_capital = backtester.current_capital
    total_return_pct = (final_capital - initial_capital) / initial_capital * 100
    
    print(f"\n初始资金: ${initial_capital:,.2f}")
    print(f"最终资金: ${final_capital:,.2f}")
    print(f"总收益率: {total_return_pct:+.2f}%")

    if backtester.completed_predictions:
        results_df = pd.DataFrame(backtester.completed_predictions)
        # 确保时间戳列是datetime类型以便正确排序
        results_df['_sort_time'] = pd.to_datetime(results_df['StartTimestamp'].str.replace(' UTC\+8', '', regex=True))
        results_df = results_df.sort_values('_sort_time').drop('_sort_time', axis=1)
        
        output_filename = "backtest_money_log_quick.csv"
        results_df.to_csv(output_filename, index=False, float_format='%.4f')
        print(f"\n详细回测日志已保存到: {output_filename}")
        analyze_and_plot_results(results_df, "backtest_money_quick")
    else:
        print("\n没有生成任何预测结果。")

def run_quick_backtest(df_main: pd.DataFrame, df_secondary: Optional[pd.DataFrame], secondary_interval: Optional[str], **kwargs):
    """新增的快速回测方法，支持副模型过滤"""
    print("=== 开始快速回测(预计算特征) ===")
    
    backtester = HistoricalBacktester(
        kwargs['model_file'], kwargs['config_file'], kwargs['initial_capital'], 
        kwargs['risk_per_trade_pct'], kwargs['price_multiplier'], kwargs['stop_loss_pct'],
        kwargs.get('secondary_model_file'), kwargs.get('secondary_config_file')
    )

    # 1. 一次性计算所有特征
    print("正在为【主模型】预先计算所有时间序列的特征...")
    features_df_main = calculate_features(df_main, timeframe=backtester.config['timeframe_minutes'])
    print("主模型特征计算完成。")

    features_df_secondary = None
    if backtester.secondary_model and df_secondary is not None:
        print("正在为【副模型】预先计算所有时间序列的特征...")
        features_df_secondary = calculate_features(df_secondary, timeframe=backtester.secondary_config['timeframe_minutes'])
        print("副模型特征计算完成。")
    
    valid_features_df = features_df_main.dropna(subset=backtester.config['feature_list'])
    if valid_features_df.empty:
        print("❌ 计算特征后没有剩下任何有效数据行，无法进行回测。")
        return
    
    first_valid_index_pos = df_main.index.get_loc(valid_features_df.index[0])
    print(f"\n从索引 {first_valid_index_pos} 开始预测 (时间: {format_beijing_time(valid_features_df.index[0])})")
    print(f"将同时保持最多 {kwargs['max_active_predictions']} 笔活跃的投资。")

    # 2. 遍历数据进行回测
    for i in range(first_valid_index_pos, len(df_main)):
        current_timestamp = df_main.index[i]
        current_price = df_main.iloc[i]['close']
        
        backtester.check_predictions(current_price, current_timestamp, i)

        active_count = len([p for p in backtester.active_predictions.values() if p['status'] == 'active'])
        if active_count < kwargs['max_active_predictions']:
            if current_timestamp in features_df_main.index:
                main_features_series = features_df_main.loc[current_timestamp]
                main_guess, probability, pred_price = backtester.get_main_prediction(main_features_series)
                
                if main_guess is not None:
                    proceed = False
                    if backtester.secondary_model is None or features_df_secondary is None:
                        proceed = True # 没有副模型，直接通过
                    else:
                        # 寻找对应的副模型时间戳 (向下取整)
                        secondary_tf_min = int(secondary_interval.replace('m', ''))
                        secondary_ts = current_timestamp.floor(f'{secondary_tf_min}min')
                        
                        if secondary_ts in features_df_secondary.index:
                            secondary_features_series = features_df_secondary.loc[secondary_ts]
                            secondary_guess, _, _ = backtester.get_secondary_prediction(secondary_features_series)
                            
                            if main_guess == secondary_guess:
                                proceed = True
                                # print(f"[{format_beijing_time(current_timestamp)}] 主副模型判断一致 (主: {main_guess}, 副: {secondary_guess})。")
                            else:
                                pass
                                # print(f"[{format_beijing_time(current_timestamp)}] 过滤信号: 主模型判断为 {main_guess}, 副模型判断为 {secondary_guess}。")
                        # else: 副模型数据在该时间点不可用，跳过
                    
                    if proceed:
                        backtester.add_prediction(main_guess, probability, pred_price, current_timestamp, i)
    
    # 3. 结束所有仍在活跃的预测
    final_timestamp, final_price, final_idx = df_main.index[-1], df_main.iloc[-1]['close'], len(df_main) - 1
    for pred_id in list(backtester.active_predictions.keys()):
        backtester.complete_prediction(pred_id, -1, final_price, final_timestamp, final_idx, "数据结束-超时")
    
    finalize_and_report(backtester, kwargs['initial_capital'], kwargs['stop_loss_pct'])

# 注意：为简化，副模型过滤功能目前仅在 --quick 模式下实现。
# 如需在原始模式下支持，需要对 run_backtest_original 函数进行类似的修改。

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="使用资金管理模型对SQLite历史数据进行回测。")
    # 主要参数
    parser.add_argument("--coin", default="ETH", help="交易对，例如 BTC")
    parser.add_argument("--interval", default="5m", help="主模型K线间隔，例如 5m")
    parser.add_argument("--market", default="spot", choices=["spot", "futures"], help="市场类型")
    parser.add_argument("--db", default="coin_data.db", help="SQLite数据库路径")
    parser.add_argument("--start-time", help="回测开始时间(北京时间, YYYY-MM-DD HH:MM)")
    parser.add_argument("--end-time", help="回测结束时间(北京时间, YYYY-MM-DD HH:MM)")
    # 回测模式
    parser.add_argument("--quick", action='store_true', help="【推荐】启用快速回测模式 (预计算所有特征)")
    # 资金管理参数
    parser.add_argument("--stop-loss", type=float, help="止损百分比 (例如, 输入 2.5 表示 2.5%%)")
    parser.add_argument("--max-active-predictions", type=int, default=1000, help="最大同时活跃预测数")
    parser.add_argument("--initial-capital", type=float, default=10000, help="初始资金")
    parser.add_argument("--risk-per-trade", type=float, default=1.0, help="单次交易风险比例(%%)")
    # 主模型文件
    parser.add_argument("--model-file", help="(可选) 主模型文件路径 (.joblib)")
    parser.add_argument("--config-file", help="(可选) 主模型配置文件路径 (.json)")
    # 副模型过滤功能
    parser.add_argument("--use-secondary-model", action='store_true', help="启用副模型进行信号过滤")
    parser.add_argument("--secondary-interval", default="15m", help="副模型K线间隔 (例如 15m)")
    parser.add_argument("--secondary-model-file", help="(可选) 副模型文件路径 (.joblib)")
    parser.add_argument("--secondary-config-file", help="(可选) 副模型配置文件路径 (.json)")
    
    args = parser.parse_args()

    if not args.quick:
        print("⚠️ 警告: 副模型过滤功能目前仅在 --quick 模式下实现。本次运行将不使用副模型。")
        args.use_secondary_model = False
        # (此处可以添加对 run_backtest_original 的支持)

    start_time = parse_time_input(args.start_time) if args.start_time else None
    end_time = parse_time_input(args.end_time) if args.end_time else None

    # 获取主模型币种配置和文件路径
    coin_config_main = get_coin_config(args.coin)
    if coin_config_main is None:
        exit(1)
    
    model_file_main = args.model_file if args.model_file else f"models/{coin_config_main['model_basename']}_model.joblib"
    config_file_main = args.config_file if args.config_file else f"models/{coin_config_main['model_basename']}_config.json"
    
    if not os.path.exists(model_file_main) or not os.path.exists(config_file_main):
        print(f"❌ 错误: 主模型文件 '{model_file_main}' 或配置文件 '{config_file_main}' 不存在。")
        exit(1)

    # 加载主模型数据
    print(f"正在加载主模型数据 ({args.interval})...")
    df_main = load_data_from_sqlite(args.db, coin_config_main['api_symbol'], args.interval, args.market, price_multiplier=1.0, start_time=start_time, end_time=end_time)
    
    if df_main is None or df_main.empty:
        print("❌ 未加载到任何主模型数据，程序退出。")
        exit(1)

    backtest_params = {
        'model_file': model_file_main,
        'config_file': config_file_main,
        'initial_capital': args.initial_capital,
        'risk_per_trade_pct': args.risk_per_trade,
        'price_multiplier': 1.0,
        'stop_loss_pct': args.stop_loss,
        'max_active_predictions': args.max_active_predictions
    }

    df_secondary = None
    if args.use_secondary_model:
        # 获取副模型币种配置和文件路径
        model_file_secondary = args.secondary_model_file 
        config_file_secondary = args.secondary_config_file 
        
        if not os.path.exists(model_file_secondary) or not os.path.exists(config_file_secondary):
            print(f"❌ 错误: 副模型文件 '{model_file_secondary}' 或配置文件 '{config_file_secondary}' 不存在。")
            exit(1)
        
        # 加载副模型数据
        print(f"正在加载副模型数据 ({args.secondary_interval})...")
        df_secondary = load_data_from_sqlite(args.db, coin_config_main['api_symbol'], args.secondary_interval, args.market, price_multiplier=1.0, start_time=start_time, end_time=end_time)
        
        if df_secondary is None or df_secondary.empty:
            print("❌ 未加载到任何副模型数据，程序退出。")
            exit(1)
            
        backtest_params['secondary_model_file'] = model_file_secondary
        backtest_params['secondary_config_file'] = config_file_secondary
    
    # 目前只在 quick 模式下实现了副模型逻辑
    if args.quick:
        run_quick_backtest(df_main, df_secondary, args.secondary_interval, **backtest_params)
    else:
        # 原始模式的调用保持不变，可以按需扩展
        # run_backtest_original(model_file_main, config_file_main, df_main, **backtest_params)
        print("请使用 --quick 参数运行回测。原始逐条回测模式已不再维护。")
        exit(0)