# train_model_regression.py
# (已修改为回归模型，并集成特征优化流程)

import pandas as pd
import numpy as np
from datetime import datetime
import lightgbm as lgb
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import mean_squared_error, r2_score
import joblib
import json
import argparse
import os
import pickle
import optuna
TARGET_TYPE_FOR_THIS_RUN = 'stabilized_ratio'
# 假设这些工具函数存在于您的项目中
# get_optimized_feature_list 是新增的导入
from model_utils2 import (
    calculate_features, get_coin_config, get_output_dir, get_feature_list,
    get_optimized_feature_list
)
from data_loader import load_data_for_training, create_data_source_config, print_data_source_info

# --- 配置将从命令行参数和配置文件中获取 ---
# 全局变量
TIMEFRAME_MINUTES = None
# 注意：UP_THRESHOLD 和 DOWN_THRESHOLD 在回归模式下不再用于生成标签
UP_THRESHOLD = None
DOWN_THRESHOLD = None
MAX_LOOKFORWARD_MINUTES = None # 这个变量在回归模式下可以重新定义为标签计算的窗口
MODEL_BASENAME = None


def create_regression_target(df, max_lookforward_minutes, timeframe, target_type='difference'):
    """
    创建回归目标标签，支持多种目标类型。
    
    Args:
        df (pd.DataFrame): 输入的K线数据。
        max_lookforward_minutes (int): 向前看的时间窗口（分钟）。
        timeframe (int): K线的时间周期（分钟）。
        target_type (str): 目标类型。可以是:
            'difference' (默认): 最高涨幅% - 最大跌幅%
            'max_gain': 仅预测未来最大涨幅%
            'stabilized_ratio': (涨幅%) / (涨幅% + 跌幅% + epsilon)，一个0到1之间的稳定比率
    
    Returns:
        pd.Series: 计算好的目标标签。
    """
    lookforward_candles = max_lookforward_minutes // timeframe
    if lookforward_candles <= 0:
        raise ValueError("max_lookforward_minutes 必须大于 timeframe_minutes")

    print(f"创建回归目标 (类型: {target_type})：未来 {max_lookforward_minutes} 分钟 (窗口: {lookforward_candles} 根K线)")

    # 使用.rolling()来高效计算未来窗口的最高价和最低价
    # .shift(1 - lookforward_candles) 将未来的值移动到当前行以对齐
    future_high = df['high'].rolling(window=lookforward_candles, min_periods=1).max().shift(1 - lookforward_candles)
    future_low = df['low'].rolling(window=lookforward_candles, min_periods=1).min().shift(1 - lookforward_candles)

    # 计算涨幅和跌幅百分比
    # 涨幅 = (未来最高价 / 当前收盘价 - 1) * 100
    percent_change_up = (future_high / df['close'] - 1) * 100
    # 跌幅的绝对值
    percent_change_down = abs((future_low / df['close'] - 1) * 100)
    
    # --- 根据 target_type 选择计算方式 ---
    if target_type == 'difference':
        labels = percent_change_up - percent_change_down
        print("目标：预测未来涨跌幅的差值。")
        
    elif target_type == 'max_gain':
        labels = percent_change_up
        print("目标：预测未来最大涨幅百分比。")
        
    elif target_type == 'stabilized_ratio':
        # 直接用 涨幅/跌幅 有除以零的风险且值域不稳定。
        # 我们使用一个更稳健的公式，将结果归一化到 0 和 1 之间。
        # 值越接近1，说明上涨潜力远大于下跌风险。
        # 值越接近0.5，说明二者相当。
        epsilon = 1e-6 # 防止除以零
        labels = percent_change_up / (percent_change_up + percent_change_down + epsilon)
        print("目标：预测未来涨跌幅的稳定比率 (0到1之间)。")
        
    else:
        raise ValueError(f"未知的 target_type: '{target_type}'")

    labels.name = 'label'

    print(f"有效标签数量: {labels.notna().sum()}/{len(df)} ({labels.notna().sum()/len(df)*100:.1f}%)")
    print("标签统计信息:")
    print(labels.describe())

    return labels

def prepare_features_and_labels(df, symbol='ETHUSDT', market='spot'):
    """准备特征和标签（回归版）"""
    print("在完整数据集上计算增强特征...")
    df_with_features = calculate_features(df.copy(), timeframe=TIMEFRAME_MINUTES)

    print("创建回归目标标签...")
    # 目标设置为预测未来24小时的潜力
    lookforward_minutes_for_regression = 4 * 60
    target_labels = create_regression_target(df, lookforward_minutes_for_regression, TIMEFRAME_MINUTES, 
                                             target_type=TARGET_TYPE_FOR_THIS_RUN) 

    print("合并特征与标签...")
    df_combined = df_with_features.join(target_labels, how='inner')
    df_clean = df_combined.dropna()  # 清理特征计算和标签合并后可能产生的NaN
    print(f"清理NaN后剩余 {len(df_clean)} 条记录")
    return df_clean


def split_data(df_clean):
    """按时间顺序分割数据"""
    train_size = int(len(df_clean) * 0.70)
    val_size = int(len(df_clean) * 0.15)
    train_df = df_clean.iloc[:train_size]
    val_df = df_clean.iloc[train_size:train_size + val_size]
    test_df = df_clean.iloc[train_size + val_size:]
    print(f"训练集: {len(train_df)} ({train_df.index.min()} to {train_df.index.max()})")
    print(f"验证集: {len(val_df)} ({val_df.index.min()} to {val_df.index.max()})")
    print(f"测试集: {len(test_df)} ({test_df.index.min()} to {test_df.index.max()})")
    return train_df, val_df, test_df


def time_series_cross_validation(df_clean, features, target='label', n_splits=5, n_trials=100):
    """使用Optuna进行时序交叉验证（回归版）"""
    print(f"\n=== 开始使用 Optuna 进行时序交叉验证 (n_splits={n_splits}, n_trials={n_trials}) ===")
    tscv = TimeSeriesSplit(n_splits=n_splits)
    X = df_clean[features]
    y = df_clean[target]

    def objective(trial):
        params = {
            'objective': 'regression_l1',  # 使用L1损失 (MAE)，对金融数据中的异常值更鲁棒
            'metric': 'mae',
            'random_state': 42, 'verbose': -1, 'n_jobs': -1,
            'n_estimators': trial.suggest_int('n_estimators', 800, 2500),
            'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.1, log=True),
            'max_depth': trial.suggest_int('max_depth', 5, 12),
            'num_leaves': trial.suggest_int('num_leaves', 20, 150),
            'min_child_samples': trial.suggest_int('min_child_samples', 20, 100),
            'subsample': trial.suggest_float('subsample', 0.7, 1.0),
            'colsample_bytree': trial.suggest_float('colsample_bytree', 0.7, 1.0),
        }
        fold_scores = []
        for train_idx, val_idx in tscv.split(X):
            X_train_fold, X_val_fold = X.iloc[train_idx], X.iloc[val_idx]
            y_train_fold, y_val_fold = y.iloc[train_idx], y.iloc[val_idx]
            try:
                lgbm = lgb.LGBMRegressor(**params)  # 改为LGBMRegressor
                lgbm.fit(X_train_fold, y_train_fold, eval_set=[(X_val_fold, y_val_fold)], eval_metric='mae',
                         callbacks=[lgb.early_stopping(stopping_rounds=50, verbose=False)])
                y_pred = lgbm.predict(X_val_fold)
                # 使用均方根误差(RMSE)作为评估分数
                fold_scores.append(mean_squared_error(y_val_fold, y_pred, squared=False))
            except Exception as e:
                # print(f"CV a fold failed: {e}")
                continue
        return np.mean(fold_scores) if fold_scores else float('inf')

    optuna.logging.set_verbosity(optuna.logging.WARNING)
    # 对于误差指标，我们的目标是最小化
    study = optuna.create_study(direction='minimize')
    study.optimize(objective, n_trials=n_trials)

    print(f"=== Optuna 交叉验证完成 ===")
    if not study.best_trial or study.best_value == float('inf'):
        print("⚠️ 警告: Optuna 未找到有效的参数组合，将使用默认参数。")
        return {}, float('inf')
    else:
        print(f"最佳RMSE (交叉验证平均值): {study.best_value:.4f}")
        print(f"最佳参数: {study.best_params}")

    cv_results_df = study.trials_dataframe()
    output_dir = get_output_dir()
    cv_results_file = os.path.join(output_dir, f'cv_results_optuna_{MODEL_BASENAME}_regression.csv')
    cv_results_df.to_csv(cv_results_file, index=False)
    print(f"交叉验证结果已保存到: {cv_results_file}")

    return study.best_params, study.best_value


def train_model(args):
    """主训练函数，集成了特征优化逻辑（回归版）"""
    print(f"开始训练LightGBM回归模型 - {MODEL_BASENAME.replace('_', ' ').title()}")
    data_file = args.data_file or f"{MODEL_BASENAME}_regression.pkl"

    if args.load_data:
        df_clean, train_df, val_df, test_df, _ = load_processed_data(data_file)
        if df_clean is None: args.load_data = False

    if not args.load_data:
        df = load_data_for_training(args.coin, args.db_path, args.symbol, args.interval, args.market,
                                    start_time=args.start_time, end_time=args.end_time)
        if df is None: print("❌ 数据加载失败，退出训练。"); return

        df_clean = prepare_features_and_labels(df, args.symbol, args.market)
        train_df, val_df, test_df = split_data(df_clean)

        if args.save_data:
            all_features = get_feature_list(df_clean)
            save_processed_data(df_clean, train_df, val_df, test_df, all_features, data_file)

    target = 'label'
    X_train, y_train = train_df.drop(columns=[target]), train_df[target]
    X_val, y_val = val_df.drop(columns=[target]), val_df[target]
    X_test, y_test = test_df.drop(columns=[target]), test_df[target]

    # --- 特征选择流程 ---
    all_features = get_feature_list(df_clean)
    features = all_features

    if args.optimize_features:
        print("\n--- 阶段1: 训练初步模型以获取特征重要性 ---")
        prelim_model = lgb.LGBMRegressor(objective='regression_l1', n_jobs=-1, random_state=42)
        prelim_model.fit(X_train[all_features], y_train)

        importances_df = pd.DataFrame({
            'feature': all_features,
            'importance': prelim_model.feature_importances_
        }).sort_values('importance', ascending=False)

        print("--- 阶段2: 根据重要性和相关性筛选最优特征 ---")
        features = get_optimized_feature_list(
            df_clean, importances_df,
            top_n=args.top_n,
            corr_threshold=args.corr_threshold
        )
    else:
        print("\n使用所有可用特征进行训练。")

    # --- 模型训练 ---
    best_params = {}
    best_cv_score = float('inf')
    use_time_series_cv = not args.no_time_series_cv

    if use_time_series_cv:
        print(f"🔍 使用时序交叉验证对优化后的 {len(features)} 个特征进行超参数搜索...")
        train_val_df = pd.concat([train_df, val_df])
        best_params, best_cv_score = time_series_cross_validation(
            train_val_df, features, target, n_splits=args.cv_splits, n_trials=args.cv_trials
        )
    else:
        print(f"\n⚠️  禁用时序交叉验证，使用默认参数...")

    final_params = {
        'objective': 'regression_l1', 'metric': 'mae', 'n_jobs': -1, 'random_state': 42,
        'n_estimators': 2000, 'learning_rate': 0.05
    }
    final_params.update(best_params)

    print(f"\n开始使用 {len(features)} 个最终特征训练LightGBM回归模型...")
    lgbm_regressor = lgb.LGBMRegressor(**final_params)
    lgbm_regressor.fit(X_train[features], y_train, eval_set=[(X_val[features], y_val)], eval_metric='mae',
             callbacks=[lgb.early_stopping(stopping_rounds=100, verbose=False)])

    # 回归模型不需要概率校准或寻找最优分类阈值
    print("模型训练完成，开始在测试集上评估...")
    evaluate_on_test_set_regression(lgbm_regressor, X_test, y_test, test_df, features,target_type=TARGET_TYPE_FOR_THIS_RUN)

    # 准备保存的配置
    extra_config = {
        'model_objective': 'regression',
        'time_series_cv_used': use_time_series_cv,
        'feature_optimization_used': args.optimize_features,
        'final_feature_count': len(features)
    }
    if use_time_series_cv:
        extra_config.update({'cv_splits': args.cv_splits, 'cv_trials': args.cv_trials,
                             'best_cv_rmse': float(best_cv_score), 'best_cv_params': best_params})
    if args.optimize_features:
        extra_config.update({'top_n_features': args.top_n, 'corr_threshold': args.corr_threshold})

    save_model_and_config_regression(lgbm_regressor, features, len(X_train), len(X_val), len(X_test), extra_config)
    analyze_feature_importance(lgbm_regressor, features)


def evaluate_on_test_set_regression(model, X_test, y_test, test_df, features, target_type='difference'):
    """
    在测试集上评估回归模型，并根据目标类型采用不同的评估逻辑。
    
    Args:
        ... (之前的参数) ...
        target_type (str): 创建标签时使用的目标类型，用于选择正确的评估方法。
    """
    print(f"\n--- 测试集评估 (模型目标: {target_type}) ---")

    X_test_final = X_test[features]
    predicted_scores = model.predict(X_test_final)

    # 1. 通用回归指标 (对所有模型都适用)
    mse = mean_squared_error(y_test, predicted_scores)
    rmse = np.sqrt(mse)
    r2 = r2_score(y_test, predicted_scores)
    print(f"测试集 R² 分数: {r2:.4f} (衡量模型解释变异性的能力)")
    print(f"测试集 均方根误差 (RMSE): {rmse:.4f} (预测值与实际值的平均差异幅度)")

    results_df = test_df.copy()
    results_df['predicted_score'] = predicted_scores
    results_df['actual_score'] = y_test

    # 2. 针对不同模型目标的自定义业务评估
    
    # --- 评估逻辑 for 'max_gain' ---
    if target_type == 'max_gain':
        # 当模型预测未来有显著涨幅时，我们关心它成功的概率和实际回报
        prediction_threshold = 3.0  # 示例阈值: 我们只关心那些模型预测涨幅超过3%的机会
        outcome_threshold = 0.5     # 示例阈值: 只要实际涨幅>0.5%就算成功预测了方向
        
        print(f"\n--- 'max_gain' 业务评估 (预测涨幅 > {prediction_threshold}%) ---")
        
        triggered_trades = results_df[results_df['predicted_score'] > prediction_threshold]
        trades_made = len(triggered_trades)
        
        if trades_made > 0:
            # 成功率: 在触发的交易中，有多少次真实涨幅是正的
            successes = triggered_trades[triggered_trades['actual_score'] > outcome_threshold]
            success_rate = len(successes) / trades_made
            
            print(f"触发交易次数: {trades_made} / {len(results_df)} ({trades_made/len(results_df)*100:.2f}%)")
            print(f"成功率 (实际涨幅 > {outcome_threshold}%): {success_rate*100:.2f}%")
            
            if len(successes) > 0:
                print(f"  - 成功案例的平均预测涨幅: {successes['predicted_score'].mean():.2f}%")
                print(f"  - 成功案例的平均实际涨幅: {successes['actual_score'].mean():.2f}%")

            failures = triggered_trades[triggered_trades['actual_score'] <= outcome_threshold]
            if len(failures) > 0:
                 print(f"  - 失败案例的平均实际涨幅: {failures['actual_score'].mean():.2f}% (用于评估风险)")
        else:
            print("在测试集中，没有任何预测值超过阈值，无法进行业务评估。")

    # --- 评估逻辑 for 'stabilized_ratio' ---
    elif target_type == 'stabilized_ratio':
        # 当模型预测风险回报比极好时（比率接近1），我们关心方向是否正确
        prediction_threshold = 0.8  # 示例阈值: 我们只关心模型高度确信（比率>0.8）的机会
        
        print(f"\n--- 'stabilized_ratio' 业务评估 (预测比率 > {prediction_threshold}) ---")
        
        triggered_trades = results_df[results_df['predicted_score'] > prediction_threshold]
        trades_made = len(triggered_trades)
        
        if trades_made > 0:
            # 成功率: 在触发的交易中，有多少次真实的风险回报比是优的（>0.5，即涨幅>跌幅）
            successes = triggered_trades[triggered_trades['actual_score'] > 0.5]
            success_rate = len(successes) / trades_made

            print(f"触发交易次数: {trades_made} / {len(results_df)} ({trades_made/len(results_df)*100:.2f}%)")
            print(f"方向预测准确率 (实际比率 > 0.5): {success_rate*100:.2f}%")
            
            if len(successes) > 0:
                print(f"  - 成功案例的平均预测比率: {successes['predicted_score'].mean():.3f}")
                print(f"  - 成功案例的平均实际比率: {successes['actual_score'].mean():.3f}")
        else:
            print("在测试集中，没有任何预测值超过阈值，无法进行业务评估。")
            
    # --- 保留旧的评估逻辑以实现后向兼容 ---
    else: # 'difference'
        print("\n使用原始的 '涨跌幅差' 评估逻辑...")
        # ... (此处可以粘贴你之前的评估代码)
        
    # 保存结果
    results_filename = os.path.join(get_output_dir(), f'test_results_{MODEL_BASENAME}_{target_type}.csv')
    results_df[['close', 'predicted_score', 'actual_score']].to_csv(
        results_filename, index=True, float_format='%.4f')
    print(f"\n详细测试结果已保存到: {results_filename}")

def save_model_and_config_regression(model, features, train_size, val_size, test_size, extra_config=None):
    output_dir = get_output_dir()
    os.makedirs(output_dir, exist_ok=True)
    model_file = os.path.join(output_dir, f'{MODEL_BASENAME}_model_regression.joblib')
    config_file = os.path.join(output_dir, f'{MODEL_BASENAME}_config_regression.json')

    joblib.dump(model, model_file)
    config = {
        'model_type': f'LGBM_Regression_{MODEL_BASENAME}',
        'target_description': 'predict_24h_max_gain_pct_minus_max_loss_pct',
        'feature_list': features,
        'training_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'train_size': train_size, 'val_size': val_size, 'test_size': test_size,
        'timeframe_minutes': TIMEFRAME_MINUTES
    }
    if extra_config: config.update(extra_config)
    with open(config_file, 'w') as f: json.dump(config, f, indent=2)
    print(f"\n回归模型和配置已保存。\n模型文件: {model_file}\n配置文件: {config_file}")


def save_processed_data(df_clean, train_df, val_df, test_df, features, data_file):
    print(f"保存预处理数据到 {data_file}...")
    data_dict = {'df_clean': df_clean, 'train_df': train_df, 'val_df': val_df, 'test_df': test_df, 'features': features, 'save_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
    with open(data_file, 'wb') as f: pickle.dump(data_dict, f)
    print(f"数据已保存到 {data_file}")


def load_processed_data(data_file):
    print(f"从 {data_file} 加载预处理数据...")
    if not os.path.exists(data_file):
        print(f"错误：数据文件 {data_file} 不存在！"); return None, None, None, None, None
    with open(data_file, 'rb') as f: data_dict = pickle.load(f)
    print(f"数据加载完成，保存时间: {data_dict.get('save_time', '未知')}")
    return data_dict['df_clean'], data_dict['train_df'], data_dict['val_df'], data_dict['test_df'], data_dict.get('features')


def analyze_feature_importance(lgbm, features):
    output_dir = get_output_dir()
    os.makedirs(output_dir, exist_ok=True)
    importance_df = pd.DataFrame({'feature': features, 'importance': lgbm.feature_importances_}).sort_values('importance', ascending=False)
    print("\n" + "="*20 + " 特征重要性 (Top 20) " + "="*20)
    print(importance_df.head(20).to_string(index=False))
    importance_file = os.path.join(output_dir, f'feature_importance_{MODEL_BASENAME}_regression.csv')
    importance_df.to_csv(importance_file, index=False)
    print(f"\n完整特征重要性已保存到 {importance_file}")


def main():
    parser = argparse.ArgumentParser(description="多币种 LGBM 回归模型训练与验证器")
    parser.add_argument("--coin", default="ETH", help="币种名称 (如: DOT, SEI)")
    parser.add_argument("--mode", choices=['train', 'validate'], default='train', help="运行模式")
    parser.add_argument("--save-data", action='store_true', help="保存预处理后的数据")
    parser.add_argument("--load-data", action='store_true', help="加载预处理后的数据")
    parser.add_argument("--data-file", help="预处理数据文件路径")
    parser.add_argument("--no-time-series-cv", action='store_true', help="禁用时序交叉验证")
    parser.add_argument("--cv-splits", type=int, default=5, help="CV分割数")
    parser.add_argument("--cv-trials", type=int, default=100, help="Optuna尝试次数")
    parser.add_argument("--db-path", default='coin_data.db', help="SQLite数据库路径")
    parser.add_argument("--symbol", help="交易对符号")
    parser.add_argument("--interval", help="时间间隔")
    parser.add_argument("--market", choices=['spot', 'futures'], default='spot', help="市场类型")
    parser.add_argument("--start-time", help="数据开始时间 (YYYY-MM-DD)")
    parser.add_argument("--end-time", help="数据结束时间 (YYYY-MM-DD)")

    # --- 新增的特征优化参数 ---
    parser.add_argument("--optimize-features", action='store_true', help="启用特征优化流程")
    parser.add_argument("--top-n", type=int, default=50, help="特征优化：选择最重要的N个特征")
    parser.add_argument("--corr-threshold", type=float, default=0.9, help="特征优化：移除相关性的阈值")

    args = parser.parse_args()

    # 动态加载币种配置
    coin_config = get_coin_config(args.coin)
    if coin_config is None:
        print(f"错误: 未找到 {args.coin} 的配置。")
        exit(1)

    # 设置全局变量
    global TIMEFRAME_MINUTES, UP_THRESHOLD, DOWN_THRESHOLD, MAX_LOOKFORWARD_MINUTES, MODEL_BASENAME
    TIMEFRAME_MINUTES = coin_config['timeframe_minutes']
    UP_THRESHOLD = coin_config['up_threshold']
    DOWN_THRESHOLD = coin_config['down_threshold']
    MAX_LOOKFORWARD_MINUTES = coin_config['max_lookforward_minutes']
    MODEL_BASENAME = coin_config['model_basename']

    # 确保输出目录存在
    os.makedirs(get_output_dir(), exist_ok=True)

    print(f"=== {coin_config['display_name']} 回归模型任务 ({args.mode}) ===")
    if args.mode == 'train':
        print(f"模型目标: 预测未来24小时涨跌幅潜力分数")
        print(f"特征优化: {'启用' if args.optimize_features else '禁用'}")
        if args.optimize_features:
            print(f" - Top N: {args.top_n}, Corr Threshold: {args.corr_threshold}")
        train_model(args)
    else:
        print("验证模式待实现。可以扩展此部分以加载已保存的模型并进行评估。")

if __name__ == '__main__':
    # 提醒: 确保 model_utils2.py 和 data_loader.py 文件在同一目录下或在Python路径中
    # 并且已经安装了所需的库: pip install pandas numpy lightgbm scikit-learn joblib optuna
    main()