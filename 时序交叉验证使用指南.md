# 时序交叉验证使用指南

## 概述

我们已经成功在 `train.py` 中集成了 `sklearn.model_selection.TimeSeriesSplit` 来实施时序交叉验证，这是解决时间序列机器学习模型过拟合和提高泛化能力的关键步骤。

## 主要改进

### 1. 新增功能
- ✅ 集成 `TimeSeriesSplit` 进行时序交叉验证
- ✅ 自动超参数网格搜索
- ✅ 防止数据泄露的时间序列分割
- ✅ 交叉验证结果保存和分析
- ✅ 可配置的验证分割数量

### 2. 核心优势
- **防止数据泄露**: 确保训练集永远在验证集之前，符合时间序列的时间顺序
- **更可靠的模型评估**: 通过多个时间窗口验证模型性能
- **自动超参数优化**: 系统性搜索最佳参数组合
- **稳定性评估**: 评估模型在不同时间段的表现一致性

## 使用方法

### 基本用法（启用时序交叉验证）
```bash
# 默认启用时序交叉验证，使用5折分割
python train.py --coin ETH

# 自定义分割数量
python train.py --coin ETH --cv-splits 3

# 指定时间范围
python train.py --coin ETH --start-time "2024-01-01" --end-time "2024-12-31"
```

### 禁用时序交叉验证（使用原始方法）
```bash
# 如果需要快速训练或调试，可以禁用交叉验证
python train.py --coin ETH --no-time-series-cv
```

### 完整参数示例
```bash
python train.py \
  --coin ETH \
  --cv-splits 5 \
  --save-data \
  --start-time "2024-01-01" \
  --end-time "2024-12-31" \
  --db-path coin_data.db
```

## 超参数搜索空间

当前搜索的超参数包括：

```python
param_grid = {
    'n_estimators': [1000, 1500, 2000],      # 树的数量
    'learning_rate': [0.03, 0.05, 0.08],     # 学习率
    'max_depth': [6, 8, 10],                 # 树的最大深度
    'num_leaves': [31, 63, 127],             # 叶子节点数量
    'min_child_samples': [20, 50, 100],      # 叶子节点最小样本数
    'subsample': [0.8, 0.9, 1.0],           # 样本采样比例
    'colsample_bytree': [0.8, 0.9, 1.0]     # 特征采样比例
}
```

总共 **189 种参数组合**，系统会自动找到最佳组合。

## 输出文件

### 1. 交叉验证结果
- 文件: `cv_results_{MODEL_BASENAME}.json`
- 内容: 每种参数组合的详细评估结果
- 包含: 平均得分、标准差、AUC值等

### 2. 模型配置
- 文件: `{MODEL_BASENAME}_config.json`
- 新增字段:
  ```json
  {
    "time_series_cv_used": true,
    "cv_splits": 5,
    "best_cv_score": 85.2,
    "best_cv_params": {
      "n_estimators": 1500,
      "learning_rate": 0.05,
      ...
    }
  }
  ```

## 时序交叉验证原理

### 传统交叉验证 vs 时序交叉验证

**❌ 传统 K-Fold（错误）:**
```
Fold 1: Train[1,3,4,5] Test[2]     # 用未来数据训练预测过去
Fold 2: Train[1,2,4,5] Test[3]     # 数据泄露！
```

**✅ 时序交叉验证（正确）:**
```
Fold 1: Train[1,2]     Test[3]     # 只用过去预测未来
Fold 2: Train[1,2,3]   Test[4]     # 时间顺序正确
Fold 3: Train[1,2,3,4] Test[5]     # 无数据泄露
```

### 评估指标

我们使用自定义的交易得分作为主要评估指标：
- **正确交易**: +1 分
- **错误交易**: -1 分
- **放弃交易**: 0 分

这个指标更贴近实际交易场景，比单纯的准确率或AUC更有实用价值。

## 性能考虑

### 计算时间
- 时序交叉验证会显著增加训练时间
- 189种参数组合 × 5折验证 = 945次模型训练
- 建议在有充足计算资源时使用

### 内存使用
- 需要同时保存多个模型的结果
- 建议确保有足够的内存空间

### 优化建议
1. **减少参数搜索空间**: 如果计算资源有限，可以减少参数组合
2. **减少交叉验证折数**: 从5折减少到3折
3. **使用更小的数据集**: 先在小数据集上验证，再用全量数据

## 最佳实践

### 1. 数据准备
- 确保数据按时间顺序排列
- 数据量建议至少6个月以上
- 检查数据质量和连续性

### 2. 参数调优
- 先用较少的参数组合快速验证
- 根据初步结果缩小搜索范围
- 关注模型的稳定性而非单一指标

### 3. 结果分析
- 查看交叉验证结果的标准差
- 分析不同时间段的表现差异
- 关注过拟合的迹象

## 故障排除

### 常见问题

1. **内存不足**
   ```bash
   # 减少交叉验证折数
   python train.py --coin ETH --cv-splits 3
   ```

2. **训练时间过长**
   ```bash
   # 禁用交叉验证进行快速测试
   python train.py --coin ETH --no-time-series-cv
   ```

3. **参数搜索空间过大**
   - 修改 `time_series_cross_validation()` 函数中的 `param_grid`
   - 减少参数选项数量

### 调试模式
```bash
# 使用小数据集快速测试
python train.py --coin ETH --start-time "2024-12-01" --end-time "2024-12-31"
```

## 下一步改进

1. **并行化**: 使用多进程加速参数搜索
2. **早停机制**: 在交叉验证中添加早停
3. **贝叶斯优化**: 替换网格搜索为更智能的优化算法
4. **模型集成**: 结合多个最佳模型的预测结果

---

**重要提醒**: 时序交叉验证是时间序列机器学习的最佳实践，虽然会增加计算成本，但能显著提高模型的可靠性和泛化能力。建议在正式训练中始终启用此功能。
