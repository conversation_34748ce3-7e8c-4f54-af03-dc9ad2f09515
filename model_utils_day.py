# model_utils.py
# 公共函数库，用于数据加载、特征计算和标签生成
# 只支持SQLite数据源

import pandas as pd
import numpy as np
import json
import os
import sqlite3
from typing import Optional, Union, List

# ==============================================================================
# 1. 配置加载模块 (来自版本1的稳健设计)
# ==============================================================================

def load_config(config_file: str = 'config.json') -> Optional[dict]:
    """加载JSON配置文件"""
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        return config
    except FileNotFoundError:
        print(f"❌ 错误: 配置文件 '{config_file}' 未找到。")
        return None
    except json.JSONDecodeError:
        print(f"❌ 错误: 配置文件 '{config_file}' 格式错误。")
        return None
    except Exception as e:
        print(f"❌ 加载配置文件时发生未知错误: {e}")
        return None

def get_coin_config(coin_name: str, config_file: str = 'config.json') -> Optional[dict]:
    """获取指定币种的配置"""
    config = load_config(config_file)
    if config is None:
        return None

    if 'coin_configs' not in config or coin_name not in config['coin_configs']:
        print(f"❌ 错误: 配置中未找到币种 '{coin_name}'")
        available_coins = list(config.get('coin_configs', {}).keys())
        if available_coins:
            print(f"可用币种: {available_coins}")
        return None

    return config['coin_configs'][coin_name]

def get_output_dir(config_file: str = 'config.json') -> Optional[str]:
    """获取输出目录"""
    config = load_config(config_file)
    if config is None:
        return None
    
    output_dir = config.get('output_dir', 'output')
    os.makedirs(output_dir, exist_ok=True)
    return output_dir

# ==============================================================================
# 2. 数据加载模块 (只支持SQLite数据源)
# ==============================================================================

def get_table_name(symbol: str, interval: str, market: str = "spot") -> str:
    """
    根据交易对、间隔和市场类型生成表名 (保留V1版本实现)
    """
    # 移除特殊字符，确保表名合法
    safe_symbol = symbol.replace('/', '_').replace('-', '_')
    # 转换时间单位以匹配数据库表名
    safe_interval = interval.replace('m', 'min').replace('h', 'hour').replace('d', 'day')
    return f"{safe_symbol}_{safe_interval}_{market}"



def load_from_sqlite(db_path: str, symbol: str, interval: str, market: str = "spot", 
                     start_time: Optional[int] = None, end_time: Optional[int] = None,
                     price_multiplier: float = 1.0) -> Optional[pd.DataFrame]:
    """从SQLite数据库加载并预处理数据"""
    if not os.path.exists(db_path):
        print(f"❌ 错误: SQLite数据库文件不存在: {db_path}")
        return None
    
    table_name = get_table_name(symbol, interval, market)
    print(f"📊 从SQLite数据库加载数据 (表: {table_name})")
    
    try:
        with sqlite3.connect(db_path) as conn:
            # 假设数据库列名为 timestamp, open, high, low, close, volume
            # 注意：这里的select列名要和数据库中的一致
            query = f"SELECT timestamp as Timestamp, open as Open, high as High, low as Low, close as Close, volume as Volume FROM {table_name} WHERE 1=1"
            params = []
            if start_time:
                query += " AND timestamp >= ?"
                params.append(start_time)
            if end_time:
                query += " AND timestamp <= ?"
                params.append(end_time)
            query += " ORDER BY timestamp"
            
            df = pd.read_sql_query(query, conn, params=params)

        if df.empty:
            print(f"⚠️ 警告: 在表 {table_name} 中没有找到符合条件的数据")
            return pd.DataFrame() # 返回空的DataFrame
        
        df = _preprocess_loaded_df(df, price_multiplier)
        print(f"✅ SQLite数据加载完成，共 {len(df)} 条记录")
        print(f"📅 时间范围: {df.index.min()} -> {df.index.max()}")
        return df
        
    except Exception as e:
        print(f"❌ 加载SQLite数据时出错: {e}")
        return None

def load_and_prepare_data(data_source: dict, price_multiplier: float = 1.0) -> Optional[pd.DataFrame]:
    """统一的数据加载入口，只支持SQLite"""
    print("🔄 开始加载和准备数据...")
    if isinstance(data_source, dict) and data_source.get('type') == 'sqlite':
        return load_from_sqlite(
            db_path=data_source['db_path'],
            symbol=data_source['symbol'],
            interval=data_source['interval'],
            market=data_source.get('market', 'spot'),
            start_time=data_source.get('start_time'),
            end_time=data_source.get('end_time'),
            price_multiplier=price_multiplier
        )
    else:
        print("❌ 错误: 只支持SQLite数据源，请提供包含'type': 'sqlite'的配置字典。")
        return None

# ==============================================================================
# 3. 标签生成模块 (来自版本1的清晰逻辑)
# ==============================================================================

def create_percentage_target(df: pd.DataFrame, up_threshold: float, down_threshold: float, 
                             max_lookforward_minutes: int, timeframe: int) -> pd.Series:
    """为回测数据生成真实标签 (先触碰哪个阈值)"""
    max_lookforward_candles = max_lookforward_minutes // timeframe
    print(f"🎯 为数据生成标签 (向前看 {max_lookforward_candles} 根K线, 涨: {up_threshold:.2%}, 跌: {down_threshold:.2%})")

    labels = []
    valid_indices = []
    
    close_prices = df['close'].values
    n = len(close_prices)

    for i in range(n - max_lookforward_candles):
        current_price = close_prices[i]
        up_target = current_price * (1 + up_threshold)
        down_target = current_price * (1 - down_threshold)
        label = np.nan # 默认为NaN

        for j in range(1, max_lookforward_candles + 1):
            future_price = close_prices[i + j]
            if future_price >= up_target:
                label = 1  # 上涨目标先到达
                break
            elif future_price <= down_target:
                label = 0  # 下跌目标先到达
                break
        
        if not np.isnan(label):
            labels.append(label)
            valid_indices.append(df.index[i])

    label_series = pd.Series(data=labels, index=valid_indices, name='ActualResult', dtype='int8')
    print(f"✅ 成功生成 {len(label_series)} 个有效标签。")
    return label_series

# ==============================================================================
# 4. 特征工程模块 (融合版本1和2的精华)
# ==============================================================================

def get_standardized_kline_features(df: pd.DataFrame, atr_period: int = 14, epsilon: float = 1e-9) -> pd.DataFrame:
    """
    计算基于ATR标准化的K线特征 (来自版本2的核心改进)
    ATR标准化可以消除价格绝对值对特征的影响，增强特征在不同币种和时期的稳健性。
    """
    # 1. 计算ATR (平均真实波幅)
    high_low = df['high'] - df['low']
    high_prev_close = abs(df['high'] - df['close'].shift(1))
    low_prev_close = abs(df['low'] - df['close'].shift(1))
    
    # TR (真实波幅) 是三者中的最大值
    tr = pd.concat([high_low, high_prev_close, low_prev_close], axis=1).max(axis=1)
    df[f'atr_{atr_period}'] = tr.rolling(window=atr_period).mean()
    
    # 2. 计算标准化的K线形态特征
    price_range = df['high'] - df['low']
    body_size = abs(df['close'] - df['open'])

    # 将K线实体大小和振幅用ATR进行归一化
    df[f'body_div_atr_{atr_period}'] = body_size / (df[f'atr_{atr_period}'] + epsilon)
    df[f'range_div_atr_{atr_period}'] = price_range / (df[f'atr_{atr_period}'] + epsilon)
    
    # 计算上影线和下影线长度，并用ATR归一化
    df[f'upper_shadow_div_atr_{atr_period}'] = (df['high'] - df[['open', 'close']].max(axis=1)) / (df[f'atr_{atr_period}'] + epsilon)
    df[f'lower_shadow_div_atr_{atr_period}'] = (df[['open', 'close']].min(axis=1) - df['low']) / (df[f'atr_{atr_period}'] + epsilon)

    return df


def load_daily_data(db_path: str = 'coin_data.db', symbol: str = 'ETHUSDT', market: str = 'spot') -> pd.DataFrame:
    """
    加载日线数据用于多时间尺度特征计算
    """
    import sqlite3

    table_name = f"{symbol}_1day_{market}"

    try:
        conn = sqlite3.connect(db_path)
        query = f"""
        SELECT timestamp, open, high, low, close, volume
        FROM {table_name}
        ORDER BY timestamp ASC
        """

        daily_df = pd.read_sql_query(query, conn)
        conn.close()

        if daily_df.empty:
            print(f"⚠️  警告: 日线数据表 {table_name} 为空")
            return None

        # 转换时间戳为datetime
        daily_df['timestamp'] = pd.to_datetime(daily_df['timestamp'], unit='s')
        daily_df.set_index('timestamp', inplace=True)

        print(f"📊 成功加载日线数据: {len(daily_df)} 条记录")
        print(f"📅 日线数据时间范围: {daily_df.index[0]} 到 {daily_df.index[-1]}")

        return daily_df

    except Exception as e:
        print(f"❌ 加载日线数据失败: {e}")
        return None

def calculate_market_regime_features(daily_df: pd.DataFrame) -> pd.DataFrame:
    """
    基于日线数据计算市场状态特征
    """
    if daily_df is None or len(daily_df) < 50:
        print("⚠️  日线数据不足，跳过市场状态特征计算")
        return pd.DataFrame()

    print("🏛️  计算市场状态特征...")
    epsilon = 1e-9
    regime_features = pd.DataFrame(index=daily_df.index)

    # 1. 趋势强度特征
    for period in [7, 14, 30, 60]:
        if len(daily_df) >= period:
            # 移动平均线斜率
            sma = daily_df['close'].rolling(window=period).mean()
            regime_features[f'sma_{period}d_slope'] = sma.diff(1) / (sma.shift(1) + epsilon)

            # 价格相对于移动平均线的位置
            regime_features[f'price_vs_sma_{period}d'] = daily_df['close'] / (sma + epsilon)

            # 移动平均线排列（多头/空头排列）
            if period >= 14:
                sma_short = daily_df['close'].rolling(window=7).mean()
                regime_features[f'ma_alignment_{period}d'] = (sma_short > sma).astype(int)

    # 2. 波动率状态特征
    for period in [7, 14, 30]:
        if len(daily_df) >= period:
            # 历史波动率
            returns = daily_df['close'].pct_change()
            volatility = returns.rolling(window=period).std() * np.sqrt(365)  # 年化波动率
            regime_features[f'volatility_{period}d'] = volatility

            # 波动率分位数（相对波动率水平）
            vol_rank = volatility.rolling(window=252, min_periods=period).rank(pct=True)
            regime_features[f'volatility_rank_{period}d'] = vol_rank

            # ATR相对水平
            high_low = daily_df['high'] - daily_df['low']
            high_prev_close = abs(daily_df['high'] - daily_df['close'].shift(1))
            low_prev_close = abs(daily_df['low'] - daily_df['close'].shift(1))
            tr = pd.concat([high_low, high_prev_close, low_prev_close], axis=1).max(axis=1)
            atr = tr.rolling(window=period).mean()
            regime_features[f'atr_{period}d'] = atr / (daily_df['close'] + epsilon)

    # 3. 市场结构特征
    # 高低点突破
    for period in [10, 20, 50]:
        if len(daily_df) >= period:
            highest = daily_df['high'].rolling(window=period).max()
            lowest = daily_df['low'].rolling(window=period).min()

            # 突破新高/新低
            regime_features[f'new_high_{period}d'] = (daily_df['high'] >= highest.shift(1)).astype(int)
            regime_features[f'new_low_{period}d'] = (daily_df['low'] <= lowest.shift(1)).astype(int)

            # 价格在区间中的位置
            price_position = (daily_df['close'] - lowest) / (highest - lowest + epsilon)
            regime_features[f'price_position_{period}d'] = price_position

    # 4. 成交量特征
    for period in [7, 14, 30]:
        if len(daily_df) >= period:
            # 成交量移动平均
            vol_ma = daily_df['volume'].rolling(window=period).mean()
            regime_features[f'volume_ratio_{period}d'] = daily_df['volume'] / (vol_ma + epsilon)

            # 成交量突增
            vol_spike = daily_df['volume'] > (vol_ma * 2)
            regime_features[f'volume_spike_{period}d'] = vol_spike.astype(int)

    # 5. 市场情绪指标
    # VIX-like指标（基于价格波动）
    if len(daily_df) >= 30:
        returns = daily_df['close'].pct_change()
        fear_greed = returns.rolling(window=14).std() / returns.rolling(window=30).std()
        regime_features['fear_greed_index'] = fear_greed

    print(f"✅ 计算了 {len(regime_features.columns)} 个市场状态特征")
    return regime_features

def calculate_features(df: pd.DataFrame, timeframe: int, db_path: str = 'coin_data.db',
                      symbol: str = 'ETHUSDT', market: str = 'spot') -> pd.DataFrame:
    """
    计算用于模型训练的综合技术指标特征，包含多时间尺度和市场状态特征。
    """
    print(f"🛠️  开始计算增强特征 ({timeframe}-min K线)...")
    epsilon = 1e-9
    FEATURE_WINDOWS = [60, 120, 360, 720, 1440] # 时间窗口 (分钟)，增加日线级别

    df_feat = df.copy()

    # --- 1. 加载日线数据用于多时间尺度特征 ---
    daily_df = load_daily_data(db_path, symbol, market)
    regime_features = calculate_market_regime_features(daily_df)

    # --- 2. 周期性时间特征 (来自版本1的最佳实践) ---
    df_feat['hour'] = df_feat.index.hour
    df_feat['day_of_week'] = df_feat.index.dayofweek
    df_feat['hour_sin'] = np.sin(2 * np.pi * df_feat['hour'] / 24)
    df_feat['hour_cos'] = np.cos(2 * np.pi * df_feat['hour'] / 24)
    df_feat['day_sin'] = np.sin(2 * np.pi * df_feat['day_of_week'] / 7)
    df_feat['day_cos'] = np.cos(2 * np.pi * df_feat['day_of_week'] / 7)

    # 月份特征（捕捉季节性）
    df_feat['month'] = df_feat.index.month
    df_feat['month_sin'] = np.sin(2 * np.pi * df_feat['month'] / 12)
    df_feat['month_cos'] = np.cos(2 * np.pi * df_feat['month'] / 12)

    # --- 3. 基于ATR标准化的K线特征 (来自版本2的核心) ---
    df_feat = get_standardized_kline_features(df_feat, atr_period=14, epsilon=epsilon)

    # --- 3. 动量、波动率和趋势特征 ---
    for n_minutes in FEATURE_WINDOWS:
        n_candles = n_minutes // timeframe
        if n_candles >= 1:
            # 动量: 价格回报率
            df_feat[f'return_{n_minutes}m'] = df_feat['close'].pct_change(n_candles)
            # 趋势: 移动平均线 (SMA)
            sma_col = f'sma_{n_minutes}m'
            df_feat[sma_col] = df_feat['close'].rolling(window=n_candles).mean()
            df_feat[f'price_div_sma_{n_minutes}m'] = df_feat['close'] / (df_feat[sma_col] + epsilon)
            # 波动率: 价格在窗口内的标准差与价格的比率
            df_feat[f'volatility_{n_minutes}m'] = df_feat['close'].rolling(window=n_candles).std() / (df_feat['close'] + epsilon)

    # 跨周期趋势特征
    if 'sma_120m' in df_feat.columns and 'sma_720m' in df_feat.columns:
        df_feat['sma_120m_div_sma_720m'] = df_feat['sma_120m'] / (df_feat['sma_720m'] + epsilon)

    # --- 4. 震荡器与波动带特征 (高级特征) ---
    # RSI (相对强弱指数)
    rsi_period = 14
    delta = df_feat['close'].diff(1)
    gain = delta.where(delta > 0, 0).rolling(window=rsi_period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=rsi_period).mean()
    rs = gain / (loss + epsilon)
    df_feat[f'rsi_{rsi_period}'] = 100 - (100 / (1 + rs))

    # Bollinger Bands (布林带)
    bb_period = 20
    bb_std = 2
    bb_sma = df_feat['close'].rolling(window=bb_period).mean()
    bb_upper = bb_sma + (bb_std * df_feat['close'].rolling(window=bb_period).std())
    bb_lower = bb_sma - (bb_std * df_feat['close'].rolling(window=bb_period).std())
    df_feat[f'bb_width_{bb_period}'] = (bb_upper - bb_lower) / (bb_sma + epsilon)
    df_feat[f'bb_pos_{bb_period}'] = (df_feat['close'] - bb_lower) / (bb_upper - bb_lower + epsilon)

    # --- 5. 指标的动态特征 (来自版本2的深度洞察) ---
    indicator_ma_period = 5
    # 指标的变化率 (速度)
    df_feat[f'rsi_{rsi_period}_diff_1'] = df_feat[f'rsi_{rsi_period}'].diff(1)
    df_feat[f'bb_width_{bb_period}_diff_1'] = df_feat[f'bb_width_{bb_period}'].diff(1)
    # 指标的移动平均 (短期趋势)
    df_feat[f'rsi_{rsi_period}_ma_{indicator_ma_period}'] = df_feat[f'rsi_{rsi_period}'].rolling(window=indicator_ma_period).mean()
    df_feat[f'bb_width_{bb_period}_ma_{indicator_ma_period}'] = df_feat[f'bb_width_{bb_period}'].rolling(window=indicator_ma_period).mean()

    # --- 6. 成交量相关特征 (包含VWAP，关键！) ---
    for n_minutes in FEATURE_WINDOWS:
        n_candles = n_minutes // timeframe
        if n_candles >= 1:
            # 成交量移动平均 (VMA)
            vma_col = f'vma_{n_minutes}m'
            df_feat[vma_col] = df_feat['volume'].rolling(window=n_candles).mean()
            df_feat[f'volume_div_vma_{n_minutes}m'] = df_feat['volume'] / (df_feat[vma_col] + epsilon)
            
            # 成交量加权平均价 (VWAP) - 极其重要的机构指标
            price_x_volume = df_feat['close'] * df_feat['volume']
            vwap_num = price_x_volume.rolling(window=n_candles).sum()
            vwap_den = df_feat['volume'].rolling(window=n_candles).sum()
            vwap_col = f'vwap_{n_minutes}m'
            df_feat[vwap_col] = vwap_num / (vwap_den + epsilon)
            df_feat[f'price_div_vwap_{n_minutes}m'] = df_feat['close'] / (df_feat[vwap_col] + epsilon)
            
    # --- 7. 整合多时间尺度特征 ---
    if regime_features is not None and len(regime_features) > 0:
        print("🔗 整合多时间尺度特征（使用前一日数据，避免数据泄露）...")

        # 将日线特征对齐到分钟级数据
        # ⚠️ 关键：使用前一日的日线特征，避免数据泄露
        df_feat_aligned = df_feat.copy()

        # 为每个分钟级时间戳找到对应的日期
        df_feat_aligned['current_date'] = df_feat_aligned.index.date

        # 计算前一日日期
        import datetime
        df_feat_aligned['prev_date'] = df_feat_aligned['current_date'].apply(
            lambda x: x - datetime.timedelta(days=1)
        )

        # 准备日线特征数据
        regime_features_daily = regime_features.copy()
        regime_features_daily['date'] = regime_features_daily.index.date

        # 创建日期到特征值的映射
        date_feature_maps = {}
        for col in regime_features.columns:
            if col not in ['date']:
                date_feature_maps[col] = regime_features_daily.set_index('date')[col].to_dict()

        # 合并特征：使用前一日的日线特征
        for col, feature_map in date_feature_maps.items():
            # 使用前一日的特征值
            df_feat_aligned[f'daily_{col}'] = df_feat_aligned['prev_date'].map(feature_map)

        # 移除临时的date列
        df_feat_aligned.drop(columns=['current_date', 'prev_date'], inplace=True)
        df_feat = df_feat_aligned

        # 统计有多少数据因为缺少前一日特征而变成NaN
        daily_cols = [col for col in df_feat.columns if col.startswith('daily_')]
        if daily_cols:
            nan_count_before = df_feat[daily_cols].isna().sum().sum()
            total_values = len(df_feat) * len(daily_cols)
            print(f"📊 日线特征统计: {len(daily_cols)}个特征, {nan_count_before}/{total_values} 个值缺失（前一日数据不可用）")

        print(f"✅ 成功整合了 {len(regime_features.columns)} 个多时间尺度特征（使用前一日数据）")
    else:
        print("⚠️  跳过多时间尺度特征整合（日线数据不可用）")

    # --- 8. 增强的技术指标特征 ---
    print("🔧 计算增强技术指标...")

    # 价格动量指标
    for period in [5, 10, 20]:
        if len(df_feat) >= period:
            # 价格动量
            df_feat[f'momentum_{period}'] = df_feat['close'] / df_feat['close'].shift(period) - 1

            # 价格加速度（动量的变化率）
            momentum = df_feat['close'].pct_change(period)
            df_feat[f'acceleration_{period}'] = momentum.diff(1)

    # 波动率聚类特征
    returns = df_feat['close'].pct_change()
    for period in [10, 20, 50]:
        if len(df_feat) >= period:
            # GARCH-like波动率
            vol_ewm = returns.ewm(span=period).std()
            df_feat[f'vol_ewm_{period}'] = vol_ewm

            # 波动率比率
            vol_short = returns.rolling(window=5).std()
            vol_long = returns.rolling(window=period).std()
            df_feat[f'vol_ratio_{period}'] = vol_short / (vol_long + epsilon)

    # 市场微观结构特征
    # 价格跳跃检测
    price_changes = df_feat['close'].pct_change()
    vol_threshold = price_changes.rolling(window=20).std() * 2
    df_feat['price_jump'] = (abs(price_changes) > vol_threshold).astype(int)

    # 成交量价格趋势
    volume_price_trend = (df_feat['close'].diff() * df_feat['volume']).rolling(window=10).sum()
    df_feat['volume_price_trend'] = volume_price_trend / (df_feat['close'] + epsilon)

    # --- 清理 & 总结 ---
    # 删除计算过程中的临时列
    df_feat.drop(columns=[f'atr_14'], inplace=True, errors='ignore')

    # 填充初始的NaN值
    # 使用bfill填充窗口计算导致的头部NaN，然后dropna以防万一还有剩余
    df_feat.bfill(inplace=True)
    df_feat.dropna(inplace=True)

    final_rows = len(df_feat)
    if final_rows == 0:
         print("⚠️ 警告: 特征计算后没有剩余数据，请检查原始数据量和特征窗口大小。")
    else:
        print(f"✅ 增强特征计算完成。最终生成 {len(df_feat.columns)} 个特征，数据量为 {final_rows}。")

    return df_feat


# ==============================================================================
# 5. 特征列表生成模块 (来自版本2的稳定设计)
# ==============================================================================

def get_feature_list(df: pd.DataFrame) -> List[str]:
    """
    获取用于训练的最终特征列表。
    使用排除法动态生成，同时排除中间计算列，确保模型的稳定性和可复现性。
    """
    # 排除原始OHLCV、标签以及用于生成特征的中间列
    NON_FEATURE_COLS = {
        'open', 'high', 'low', 'close', 'volume', # 原始数据
        'label', 'ActualResult',                 # 标签
        'hour', 'day_of_week', 'month',          # 已被sin/cos编码替代
    }

    # 将所有计算出的SMA, VMA, VWAP, ATR基础列也排除，因为我们使用的是它们的衍生特征（如 price_div_sma）
    derived_base_cols = [col for col in df.columns if any(x in col for x in ['sma_', 'vma_', 'vwap_', 'atr_'] ) and 'div' not in col and 'daily_' not in col]

    # 合并所有要排除的列
    all_exclude_cols = list(NON_FEATURE_COLS) + derived_base_cols

    features = [col for col in df.columns if col not in all_exclude_cols]

    # 按特征类型分组显示
    feature_groups = {
        '时间特征': [f for f in features if any(x in f for x in ['hour_', 'day_', 'month_'])],
        'K线形态特征': [f for f in features if any(x in f for x in ['body_', 'range_', 'shadow_'])],
        '动量特征': [f for f in features if any(x in f for x in ['return_', 'momentum_', 'acceleration_'])],
        '趋势特征': [f for f in features if any(x in f for x in ['price_div_sma', 'ma_alignment'])],
        '波动率特征': [f for f in features if any(x in f for x in ['volatility_', 'vol_', 'atr_'])],
        '震荡器特征': [f for f in features if any(x in f for x in ['rsi_', 'bb_'])],
        '成交量特征': [f for f in features if any(x in f for x in ['volume_', 'vwap'])],
        '市场状态特征': [f for f in features if f.startswith('daily_')],
        '微观结构特征': [f for f in features if any(x in f for x in ['jump', 'trend'])],
        '其他特征': []
    }

    # 将未分类的特征归入其他特征
    classified_features = set()
    for group_features in feature_groups.values():
        classified_features.update(group_features)

    feature_groups['其他特征'] = [f for f in features if f not in classified_features]

    print(f"📋 选定用于模型的特征共计: {len(features)}个")
    print("📊 特征分组统计:")
    for group_name, group_features in feature_groups.items():
        if group_features:
            print(f"  {group_name}: {len(group_features)}个")

    # 如果需要调试，可以取消下面的注释来查看具体的特征列表
    # print("\n🔍 详细特征列表:")
    # for group_name, group_features in feature_groups.items():
    #     if group_features:
    #         print(f"\n{group_name}:")
    #         for feature in sorted(group_features):
    #             print(f"  - {feature}")

    return features