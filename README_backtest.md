# 回测系统重构说明

## 概述

`backtest.py` 已经重构为支持多种数据源的模块化系统。新的架构使用抽象的数据提供者接口，支持从 CSV 文件、SQLite 数据库加载数据，并且可以扩展支持实时数据流。

## 主要改进

### 1. 抽象数据提供者接口

新增了 `DataProvider` 抽象基类，定义了统一的数据访问接口：

```python
class DataProvider(ABC):
    @abstractmethod
    def get_initial_data(self, start_time=None, end_time=None) -> pd.DataFrame:
        """获取初始数据"""
        pass
    
    @abstractmethod
    def get_next_data(self) -> Optional[Tuple[pd.Timestamp, float]]:
        """获取下一条数据"""
        pass
    
    @abstractmethod
    def reset(self):
        """重置数据提供者状态"""
        pass
    
    @abstractmethod
    def get_total_count(self) -> int:
        """获取总数据条数"""
        pass
    
    @abstractmethod
    def get_current_position(self) -> int:
        """获取当前位置"""
        pass
```

### 2. 支持的数据源类型

#### CSVDataProvider
- 从 CSV 文件加载数据
- 支持时间范围过滤
- 支持价格缩放

#### SQLiteDataProvider
- 从 SQLite 数据库加载数据
- 自动处理表名生成
- 支持时间范围过滤
- 兼容 `get_coin_history.py` 生成的数据库格式

#### RealTimeDataProvider
- 支持实时数据流
- 可以动态添加新数据
- 适用于实时交易场景

### 3. 新的回测函数

新增了 `run_backtest_with_provider()` 函数，接受数据提供者作为参数：

```python
def run_backtest_with_provider(data_provider: DataProvider, model_file: str, config_file: str, 
                             output_log_csv: str, start_time=None, stop_loss_pct=None, 
                             max_active_predictions=10000):
    """使用数据提供者执行回测"""
```

## 使用方法

### 1. 使用 CSV 文件进行回测

```bash
python backtest.py --coin DOT --data-source csv --input-csv data/DOTUSDT_15m_10000.csv
```

### 2. 使用 SQLite 数据库进行回测

```bash
python backtest.py --coin DOT --data-source sqlite --db-path coin_data.db --symbol DOTUSDT --interval 15m --market spot
```

### 3. 使用指定时间范围

```bash
python backtest.py --coin DOT --data-source sqlite --start-time "2024-01-15 14:30:00"
```

### 4. 使用止损功能

```bash
python backtest.py --coin DOT --data-source sqlite --stop-loss 2.5
```

### 5. 限制最大活跃预测数

```bash
python backtest.py --coin DOT --data-source sqlite --max-active-predictions 1
```

## 命令行参数

### 新增参数

- `--data-source`: 数据源类型 (`csv` 或 `sqlite`)
- `--db-path`: SQLite 数据库路径
- `--symbol`: 交易对符号 (用于 SQLite)
- `--interval`: 时间间隔 (用于 SQLite)
- `--market`: 市场类型 (`spot` 或 `futures`)

### 现有参数

- `--coin`: 币种名称
- `--model-file`: 模型文件路径
- `--config-file`: 配置文件路径
- `--start-time`: 回测开始时间
- `--stop-loss`: 止损百分比
- `--max-active-predictions`: 最大活跃预测数

## 配置示例

### config.json 配置

```json
{
  "output_dir": "models",
  "coin_configs": {
    "DOT": {
      "csv_file": "data/DOTUSDT_15m_10000.csv",
      "db_path": "coin_data.db",
      "api_symbol": "DOTUSDT",
      "display_name": "DOT/USDT",
      "timeframe_minutes": 15,
      "up_threshold": 0.05,
      "down_threshold": 0.05,
      "max_lookforward_minutes": 1440,
      "model_basename": "dot_15m",
      "price_multiplier": 1.0
    }
  }
}
```

## 实时数据处理

### 创建实时数据提供者

```python
from backtest import RealTimeDataProvider, HistoricalBacktester

# 创建初始历史数据
initial_df = pd.DataFrame(...)  # 你的历史数据
realtime_provider = RealTimeDataProvider(initial_df)

# 初始化回测器
backtester = HistoricalBacktester(model_file, config_file)

# 获取初始数据
df = realtime_provider.get_initial_data()

# 模拟实时数据流
for new_data in realtime_data_stream:
    # 添加新数据
    realtime_provider.add_new_data(
        timestamp=new_data['timestamp'],
        open_price=new_data['open'],
        high=new_data['high'],
        low=new_data['low'],
        close=new_data['close'],
        volume=new_data['volume']
    )
    
    # 获取下一条数据进行处理
    data_point = realtime_provider.get_next_data()
    if data_point is not None:
        timestamp, price = data_point
        
        # 检查现有预测
        backtester.check_predictions(price, timestamp, current_idx)
        
        # 尝试进行新预测
        guess, probability, pred_price = backtester.make_prediction(df, current_idx)
        if guess is not None:
            backtester.add_prediction(guess, probability, pred_price, timestamp, current_idx)
```

## 测试

运行测试脚本验证功能：

```bash
python test_backtest.py
```

## 示例

运行示例脚本查看各种用法：

```bash
python example_usage.py
```

## 优势

1. **模块化设计**: 数据源与回测逻辑分离
2. **易于扩展**: 可以轻松添加新的数据源类型
3. **实时支持**: 支持实时数据流处理
4. **向后兼容**: 保持与现有代码的兼容性
5. **统一接口**: 所有数据源使用相同的接口

## 注意事项

1. SQLite 数据库表名格式: `{symbol}_{interval}_{market}`
2. 时间戳使用 UTC 时间
3. 价格缩放在数据加载时应用
4. 实时数据提供者需要外部调用 `add_new_data()` 来添加新数据 