#!/usr/bin/env python3
"""
测试时序交叉验证功能的简单脚本
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import TimeSeriesSplit

def test_time_series_split():
    """测试TimeSeriesSplit的基本功能"""
    print("=== 测试 TimeSeriesSplit 基本功能 ===")
    
    # 创建模拟时间序列数据
    dates = pd.date_range('2023-01-01', periods=1000, freq='1H')
    data = pd.DataFrame({
        'timestamp': dates,
        'value': np.random.randn(1000).cumsum(),
        'feature1': np.random.randn(1000),
        'feature2': np.random.randn(1000),
        'target': np.random.choice([0, 1], 1000)
    })
    
    print(f"数据形状: {data.shape}")
    print(f"时间范围: {data['timestamp'].min()} 到 {data['timestamp'].max()}")
    
    # 创建时序分割器
    tscv = TimeSeriesSplit(n_splits=5)
    
    print(f"\n时序交叉验证分割 (n_splits=5):")
    
    for fold, (train_idx, val_idx) in enumerate(tscv.split(data)):
        train_start = data.iloc[train_idx[0]]['timestamp']
        train_end = data.iloc[train_idx[-1]]['timestamp']
        val_start = data.iloc[val_idx[0]]['timestamp']
        val_end = data.iloc[val_idx[-1]]['timestamp']
        
        print(f"Fold {fold + 1}:")
        print(f"  训练集: {len(train_idx)} 样本 ({train_start} 到 {train_end})")
        print(f"  验证集: {len(val_idx)} 样本 ({val_start} 到 {val_end})")
        print(f"  时间间隔: 训练集结束到验证集开始")
        print()

def visualize_time_series_split():
    """可视化时序交叉验证的分割方式（文本版本）"""
    print("=== 时序交叉验证分割可视化 ===")

    # 创建简单的时间序列数据
    n_samples = 100
    data = np.arange(n_samples)

    # 创建时序分割器
    tscv = TimeSeriesSplit(n_splits=5)

    print(f"总样本数: {n_samples}")
    print("分割可视化 (T=训练集, V=验证集, -=未使用):")
    print()

    for fold, (train_idx, val_idx) in enumerate(tscv.split(data)):
        # 创建可视化字符串
        viz = ['-'] * n_samples

        # 标记训练集
        for idx in train_idx:
            viz[idx] = 'T'

        # 标记验证集
        for idx in val_idx:
            viz[idx] = 'V'

        # 每20个字符换行显示
        viz_str = ''.join(viz)
        lines = [viz_str[i:i+50] for i in range(0, len(viz_str), 50)]

        print(f"Fold {fold + 1}:")
        for line in lines:
            print(f"  {line}")
        print(f"  训练集: {len(train_idx)} 样本, 验证集: {len(val_idx)} 样本")
        print()

def test_parameter_grid():
    """测试参数网格搜索"""
    print("=== 测试参数网格搜索 ===")
    
    from sklearn.model_selection import ParameterGrid
    
    # 定义参数网格
    param_grid = {
        'n_estimators': [100, 200],
        'learning_rate': [0.05, 0.1],
        'max_depth': [6, 8]
    }
    
    print(f"参数网格: {param_grid}")
    
    # 生成所有参数组合
    grid = list(ParameterGrid(param_grid))
    print(f"总共 {len(grid)} 种参数组合:")
    
    for i, params in enumerate(grid):
        print(f"  组合 {i+1}: {params}")

def main():
    """主函数"""
    print("🧪 时序交叉验证功能测试\n")
    
    try:
        # 测试基本功能
        test_time_series_split()
        
        # 测试参数网格
        test_parameter_grid()
        
        # 可视化
        visualize_time_series_split()
        
        print("✅ 所有测试通过！时序交叉验证功能正常。")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
