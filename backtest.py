# backtest.py
# 使用已保存的模型，对历史数据进行真实环境模拟回测
# 只支持SQLite数据源

import pandas as pd
import numpy as np
import joblib
import json
import argparse
import os
from datetime import datetime
import pytz
from typing import Dict, List, Optional
from model_utils import calculate_features, get_coin_config, get_output_dir
from analyze_backtest import analyze_and_plot_results
from data_providers import DataProvider, format_beijing_time
from data_loader import load_data_for_backtest, create_data_source_config, print_data_source_info

# 北京时区
BEIJING_TZ = pytz.timezone('Asia/Shanghai')

def parse_time_input(time_str):
    """解析用户输入的时间字符串为UTC时间戳"""
    if not time_str:
        return None

    try:
        # 尝试解析不同格式
        formats = [
            '%Y-%m-%d %H:%M:%S',
            '%Y-%m-%d %H:%M',
            '%Y-%m-%d',
            '%m-%d %H:%M',
            '%m-%d'
        ]

        dt = None
        for fmt in formats:
            try:
                dt = datetime.strptime(time_str, fmt)
                break
            except ValueError:
                continue

        if dt is None:
            raise ValueError(f"无法解析时间格式: {time_str}")

        # 如果只有月日，补充年份
        if dt.year == 1900:
            dt = dt.replace(year=datetime.now().year)

        # 将北京时间转换为UTC
        beijing_dt = BEIJING_TZ.localize(dt)
        utc_dt = beijing_dt.astimezone(pytz.UTC)

        return pd.Timestamp(utc_dt).tz_localize(None)  # 返回naive UTC时间戳

    except Exception as e:
        print(f"时间解析错误: {e}")
        print("支持的格式示例:")
        print("  2024-09-15 14:30:00")
        print("  2024-09-15 14:30")
        print("  2024-09-15")
        print("  09-15 14:30")
        print("  09-15")
        return None

class HistoricalBacktester:
    def __init__(self, model_file: str, config_file: str, price_multiplier: float = 1.0, stop_loss_pct: float = None):
        """初始化历史回测器"""
        self.model_file = model_file
        self.config_file = config_file
        self.price_multiplier = price_multiplier
        self.stop_loss_pct = stop_loss_pct  # 止损百分比，None表示不使用止损

        # 加载模型和配置
        self.model = joblib.load(model_file)
        with open(config_file, 'r') as f:
            self.config = json.load(f)

        # 预测跟踪
        self.active_predictions: Dict[str, dict] = {}
        self.completed_predictions: List[dict] = []
        self.prediction_counter = 0

        # 统计
        self.total_predictions = 0
        self.successful_predictions = 0
        self.failed_predictions = 0
        self.timeout_predictions = 0
        self.stop_loss_predictions = 0  # 新增：止损交易统计

        print(f"回测器初始化完成")
        print(f"模型阈值: {self.config['best_threshold']:.3f}")
        print(f"最大等待: {self.config['max_lookforward_minutes']}分钟")
        print(f"目标计算方式: 24小时内最大涨跌幅差")
        if price_multiplier != 1.0:
            print(f"价格缩放: {price_multiplier}x")
        if stop_loss_pct is not None:
            print(f"止损设置: {stop_loss_pct:.1f}%")

    def make_prediction(self, df: pd.DataFrame, current_idx: int) -> tuple:
        """对当前时间点进行预测"""
        try:
            # 获取到当前时间点的数据
            current_data = df.iloc[:current_idx+1].copy()

            # 计算特征
            features_df = calculate_features(current_data, timeframe=self.config['timeframe_minutes'])
            features_df_clean = features_df.dropna()

            if features_df_clean.empty:
                return None, 0.0, 0.0

            # 获取最新的特征
            feature_list = self.config['feature_list']
            latest_features_series = features_df_clean.iloc[-1]

            # 检查特征完整性
            missing_features = [f for f in feature_list if f not in latest_features_series.index]
            if missing_features:
                return None, 0.0, 0.0

            # 进行预测
            latest_features_df = latest_features_series[feature_list].to_frame().T
            current_price = latest_features_series['close']

            probability = self.model.predict_proba(latest_features_df)[0, 1]
            best_threshold = self.config['best_threshold']

            # 判断预测方向
            guess = None
            if probability > best_threshold:
                guess = 1  # 预测先涨
            elif probability < (1 - best_threshold):
                guess = 0  # 预测先跌

            return guess, probability, current_price

        except Exception as e:
            print(f"预测时发生错误: {e}")
            return None, 0.0, 0.0

    def add_prediction(self, guess: int, probability: float, price: float, timestamp: pd.Timestamp, current_idx: int):
        """添加新的预测"""
        self.prediction_counter += 1
        prediction_id = f"pred_{self.prediction_counter:06d}"

        # 计算过期时间（索引）
        max_wait_minutes = self.config['max_lookforward_minutes']
        timeframe_minutes = self.config['timeframe_minutes']
        max_wait_candles = max_wait_minutes // timeframe_minutes
        expire_idx = current_idx + max_wait_candles

        prediction = {
            'id': prediction_id,
            'guess': guess,
            'probability': probability,
            'start_price': price,
            'start_timestamp': timestamp,
            'start_idx': current_idx,
            'expire_idx': expire_idx,
            'up_target': price * (1 + self.config['up_threshold']),
            'down_target': price * (1 - self.config['down_threshold']),
            'status': 'active',
            'max_loss_pct': 0.0,  # 记录最大亏损百分比
            'max_loss_price': price,  # 记录最大亏损时的价格
            'max_loss_timestamp': timestamp  # 记录最大亏损时的时间戳
        }

        self.active_predictions[prediction_id] = prediction
        self.total_predictions += 1

        direction_str = f"先涨{self.config['up_threshold']*100:.1f}%" if guess == 1 else f"先跌{self.config['down_threshold']*100:.1f}%"
        print(f"[{format_beijing_time(timestamp)}] 新预测: {direction_str}, 信心: {probability:.3f}, 价格: {price:.4f}")

    def check_predictions(self, current_price: float, current_timestamp: pd.Timestamp, current_idx: int):
        """检查所有活跃预测的状态"""
        completed_ids = []

        for pred_id, pred in self.active_predictions.items():
            if pred['status'] != 'active':
                continue

            # 计算当前价格相对于起始价格的变化百分比
            current_price_change_pct = (current_price - pred['start_price']) / pred['start_price'] * 100

            # 更新最大亏损记录
            if pred['guess'] == 1:  # 预测先涨
                # 对于预测上涨，价格下跌为亏损
                if current_price_change_pct < 0:
                    loss_pct = abs(current_price_change_pct)
                    if loss_pct > pred['max_loss_pct']:
                        pred['max_loss_pct'] = loss_pct
                        pred['max_loss_price'] = current_price
                        pred['max_loss_timestamp'] = current_timestamp
            else:  # 预测先跌
                # 对于预测下跌，价格上涨为亏损
                if current_price_change_pct > 0:
                    loss_pct = current_price_change_pct
                    if loss_pct > pred['max_loss_pct']:
                        pred['max_loss_pct'] = loss_pct
                        pred['max_loss_price'] = current_price
                        pred['max_loss_timestamp'] = current_timestamp

            # 检查止损条件
            if self.stop_loss_pct is not None:
                should_stop_loss = False
                if pred['guess'] == 1:  # 预测先涨
                    # 对于预测上涨，价格下跌超过止损线时止损
                    if current_price_change_pct < -self.stop_loss_pct:
                        should_stop_loss = True
                else:  # 预测先跌
                    # 对于预测下跌，价格上涨超过止损线时止损
                    if current_price_change_pct > self.stop_loss_pct:
                        should_stop_loss = True
                
                if should_stop_loss:
                    # 计算止损损失（按比例计算，up_threshold对应1分，stop_loss_pct为百分比需先除以100）
                    stop_loss_score = -((self.stop_loss_pct / 100) / self.config['up_threshold'])
                    self.complete_prediction(pred_id, -2, current_price, current_timestamp, current_idx, f"止损({self.stop_loss_pct:.1f}%)", stop_loss_score)
                    completed_ids.append(pred_id)
                    continue

            # 检查是否达到目标价格
            if current_price >= pred['up_target']:
                result = 1 if pred['guess'] == 1 else 0
                reason = "达到上涨目标" if pred['guess'] == 1 else "达到上涨目标(预测错误)"
                self.complete_prediction(pred_id, result, current_price, current_timestamp, current_idx, reason)
                completed_ids.append(pred_id)

            elif current_price <= pred['down_target']:
                result = 1 if pred['guess'] == 0 else 0
                reason = "达到下跌目标" if pred['guess'] == 0 else "达到下跌目标(预测错误)"
                self.complete_prediction(pred_id, result, current_price, current_timestamp, current_idx, reason)
                completed_ids.append(pred_id)

            elif current_idx >= pred['expire_idx']:
                self.complete_prediction(pred_id, -1, current_price, current_timestamp, current_idx, "超时")
                completed_ids.append(pred_id)

        # 移除已完成的预测
        for pred_id in completed_ids:
            if pred_id in self.active_predictions:
                del self.active_predictions[pred_id]

    def calculate_score(self, prediction: int, start_price: float, end_price: float, result: int):
        """计算预测得分"""
        # 计算价格变化幅度
        price_change_pct = (end_price - start_price) / start_price

        threshold = self.config['up_threshold']  # 假设up/down threshold相同

        if result == 1:  # 成功达到目标
            return 1.0
        elif result == 0:  # 失败（达到相反目标）
            return -1.0
        else:  # 超时，根据实际价格变化计算得分
            if prediction == 1:  # 预测上涨
                # 价格变化幅度转换为得分：-threshold=-1分，+threshold=+1分
                score = price_change_pct / threshold  # 除以up_threshold得到基础得分
                return max(-1.0, min(1.0, score))  # 限制在-1到1之间
            else:  # 预测下跌
                # 预测下跌时，价格下跌得正分，上涨得负分
                score = -price_change_pct / threshold
                return max(-1.0, min(1.0, score))  # 限制在-1到1之间

    def complete_prediction(self, pred_id: str, result: int, final_price: float,
                          end_timestamp: pd.Timestamp, end_idx: int, reason: str, custom_score: float = None):
        """完成一个预测并记录结果"""
        pred = self.active_predictions[pred_id]
        pred['status'] = 'completed'

        duration_minutes = (end_idx - pred['start_idx']) * self.config['timeframe_minutes']

        # 计算价格变化幅度
        price_change_pct = (final_price - pred['start_price']) / pred['start_price']

        # 计算得分
        if custom_score is not None:
            score = custom_score
        else:
            score = self.calculate_score(pred['guess'], pred['start_price'], final_price, result)

        # 更新统计
        if result == 1:
            self.successful_predictions += 1
            status_str = "成功✅"
        elif result == 0:
            self.failed_predictions += 1
            status_str = "失败❌"
        elif result == -2:  # 新增：止损交易
            self.stop_loss_predictions += 1
            status_str = "止损🛑"
        else:
            self.timeout_predictions += 1
            status_str = "超时⏰"

        # 记录完成的预测（时间转换为北京时间字符串）
        completed_pred = {
            'PredictionID': pred_id,
            'StartTimestamp': format_beijing_time(pred['start_timestamp']),
            'EndTimestamp': format_beijing_time(end_timestamp),
            'StartPrice': pred['start_price'],
            'EndPrice': final_price,
            'PriceChangePct': price_change_pct * 100,  # 转换为百分比
            'MaxLossPct': pred['max_loss_pct'],  # 最大亏损百分比
            'MaxLossPrice': pred['max_loss_price'],  # 最大亏损时的价格
            'MaxLossTimestamp': format_beijing_time(pred['max_loss_timestamp']),  # 最大亏损时的时间戳
            'Confidence': pred['probability'],
            'Prediction': pred['guess'],
            'Result': result,
            'Score': score,
            'Status': status_str,
            'Reason': reason,
            'DurationMinutes': duration_minutes,
            'UpTarget': pred['up_target'],
            'DownTarget': pred['down_target']
        }

        self.completed_predictions.append(completed_pred)

        direction_str = f"先涨{self.config['up_threshold']*100:.1f}%" if pred['guess'] == 1 else f"先跌{self.config['down_threshold']*100:.1f}%"
        max_loss_info = f", 最大亏损: {pred['max_loss_pct']:.2f}%" if pred['max_loss_pct'] > 0 else ""
        print(f"[{format_beijing_time(end_timestamp)}] 预测完成: {direction_str} -> {status_str} ({reason}), 用时: {duration_minutes}分钟, 涨跌: {price_change_pct*100:+.2f}%, 得分: {score:+.2f}{max_loss_info}")

def run_backtest_with_provider(data_provider: DataProvider, model_file: str, config_file: str, 
                             output_log_csv: str, start_time: Optional[pd.Timestamp] = None, 
                             end_time: Optional[pd.Timestamp] = None,
                             stop_loss_pct: Optional[float] = None, max_active_predictions: int = 10000,
                             initial_data_count: int = 1000):
    """使用数据提供者执行回测（增量模式）"""
    print("=== 开始增量数据回测 ===")

    # 1. 检查文件
    if not os.path.exists(model_file):
        print(f"错误: 模型文件 '{model_file}' 未找到。"); return
    if not os.path.exists(config_file):
        print(f"错误: 配置文件 '{config_file}' 未找到。"); return

    # 2. 加载初始数据
    try:
        df = data_provider.get_initial_data(initial_count=initial_data_count, start_time=start_time)
        if df is None or len(df) == 0:
            print("❌ 无法加载初始数据")
            return
    except Exception as e:
        print(f"❌ 加载初始数据时出错: {e}")
        return

    print(f"初始数据: {len(df)} 条记录")
    print(f"初始时间范围: {format_beijing_time(df.index[0])} 到 {format_beijing_time(df.index[-1])}")

    # 3. 初始化回测器
    backtester = HistoricalBacktester(model_file, config_file, data_provider.price_multiplier, stop_loss_pct)

    # 4. 开始增量数据回测
    print("\n开始增量数据回测...")

    # 需要足够的历史数据来计算特征
    min_history = 720 // backtester.config['timeframe_minutes'] + 50  # 至少需要720分钟的历史数据

    # 确定实际开始预测的位置
    actual_start_pos = min_history
    print(f"从索引 {actual_start_pos} 开始预测 (时间: {format_beijing_time(df.index[actual_start_pos])})")

    # 设置数据提供者的起始位置
    data_provider.start_idx = actual_start_pos
    data_provider.reset()

    current_idx = actual_start_pos
    total_count = data_provider.get_total_count()

    # 处理初始数据
    print(f"\n处理初始数据 ({len(df)} 条)...")
    for i in range(actual_start_pos, len(df)):
        current_timestamp = df.index[i]
        
        # 如果当前时间超过了设定的结束时间，则停止回测
        if end_time and current_timestamp > end_time:
            print(f"达到回测结束时间 ({format_beijing_time(end_time)})，停止处理。")
            break

        current_price = df.iloc[i]['close']

        # 检查现有预测
        backtester.check_predictions(current_price, current_timestamp, i)

        # 只在指定时间之后才进行新预测
        should_predict = True
        if start_time is not None and current_timestamp < start_time:
            should_predict = False

        # 活跃订单数限制
        if should_predict:
            if len([p for p in backtester.active_predictions.values() if p['status'] == 'active']) < max_active_predictions:
                # 尝试进行新预测
                guess, probability, pred_price = backtester.make_prediction(df, i)
                if guess is not None:
                    backtester.add_prediction(guess, probability, pred_price, current_timestamp, i)
            else:
                # 超过最大持仓数，不开新单
                pass

        # 定期显示进度
        if i % 1000 == 0:
            progress = (i - actual_start_pos) / (len(df) - actual_start_pos) * 100
            print(f"初始数据进度: {progress:.1f}% ({i}/{len(df)}), 活跃预测: {len(backtester.active_predictions)}")

    print(f"初始数据处理完成，活跃预测: {len(backtester.active_predictions)}")

    # 5. 增量处理新数据
    print(f"\n开始增量处理新数据...")
    
    # 获取剩余数据进行增量处理
    remaining_data = None
    if hasattr(data_provider, 'all_data') and data_provider.all_data is not None:
        # 从所有数据中获取剩余部分
        remaining_data = data_provider.all_data.iloc[len(df):]
        print(f"📊 发现 {len(remaining_data)} 条剩余数据，开始增量处理...")
        
        for i, (timestamp, row) in enumerate(remaining_data.iterrows()):
            # 如果当前时间超过了设定的结束时间，则停止回测
            if end_time and timestamp > end_time:
                print(f"达到回测结束时间 ({format_beijing_time(end_time)})，停止处理。")
                break

            # 添加新数据到数据提供者
            data_provider.add_new_data(
                timestamp=timestamp,
                open_price=row['open'],
                high=row['high'],
                low=row['low'],
                close=row['close'],
                volume=row['volume']
            )
            
            # 更新当前数据
            df = data_provider.get_current_data()
            current_idx = len(df) - 1
            
            # 检查现有预测
            backtester.check_predictions(row['close'], timestamp, current_idx)

            # 只在指定时间之后才进行新预测
            should_predict = True
            if start_time is not None and timestamp < start_time:
                should_predict = False

            # 活跃订单数限制
            if should_predict:
                if len([p for p in backtester.active_predictions.values() if p['status'] == 'active']) < max_active_predictions:
                    # 尝试进行新预测
                    guess, probability, pred_price = backtester.make_prediction(df, current_idx)
                    if guess is not None:
                        backtester.add_prediction(guess, probability, pred_price, timestamp, current_idx)
                else:
                    # 超过最大持仓数，不开新单
                    pass

            # 定期显示进度
            if (i + 1) % 100 == 0:
                print(f"增量数据进度: {i+1}/{len(remaining_data)}, 活跃预测: {len(backtester.active_predictions)}")
    else:
        print("⚠️  数据提供者不支持增量处理，跳过剩余数据处理")

    # 6. 处理剩余的活跃预测（全部标记为超时）
    print("\n处理剩余的活跃预测...")
    final_timestamp = df.index[-1] if len(df) > 0 else pd.Timestamp.now()
    final_price = df.iloc[-1]['close'] if len(df) > 0 else 0
    final_idx = len(df) - 1 if len(df) > 0 else 0

    for pred_id in list(backtester.active_predictions.keys()):
        backtester.complete_prediction(pred_id, -1, final_price, final_timestamp, final_idx, "数据结束-超时")

    # 7. 生成报告
    print("\n=== 回测结果摘要 ===")
    print(f"总预测数: {backtester.total_predictions}")
    print(f"成功: {backtester.successful_predictions}")
    print(f"失败: {backtester.failed_predictions}")
    print(f"超时: {backtester.timeout_predictions}")
    if stop_loss_pct is not None:
        print(f"止损: {backtester.stop_loss_predictions}")

    if backtester.completed_predictions:
        # 计算总得分（包括超时交易的得分）
        total_score = sum(pred['Score'] for pred in backtester.completed_predictions)
        avg_score = total_score / len(backtester.completed_predictions)

        # 计算传统胜率（仅非超时交易）
        valid_trades = backtester.successful_predictions + backtester.failed_predictions
        if valid_trades > 0:
            success_rate = backtester.successful_predictions / valid_trades * 100
            print(f"胜率 (非超时): {success_rate:.2f}%")

        # 计算所有交易的正得分率
        positive_scores = sum(1 for pred in backtester.completed_predictions if pred['Score'] > 0)
        positive_rate = positive_scores / len(backtester.completed_predictions) * 100

        print(f"总得分: {total_score:+.2f}")
        print(f"平均得分: {avg_score:+.4f}")
        print(f"正得分率: {positive_rate:.2f}% ({positive_scores}/{len(backtester.completed_predictions)})")

        # 分析超时交易的表现
        timeout_trades = [pred for pred in backtester.completed_predictions if pred['Result'] == -1]
        if timeout_trades:
            timeout_score = sum(pred['Score'] for pred in timeout_trades)
            timeout_avg = timeout_score / len(timeout_trades)
            print(f"超时交易得分: {timeout_score:+.2f} (平均: {timeout_avg:+.4f})")

        # 分析止损交易的表现
        if stop_loss_pct is not None:
            stop_loss_trades = [pred for pred in backtester.completed_predictions if pred['Result'] == -2]
            if stop_loss_trades:
                stop_loss_score = sum(pred['Score'] for pred in stop_loss_trades)
                stop_loss_avg = stop_loss_score / len(stop_loss_trades)
                print(f"止损交易得分: {stop_loss_score:+.2f} (平均: {stop_loss_avg:+.4f})")

    # 8. 保存详细结果
    if backtester.completed_predictions:
        results_df = pd.DataFrame(backtester.completed_predictions)

        # 按开始时间排序（需要先转换回时间戳进行排序）
        results_df['_sort_time'] = pd.to_datetime(results_df['StartTimestamp'].str.replace(' UTC+8', ''))
        results_df = results_df.sort_values('_sort_time')
        results_df = results_df.drop('_sort_time', axis=1)

        # 重新排列列的顺序，把重要信息放在前面
        column_order = [
            'PredictionID', 'StartTimestamp', 'EndTimestamp', 'StartPrice', 'EndPrice',
            'PriceChangePct', 'MaxLossPct', 'MaxLossPrice', 'MaxLossTimestamp', 'Prediction', 'Result', 'Score', 'Status', 'Confidence',
            'DurationMinutes', 'Reason', 'UpTarget', 'DownTarget'
        ]
        results_df = results_df[column_order]
        results_df.to_csv(output_log_csv, index=False, float_format='%.4f')
        print(f"\n详细回测日志已保存到: {output_log_csv}")
        print(f"CSV包含 {len(results_df)} 条交易记录，按开始时间排序，所有时间均为北京时间(UTC+8)")

        # 9. 生成收益分析和图表
        analyze_and_plot_results(results_df, output_log_csv.replace('.csv', ''))
    else:
        print("\n没有生成任何预测结果。")

def run_backtest(model_file, config_file, coin_name, db_path=None, symbol=None, interval=None, market='spot',
                output_log_csv=None, start_time=None, end_time=None, stop_loss_pct=None, max_active_predictions=10000):
    """执行真实环境模拟回测（简化接口）"""

    # 使用公共数据加载模块创建数据提供者
    try:
        data_provider = load_data_for_backtest(coin_name, db_path, symbol, interval, market)
        if data_provider is None:
            print(f"❌ 创建数据提供者失败")
            return
    except Exception as e:
        print(f"❌ 创建数据提供者失败: {e}")
        return

    # 使用新的回测函数
    run_backtest_with_provider(data_provider, model_file, config_file, output_log_csv, start_time, end_time, stop_loss_pct, max_active_predictions)

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="使用已保存的模型对新的历史数据进行回测。")
    parser.add_argument("--coin", default="DOT", help="币种名称 (如: DOT, SEI)")
    parser.add_argument("--model-file", help="已训练的模型文件路径 (.joblib) - 可选，会根据币种自动生成")
    parser.add_argument("--config-file", help="模型对应的配置文件路径 (.json) - 可选，会根据币种自动生成")
    parser.add_argument("--output-log-csv", help="保存详细回测日志的CSV文件路径 - 可选，会自动生成")
    parser.add_argument("--start-time", help="回测开始时间(北京时间)，格式: 2024-09-15 14:30:00 或 09-15 14:30")
    parser.add_argument("--end-time", help="回测结束时间(北京时间)，格式: 2024-09-15 14:30:00 或 09-15 14:30")
    parser.add_argument("--stop-loss", type=float, help="止损百分比，例如: 2.5 表示2.5%%止损")
    parser.add_argument("--max-active-predictions", type=int, default=10000, help="同时持有的最大订单数（预测单数），如1表示只允许同时持有一个订单，默认10000（基本无限制）")

    # SQLite数据源参数
    parser.add_argument("--db-path", help="SQLite数据库路径", default='coin_data.db')
    parser.add_argument("--symbol", help="交易对符号", default='ETHUSDT')
    parser.add_argument("--interval", help="时间间隔", default='15m')
    parser.add_argument("--market", choices=['spot', 'futures'], default='spot', help="市场类型")

    args = parser.parse_args()

    # 获取币种配置
    coin_config = get_coin_config(args.coin)
    if coin_config is None:
        exit(1)

    # 解析开始时间
    start_time = None
    if args.start_time:
        start_time = parse_time_input(args.start_time)
        if start_time is None:
            exit(1)

    # 解析结束时间
    end_time = None
    if args.end_time:
        end_time = parse_time_input(args.end_time)
        if end_time is None:
            exit(1)

    # 设置默认文件路径
    output_dir = get_output_dir()
    model_file = args.model_file or f"{coin_config['model_basename']}_model.joblib"
    config_file = args.config_file or f"{coin_config['model_basename']}_config.json"
    model_file = os.path.join(output_dir, model_file)
    config_file = os.path.join(output_dir, config_file)

    output_log_csv = args.output_log_csv or f"{args.coin.lower()}_backtest_log.csv"
    output_log_csv = os.path.join(output_dir, output_log_csv)
    price_multiplier = coin_config.get('price_multiplier', 1.0)

    # 创建数据源配置
    data_source = create_data_source_config(args.coin, args.db_path, args.symbol, args.interval, args.market)
    price_multiplier = coin_config.get('price_multiplier', 1.0)

    # 打印回测信息
    print(f"=== {coin_config['display_name']} 回测 ===")
    print(f"模型文件: {model_file}")
    print(f"配置文件: {config_file}")
    print_data_source_info(args.coin, data_source)
    print(f"输出文件: {output_log_csv}")
    if start_time is not None:
        print(f"开始时间: {format_beijing_time(start_time)}")
    if end_time is not None:
        print(f"结束时间: {format_beijing_time(end_time)}")
    if args.stop_loss is not None:
        print(f"止损设置: {args.stop_loss:.1f}%")
    if args.max_active_predictions != 10000:
        print(f"最大活跃预测数: {args.max_active_predictions}")
    print()

    run_backtest(model_file, config_file, args.coin, args.db_path, args.symbol, args.interval, args.market,
                output_log_csv, start_time, end_time, args.stop_loss, args.max_active_predictions)