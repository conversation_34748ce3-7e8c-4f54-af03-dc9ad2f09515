#!/usr/bin/env python3
"""
快速获取BTC历史数据脚本
专门用于获取大量历史K线数据，支持分批获取
支持本地代理配置
数据保存到SQLite数据库，按币种和间隔分表
"""

import requests
import pandas as pd
import time
from datetime import datetime, timezone
import argparse
import sys
import os
import sqlite3
from typing import Optional, Tuple

def format_timestamp(timestamp):
    """格式化时间戳为北京时间"""
    dt = datetime.fromtimestamp(timestamp, tz=timezone.utc)
    beijing_dt = dt.astimezone(timezone.utc).replace(tzinfo=None) + pd.Timedelta(hours=8)
    return beijing_dt.strftime('%Y-%m-%d %H:%M:%S')

def get_table_name(symbol: str, interval: str, market: str = "futures") -> str:
    """根据交易对、间隔和市场类型生成表名"""
    # 移除特殊字符，确保表名合法
    safe_symbol = symbol.replace('/', '_').replace('-', '_')
    safe_interval = interval.replace('m', 'min').replace('h', 'hour').replace('d', 'day')
    return f"{safe_symbol}_{safe_interval}_{market}"

def init_database(db_path: str = "btc_data.db"):
    """初始化数据库，如果需要，则自动升级表结构"""
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 1. 确保 table_info 表存在（这是新版脚本期望的结构）
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS table_info (
            table_name TEXT PRIMARY KEY,
            symbol TEXT,
            interval TEXT,
            market TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # 2. 检查 'market' 列是否存在于现有的 table_info 表中
    cursor.execute("PRAGMA table_info(table_info)")
    columns = [info[1] for info in cursor.fetchall()] # info[1] 是列名

    # 3. 如果 'market' 列不存在，就添加它
    if 'market' not in columns:
        print("🔧 检测到旧版数据库结构，正在自动升级 'table_info' 表...")
        try:
            cursor.execute("ALTER TABLE table_info ADD COLUMN market TEXT")
            conn.commit()
            print("✅ 'table_info' 表升级成功！")
        except sqlite3.OperationalError as e:
            # 这个异常理论上不该发生，但为了健壮性加上
            print(f"⚠️ 升级 'table_info' 表时出现警告: {e}")
            
    conn.close()
    print(f"📊 数据库初始化完成: {db_path}")

def create_table_if_not_exists(db_path: str, symbol: str, interval: str, market: str = "futures") -> str:
    """创建表（如果不存在）并返回表名"""
    table_name = get_table_name(symbol, interval, market)
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 创建K线数据表
    cursor.execute(f'''
        CREATE TABLE IF NOT EXISTS {table_name} (
            timestamp INTEGER PRIMARY KEY,
            open REAL,
            high REAL,
            low REAL,
            close REAL,
            volume REAL,
            close_time INTEGER,
            quote_volume REAL,
            trade_count INTEGER,
            taker_buy_base_volume REAL,
            taker_buy_quote_volume REAL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # 记录表信息
    cursor.execute('''
        INSERT OR IGNORE INTO table_info (table_name, symbol, interval, market)
        VALUES (?, ?, ?, ?)
    ''', (table_name, symbol, interval, market))
    
    conn.commit()
    conn.close()
    
    return table_name



def save_data_to_db(db_path: str, table_name: str, data_list: list) -> int:
    """保存数据到数据库，返回新增的记录数"""
    if not data_list:
        return 0

    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # 记录插入前的行数
    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
    count_before = cursor.fetchone()[0]

    # 调试：显示要插入的数据范围
    if data_list:
        timestamps = [data['Timestamp'] for data in data_list]
        min_ts, max_ts = min(timestamps), max(timestamps)
        from datetime import datetime
        min_date = datetime.fromtimestamp(min_ts).strftime('%Y-%m-%d %H:%M:%S')
        max_date = datetime.fromtimestamp(max_ts).strftime('%Y-%m-%d %H:%M:%S')
        print(f"🔍 尝试插入 {len(data_list)} 条记录，时间范围: {min_date} 到 {max_date}")

    # 直接插入所有数据，让数据库处理去重
    insert_sql = f'''
        INSERT OR IGNORE INTO {table_name}
        (timestamp, open, high, low, close, volume, close_time, quote_volume, trade_count, taker_buy_base_volume, taker_buy_quote_volume)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    '''

    values = [
        (data['Timestamp'], data['Open'], data['High'], data['Low'], data['Close'],
         data['Volume'], data['CloseTime'], data['QuoteVolume'], data['TradeCount'],
         data['TakerBuyBaseVolume'], data['TakerBuyQuoteVolume'])
        for data in data_list
    ]

    cursor.executemany(insert_sql, values)
    conn.commit()

    # 记录插入后的行数，计算实际新增数量
    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
    count_after = cursor.fetchone()[0]

    new_count = count_after - count_before
    if new_count == 0 and data_list:
        print(f"ℹ️  所有 {len(data_list)} 条记录都已存在于数据库中")

    conn.close()

    return new_count

def get_proxy_config(proxy_arg=None):
    """获取代理配置"""
    proxies = {}
    
    # 优先级：命令行参数 > 环境变量 > 无代理
    if proxy_arg:
        # 命令行指定的代理
        if proxy_arg.startswith('http://') or proxy_arg.startswith('https://'):
            proxies['http'] = proxy_arg
            proxies['https'] = proxy_arg
        else:
            # 假设是SOCKS5或HTTP代理，添加协议前缀
            if 'socks5' in proxy_arg:
                 proxies['http'] = f'socks5://{proxy_arg}'
                 proxies['https'] = f'socks5://{proxy_arg}'
            else:
                 proxies['http'] = f'http://{proxy_arg}'
                 proxies['https'] = f'http://{proxy_arg}'
        print(f"🔗 使用命令行代理: {proxies['https']}")
    else:
        # 检查环境变量
        http_proxy = os.environ.get('HTTP_PROXY') or os.environ.get('http_proxy')
        https_proxy = os.environ.get('HTTPS_PROXY') or os.environ.get('https_proxy')
        
        if http_proxy:
            proxies['http'] = http_proxy
            print(f"🔗 使用环境变量HTTP代理: {http_proxy}")
        if https_proxy:
            proxies['https' if 'socks' not in https_proxy else 'http'] = https_proxy
            print(f"🔗 使用环境变量HTTPS代理: {https_proxy}")
    
    return proxies if proxies else None

def fetch_klines(symbol, interval, limit=1000, end_time=None, start_time=None, proxies=None, market="futures", max_retries=5, retry_delay=3):
    """获取单批K线数据（带重试机制）"""
    if market == "spot":
        url = "https://api.binance.com/api/v3/klines"
    else:
        url = "https://fapi.binance.com/fapi/v1/klines"
    
    params = {
        'symbol': symbol,
        'interval': interval,
        'limit': limit
    }
    
    if end_time:
        params['endTime'] = end_time
    if start_time:
        params['startTime'] = start_time
    
    request_kwargs = {'params': params, 'timeout': 30}
    if proxies:
        request_kwargs['proxies'] = proxies

    for attempt in range(max_retries):
        try:
            response = requests.get(url, **request_kwargs)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            # 使用\n换行，防止破坏上一行的 'end=""' 格式
            print(f"\n❌ 获取K线数据失败 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                print(f"🔄 {retry_delay}秒后重试...")
                time.sleep(retry_delay)
            else:
                print("❌ 已达到最大重试次数，放弃获取。")

    return None
def get_historical_data(symbol='BTCUSDT', interval='1m', limit=10000, db_path='btc_data.db', 
                       start_time=None, end_time=None, proxies=None, market="futures", max_retry=5):
    """获取历史数据（支持大量数据分批获取）"""
    try:
        print(f"📚 开始获取历史数据")
        print(f"📊 交易对: {symbol}")
        print(f"⏱️  间隔: {interval}")
        if not start_time:
            print(f"🎯 目标: {limit} 条记录")
        print(f"📁 数据库: {db_path}")
        print(f"🛒 市场: {'合约' if market == 'futures' else '现货'}")
        if start_time:
            print(f"📅 开始时间: {format_timestamp(start_time/1000)}")
        if end_time:
            print(f"📅 结束时间: {format_timestamp(end_time/1000)}")
        if proxies:
            print(f"🔗 代理: {list(proxies.values())[0] if proxies else '无'}")
        print("-" * 50)
        
        init_database(db_path)
        table_name = create_table_if_not_exists(db_path, symbol, interval, market)
        print(f"📊 使用表: {table_name}")
        
        max_limit_per_request = 1000
        all_data = []
        total_new_records = 0
        
        # 如果提供了开始时间，则目标是获取整个时间范围的数据，忽略 limit
        is_date_range_mode = bool(start_time)
        
        if limit <= max_limit_per_request and not is_date_range_mode:
            print(f"🔄 单次获取最新的 {limit} 条记录...")
            klines = fetch_klines(symbol, interval, limit=limit, proxies=proxies, market=market, max_retries=max_retry)
            if klines:
                data_list = [
                    {'Timestamp': int(k[0]) // 1000, 'Open': float(k[1]), 'High': float(k[2]), 'Low': float(k[3]), 'Close': float(k[4]), 'Volume': float(k[5]), 'CloseTime': int(k[6]) // 1000, 'QuoteVolume': float(k[7]), 'TradeCount': int(k[8]), 'TakerBuyBaseVolume': float(k[9]), 'TakerBuyQuoteVolume': float(k[10])}
                    for k in klines
                ]
                new_records = save_data_to_db(db_path, table_name, data_list)
                total_new_records += new_records
                all_data.extend(klines)
        else:
            # 分批获取
            remaining = limit
            current_end_time = end_time if end_time else int(time.time() * 1000)
            
            # 在时间范围模式下，循环条件变为 "只要还没到开始时间就继续"
            # 在数量限制模式下，循环条件是 "只要还没达到数量限制"
            while (is_date_range_mode) or (remaining > 0):
                
                # 如果是按数量获取，计算本次批量大小
                batch_limit = max_limit_per_request if is_date_range_mode else min(remaining, max_limit_per_request)
                
                print(f"🔄 正在获取 {format_timestamp(current_end_time/1000)} 之前的 {batch_limit} 条数据...", end="", flush=True)
                
                # *** 核心修正：在循环中不再传递固定的 start_time ***
                klines = fetch_klines(symbol, interval, limit=batch_limit,
                                    end_time=current_end_time,
                                    proxies=proxies, market=market, max_retries=max_retry)

                if not klines:
                    print(" 失败❌ (未返回任何数据)")
                    break
                
                # 如果有开始时间，检查获取到的最早数据是否已经越过开始时间
                if start_time and int(klines[0][0]) < start_time:
                    # 过滤掉早于 start_time 的数据
                    klines = [k for k in klines if int(k[0]) >= start_time]
                    print(f" 过滤到开始时间...", end="")
                    if not klines: # 过滤后可能为空
                        print(" 完成✅ (无新增记录)")
                        break

                data_list = [
                    {'Timestamp': int(k[0]) // 1000, 'Open': float(k[1]), 'High': float(k[2]), 'Low': float(k[3]), 'Close': float(k[4]), 'Volume': float(k[5]), 'CloseTime': int(k[6]) // 1000, 'QuoteVolume': float(k[7]), 'TradeCount': int(k[8]), 'TakerBuyBaseVolume': float(k[9]), 'TakerBuyQuoteVolume': float(k[10])}
                    for k in klines
                ]

                new_records = save_data_to_db(db_path, table_name, data_list)
                total_new_records += new_records
                all_data = klines + all_data
                
                print(f" 完成✅ (新增: {new_records}, 总计: {len(all_data)})")
                
                if not is_date_range_mode:
                    remaining -= len(klines)
                
                current_end_time = int(klines[0][0]) - 1
                
                # 检查是否已达到或越过开始时间，这是时间范围模式下的主要退出条件
                if start_time and current_end_time < start_time:
                    print("📅 已达到指定的开始时间，停止获取。")
                    break
                
                # 如果API返回的数据少于请求数量，说明已到历史尽头
                if len(klines) < max_limit_per_request:
                    print("📅 已获取到最早的数据，停止获取。")
                    break
                
                time.sleep(0.2)

        if not all_data:
            print("❌ 没有获取到任何数据")
            return False
        
        print(f"\n✅ 历史数据获取任务完成")
        print(f"💾 总计新增记录: {total_new_records} 条")
        print(f"📁 数据库: {db_path}")
        print(f"📊 表名: {table_name}")
        
        unique_data = sorted(list({k[0]: k for k in all_data}.values()), key=lambda x: x[0])
        if unique_data:
            start_ts = int(unique_data[0][0]) // 1000
            end_ts = int(unique_data[-1][0]) // 1000
            print(f"📅 获取的时间范围: {format_timestamp(start_ts)} 到 {format_timestamp(end_ts)}")
            time_span_hours = (end_ts - start_ts) / 3600
            print(f"⏰ 时间跨度: {time_span_hours:.1f} 小时 ({time_span_hours/24:.1f} 天)")
        
        return True
        
    except KeyboardInterrupt:
        print(f"\n📋 用户中断，正在退出...")
        return False
    except Exception as e:
        print(f"\n❌ 获取历史数据时发生未知错误: {e}")
        import traceback
        traceback.print_exc()
        return False
def get_historical_data1(symbol='BTCUSDT', interval='1m', limit=10000, db_path='btc_data.db', 
                       start_time=None, end_time=None, proxies=None, market="futures", max_retry=5):
    """获取历史数据（支持大量数据分批获取）"""
    try:
        print(f"📚 开始获取历史数据")
        print(f"📊 交易对: {symbol}")
        print(f"⏱️  间隔: {interval}")
        print(f"🎯 目标: {limit} 条记录")
        print(f"📁 数据库: {db_path}")
        print(f"🛒 市场: {'合约' if market == 'futures' else '现货'}")
        if start_time:
            print(f"📅 开始时间: {format_timestamp(start_time/1000)}")
        if end_time:
            print(f"📅 结束时间: {format_timestamp(end_time/1000)}")
        if proxies:
            print(f"🔗 代理: {list(proxies.values())[0] if proxies else '无'}")
        print("-" * 50)
        
        init_database(db_path)
        table_name = create_table_if_not_exists(db_path, symbol, interval, market)
        print(f"📊 使用表: {table_name}")
        
        max_limit_per_request = 1000
        all_data = []
        total_new_records = 0
        
        if limit <= max_limit_per_request and not start_time and not end_time:
            print(f"🔄 单次获取最新的 {limit} 条记录...")
            klines = fetch_klines(symbol, interval, limit=limit, proxies=proxies, market=market, max_retries=max_retry)
            if klines:
                # 转换和保存数据
                data_list = [
                    {'Timestamp': int(k[0]) // 1000, 'Open': float(k[1]), 'High': float(k[2]), 'Low': float(k[3]), 'Close': float(k[4]), 'Volume': float(k[5]), 'CloseTime': int(k[6]) // 1000, 'QuoteVolume': float(k[7]), 'TradeCount': int(k[8]), 'TakerBuyBaseVolume': float(k[9]), 'TakerBuyQuoteVolume': float(k[10])}
                    for k in klines
                ]

                new_records = save_data_to_db(db_path, table_name, data_list)
                total_new_records += new_records
                all_data.extend(klines)
        else:
            # 分批获取
            remaining = limit
            current_end_time = end_time if end_time else int(time.time() * 1000)
            
            while remaining > 0:
                batch_limit = min(remaining, max_limit_per_request)
                
                # 如果有开始时间，确保不会获取早于开始时间的数据
                if start_time and current_end_time < start_time:
                    print("📅 已达到指定的开始时间，停止获取。")
                    break

                print(f"🔄 正在获取 {format_timestamp(current_end_time/1000)} 之前的 {batch_limit} 条数据...", end="", flush=True)
                
                klines = fetch_klines(symbol, interval, limit=batch_limit,
                                    end_time=current_end_time, start_time=start_time,
                                    proxies=proxies, market=market, max_retries=max_retry)



                if not klines:
                    print(" 失败❌")
                    break
                
                # 转换和保存数据
                data_list = [
                    {'Timestamp': int(k[0]) // 1000, 'Open': float(k[1]), 'High': float(k[2]), 'Low': float(k[3]), 'Close': float(k[4]), 'Volume': float(k[5]), 'CloseTime': int(k[6]) // 1000, 'QuoteVolume': float(k[7]), 'TradeCount': int(k[8]), 'TakerBuyBaseVolume': float(k[9]), 'TakerBuyQuoteVolume': float(k[10])}
                    for k in klines
                ]

                new_records = save_data_to_db(db_path, table_name, data_list)
                total_new_records += new_records

                # 更新all_data用于最终统计
                all_data = klines + all_data
                
                print(f" 完成✅ (新增: {new_records}, 总计: {len(all_data)})")
                
                remaining -= len(klines)
                
                # 设置下一批的结束时间为当前批最早的时间戳 - 1毫秒，以避免重复获取
                current_end_time = int(klines[0][0]) - 1
                
                time.sleep(0.2)
                
                if len(klines) < max_limit_per_request:
                    print("📅 已获取到最早的数据，停止获取。")
                    break
        
        if not all_data:
            print("❌ 没有获取到任何数据")
            return False
        
        print(f"\n✅ 历史数据获取任务完成")
        print(f"💾 总计新增记录: {total_new_records} 条")
        print(f"📁 数据库: {db_path}")
        print(f"📊 表名: {table_name}")
        
        # 重新排序并去重，以获取准确的时间范围
        unique_data = sorted(list({k[0]: k for k in all_data}.values()), key=lambda x: x[0])
        if unique_data:
            start_ts = int(unique_data[0][0]) // 1000
            end_ts = int(unique_data[-1][0]) // 1000
            print(f"📅 获取的时间范围: {format_timestamp(start_ts)} 到 {format_timestamp(end_ts)}")
            time_span_hours = (end_ts - start_ts) / 3600
            print(f"⏰ 时间跨度: {time_span_hours:.1f} 小时 ({time_span_hours/24:.1f} 天)")
        
        return True
        
    except KeyboardInterrupt:
        print(f"\n📋 用户中断，正在退出...")
        return False
    except Exception as e:
        print(f"\n❌ 获取历史数据时发生未知错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def list_tables(db_path: str = "btc_data.db"):
    """列出数据库中的所有表"""
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return

    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        cursor.execute("SELECT table_name, symbol, interval, market, created_at FROM table_info ORDER BY created_at DESC")
        tables = cursor.fetchall()
    except sqlite3.OperationalError:
        print("📊 数据库中没有找到 table_info 表，可能是旧版数据库。")
        tables = []

    conn.close()
    
    if not tables:
        print("📊 数据库中没有找到任何表信息。")
        return
    
    print(f"📊 数据库 {db_path} 中的表:")
    print("-" * 100)
    print(f"{'表名':<40} {'交易对':<15} {'间隔':<10} {'市场':<10} {'创建时间'}")
    print("-" * 100)
    
    for table_name, symbol, interval, market, created_at in tables:
        print(f"{table_name:<40} {symbol:<15} {interval:<10} {market or 'unknown':<10} {created_at}")

def get_table_stats(db_path: str, table_name: str):
    """获取表的统计信息"""
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
        
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 获取记录数
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        count = cursor.fetchone()[0]
        
        # 获取时间范围
        cursor.execute(f"SELECT MIN(timestamp), MAX(timestamp) FROM {table_name}")
        min_time, max_time = cursor.fetchone()
        
    except sqlite3.OperationalError as e:
        print(f"❌ 查询表 '{table_name}' 失败: {e}")
        conn.close()
        return

    conn.close()
    
    if count == 0:
        print(f"📊 表 {table_name} 中没有数据")
        return
    
    print(f"📊 表 '{table_name}' 统计信息:")
    print(f"📈 记录数: {count:,}")
    if min_time and max_time:
        print(f"📅 时间范围: {format_timestamp(min_time)} 到 {format_timestamp(max_time)}")
        time_span_hours = (max_time - min_time) / 3600
        print(f"⏰ 时间跨度: {time_span_hours:.1f} 小时 ({time_span_hours/24:.1f} 天)")

def test_connection(proxies=None, max_retries=3, retry_delay=2):
    """测试网络连接（带重试）"""
    print("🔍 正在测试网络连接...")
    
    request_kwargs = {'url': "https://api.binance.com/api/v3/time", 'timeout': 10}
    if proxies:
        request_kwargs['proxies'] = proxies
        
    for attempt in range(max_retries):
        try:
            response = requests.get(**request_kwargs)
            response.raise_for_status()
            server_time = response.json()['serverTime']
            print(f"✅ 连接正常，服务器时间: {format_timestamp(server_time/1000)}")
            return True
        except requests.exceptions.RequestException as e:
            print(f"❌ 连接测试失败 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                print(f"🔄 {retry_delay}秒后重试...")
                time.sleep(retry_delay)
            else:
                print("❌ 已达到最大重试次数，请检查网络或代理配置。")
    return False

def parse_datetime(datetime_str: str) -> Optional[int]:
    """解析日期时间字符串为毫秒时间戳"""
    try:
        # 支持多种格式
        formats = [
            '%Y-%m-%d %H:%M:%S', '%Y-%m-%d %H:%M', '%Y-%m-%d',
            '%Y/%m/%d %H:%M:%S', '%Y/%m/%d %H:%M', '%Y/%m/%d'
        ]
        
        dt_obj = None
        for fmt in formats:
            try:
                dt_obj = datetime.strptime(datetime_str, fmt)
                break
            except ValueError:
                continue
        
        if dt_obj is None:
            raise ValueError(f"无法解析日期时间格式: {datetime_str}")
            
        # 转换为UTC毫秒时间戳
        return int(dt_obj.replace(tzinfo=timezone.utc).timestamp() * 1000)
    except Exception as e:
        print(f"❌ 日期时间解析失败: {e}")
        sys.exit(1)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="快速获取币安历史K线数据并保存到SQLite。",
        formatter_class=argparse.RawTextHelpFormatter
    )
    parser.add_argument("--symbol", default="BTCUSDT", help="交易对符号 (默认: BTCUSDT)")
    parser.add_argument("--interval", default="15m", help="K线间隔 (e.g., 1m, 5m, 1h, 1d) (默认: 1m)")
    parser.add_argument("--limit", type=int, default=10000, help="获取数据条数 (默认: 10000)")
    parser.add_argument("--db", default="coin_data.db", help="数据库文件名 (默认: coin_data.db)")
    parser.add_argument("--proxy", help="代理服务器地址 (e.g., 127.0.0.1:7890 or socks5://127.0.0.1:1080)")
    parser.add_argument("--market", choices=["spot", "futures"], default="spot", help="市场类型: spot(现货) 或 futures(合约) (默认: futures)")
    parser.add_argument("--start-time", help="开始时间 (格式: 'YYYY-MM-DD HH:MM:SS' 或 'YYYY-MM-DD')")
    parser.add_argument("--end-time", help="结束时间 (格式: 'YYYY-MM-DD HH:MM:SS' 或 'YYYY-MM-DD')")
    parser.add_argument("--max-retry", type=int, default=5, help="网络请求失败时的最大重试次数 (默认: 5)")

    # 功能性参数
    parser.add_argument("--list-tables", action="store_true", help="列出数据库中的所有数据表并退出")
    parser.add_argument("--table-stats", metavar="TABLE_NAME", help="显示指定表的统计信息并退出")
    
    args = parser.parse_args()
    
    # 处理功能性命令
    if args.list_tables:
        list_tables(args.db)
        return
    
    if args.table_stats:
        get_table_stats(args.db, args.table_stats)
        return
    
    # 解析时间参数 (转换为毫秒时间戳)
    start_time_ms = parse_datetime(args.start_time) if args.start_time else None
    end_time_ms = parse_datetime(args.end_time) if args.end_time else None
    
    # 获取代理配置
    proxies = get_proxy_config(args.proxy)
    
    # 检查网络连接
    if not test_connection(proxies, max_retries=args.max_retry):
        sys.exit(1)
    
    # 获取历史数据
    success = get_historical_data(
        symbol=args.symbol.upper(),
        interval=args.interval,
        limit=args.limit,
        db_path=args.db,
        start_time=start_time_ms,
        end_time=end_time_ms,
        proxies=proxies,
        market=args.market,
        max_retry=args.max_retry
    )
    
    if success:
        print(f"\n🎉 任务成功完成！")
        print(f"💡 可使用以下命令查看表信息:")
        table_name_generated = get_table_name(args.symbol.upper(), args.interval, args.market)
        print(f"   python {sys.argv[0]} --list-tables --db {args.db}")
        print(f"   python {sys.argv[0]} --table-stats {table_name_generated} --db {args.db}")
    else:
        print(f"\n❌ 任务失败。")
        sys.exit(1)

if __name__ == "__main__":
    main()

# 使用示例：
#
# 获取10000条BTC 1分钟合约数据：
# python get_history.py --symbol BTCUSDT --interval 1m --limit 10000
#
# 获取50000条ETH 5分钟现货数据，并设置10次重试：
# python get_history.py --symbol ETHUSDT --interval 5m --limit 50000 --market spot --max-retry 10
#
# 获取指定时间段的数据（将获取该时段内所有数据，limit参数会被忽略）：
# python get_history.py --symbol BTCUSDT --interval 1h --start-time "2023-01-01" --end-time "2023-01-31"
#
# 使用代理获取数据：
# python get_history.py --proxy 127.0.0.1:7890
#
# 列出所有表：
# python get_history.py --list-tables --db coin_data.db
#
# 查看表统计信息：
# python get_history.py --table-stats BTCUSDT_1min_futures --db coin_data.db