import argparse
import os
import json
import subprocess
import re
from datetime import datetime

CONFIG_PATH = os.path.join(os.path.dirname(__file__), 'config.json')

# 获取数据文件名
def get_data_filename(coin, count, interval):
    return os.path.join("data", f"{coin.upper()}USDT_{interval}_{count}.csv")

def ask_yes_no(prompt):
    while True:
        ans = input(f"{prompt} [y/n]: ").strip().lower()
        if ans in ("y", "yes"): return True
        if ans in ("n", "no"): return False

# 自动更新config.json
def update_config(coin, count, interval, data_file):
    # 读取原始config
    if not os.path.exists(CONFIG_PATH):
        print(f"未找到config.json，自动创建...")
        config = {"coin_configs": {}}
    else:
        with open(CONFIG_PATH, 'r', encoding='utf-8') as f:
            config = json.load(f)
        if "coin_configs" not in config:
            config["coin_configs"] = {}
    coin = coin.upper()
    key = coin
    # 自动生成模型名
    model_basename = f"{coin.lower()}_{interval}"
    # 生成api_symbol和display_name
    api_symbol = f"{coin}USDT"
    display_name = f"{coin}/USDT"
    # 推断周期
    timeframe_minutes = int(interval.replace('m','')) if 'm' in interval else 15
    # 默认参数
    up_threshold = 0.05
    down_threshold = 0.05
    max_lookforward_minutes = 1440
    price_multiplier = 1.0
    # 允许保留原有参数
    old = config["coin_configs"].get(key, {})
    config["coin_configs"][key] = {
        "csv_file": data_file,
        "api_symbol": api_symbol,
        "display_name": display_name,
        "timeframe_minutes": timeframe_minutes,
        "up_threshold": old.get("up_threshold", up_threshold),
        "down_threshold": old.get("down_threshold", down_threshold),
        "max_lookforward_minutes": old.get("max_lookforward_minutes", max_lookforward_minutes),
        "model_basename": model_basename,
        "price_multiplier": old.get("price_multiplier", price_multiplier)
    }
    with open(CONFIG_PATH, 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
    print(f"config.json已更新: coin_configs.{key}")

# 从训练输出中提取测试集开始时间
def extract_test_start_time(train_output):
    """从训练输出中提取测试集的开始时间"""
    # 查找测试集开始时间的模式
    # 匹配格式: "测试集: 1234 (2024-01-01 12:00:00 to 2024-01-02 12:00:00) [2024-01-01 12:00:00]"
    pattern = r"测试集: \d+ \(([^)]+) to"
    match = re.search(pattern, train_output)
    if match:
        test_start_time = match.group(1).strip()
        print(f"提取到测试集开始时间: {test_start_time}")
        return test_start_time
    else:
        print("警告: 无法从训练输出中提取测试集开始时间")
        print("训练输出片段:")
        # 打印包含"测试集"的行以便调试
        lines = train_output.split('\n')
        for line in lines:
            if '测试集' in line:
                print(f"  {line}")
        return None

# 执行shell命令并返回输出
def run_cmd_with_output(cmd, check=True):
    print(f"\n>>> 执行: {cmd}")
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    if check and result.returncode != 0:
        print(f"命令失败: {cmd}")
        print(f"错误输出: {result.stderr}")
        exit(1)
    return result.stdout

# 执行shell命令
def run_cmd(cmd, check=True):
    print(f"\n>>> 执行: {cmd}")
    result = subprocess.run(cmd, shell=True)
    if check and result.returncode != 0:
        print(f"命令失败: {cmd}")
        exit(1)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="一键自动化工作流：下载、配置、训练、回测")
    parser.add_argument("coin", help="币种，如ETH")
    parser.add_argument("count", type=int, help="下载K线数量")
    parser.add_argument("interval", help="K线周期，如5m")
    parser.add_argument("steps", nargs='+', help="要执行的步骤，如download train backtest")
    parser.add_argument("--force-download", action='store_true', help="强制重新下载数据")
    args = parser.parse_args()

    coin = args.coin.upper()
    count = args.count
    interval = args.interval
    steps = [s.lower() for s in args.steps]
    data_file = get_data_filename(coin, count, interval)
    csv_file_for_json = data_file  # config.json中直接写data/xxx.csv

    # 存储测试集开始时间
    test_start_time = None

    # 1. 下载数据
    if 'download' in steps:
        if os.path.exists(data_file) and not args.force_download:
            print(f"数据文件已存在: {data_file}")
            if not ask_yes_no("是否重新下载?"):
                print("跳过下载...")
            else:
                run_cmd(f"sh download.sh {coin}USDT {count} {interval} {data_file}")
        else:
            run_cmd(f"sh download.sh {coin}USDT {count} {interval} {data_file}")

    # 2. 更新config.json
    if 'train' in steps or 'backtest' in steps:
        update_config(coin, count, interval, csv_file_for_json)

    # 3. 训练
    if 'train' in steps:
        train_output = run_cmd_with_output(f"python train.py --coin {coin}")
        # 从训练输出中提取测试集开始时间
        test_start_time = extract_test_start_time(train_output)

    # 4. 回测
    if 'backtest' in steps:
        # 如果训练阶段提取到了测试集开始时间，使用它作为start_time
        if test_start_time:
            # 将时间格式转换为backtest.py期望的格式
            # 假设test_start_time格式为 "2024-01-01 12:00:00"
            # 需要转换为 "2024-01-01 12:00:00" 格式
            try:
                # 解析时间并重新格式化
                dt = datetime.strptime(test_start_time, '%Y-%m-%d %H:%M:%S')
                formatted_time = dt.strftime('%Y-%m-%d %H:%M:%S')
                print(f"使用测试集开始时间进行回测: {formatted_time}")
                run_cmd(f"python backtest.py --coin {coin} --start-time '{formatted_time}'")
            except ValueError as e:
                print(f"时间格式转换失败: {e}")
                print("使用默认时间进行回测...")
                run_cmd(f"python backtest.py --coin {coin}")
        else:
            print("未找到测试集开始时间，使用默认时间进行回测...")
            run_cmd(f"python backtest.py --coin {coin}")

    print("\n✅ 一键流程已完成！") 