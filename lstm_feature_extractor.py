# lstm_feature_extractor.py

import numpy as np
import pandas as pd
import tensorflow as tf
from tensorflow.keras.models import Model
from tensorflow.keras.layers import LSTM, Dense, Dropout, Input
from tensorflow.keras.callbacks import EarlyStopping
from sklearn.preprocessing import StandardScaler

def create_sequences(data, n_steps_in):
    """
    将时间序列数据转换为LSTM所需的3D序列格式 (samples, timesteps, features)。
    """
    X = []
    # 我们从 n_steps_in - 1 的索引开始，这样每个样本都有足够的回看数据
    for i in range(n_steps_in - 1, len(data)):
        # 提取过去 n_steps_in 的数据作为输入序列
        X.append(data[i - (n_steps_in - 1):i + 1])
    # 返回与原始数据对齐的序列
    return np.array(X)

def build_lstm_classifier(n_steps_in, n_features, lstm_units=64):
    """
    构建一个用于特征提取的LSTM分类模型。
    """
    print(f"构建LSTM模型：输入维度=({n_steps_in}, {n_features}), LSTM单元数={lstm_units}")
    input_layer = Input(shape=(n_steps_in, n_features))
    # LSTM层，我们之后会提取这一层的输出
    lstm_layer = LSTM(lstm_units, activation='relu', name='lstm_feature_layer')(input_layer)
    dropout_layer = Dropout(0.3)(lstm_layer)
    # 输出层用于训练模型
    output_layer = Dense(1, activation='sigmoid')(dropout_layer)

    model = Model(inputs=input_layer, outputs=output_layer)
    model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['auc'])
    print("LSTM模型构建完成。")
    model.summary()
    return model

def train_and_extract_features(train_df, val_df, test_df, lstm_input_features, target_col, n_steps_in=30, lstm_units=32, epochs=50, batch_size=256):
    """
    训练LSTM模型，并用它来为所有数据集提取特征。
    这是实现混合模型的关键函数。
    """
    print("\n" + "="*20 + " 开始LSTM特征提取流程 " + "="*20)

    # 1. 特征缩放
    print("步骤1: 对LSTM输入特征进行标准化...")
    scaler = StandardScaler()
    train_df_scaled = scaler.fit_transform(train_df[lstm_input_features])
    val_df_scaled = scaler.transform(val_df[lstm_input_features])
    test_df_scaled = scaler.transform(test_df[lstm_input_features])

    # 2. 创建序列数据
    print(f"步骤2: 创建长度为 {n_steps_in} 的数据序列...")
    X_train_seq = create_sequences(train_df_scaled, n_steps_in)
    X_val_seq = create_sequences(val_df_scaled, n_steps_in)
    X_test_seq = create_sequences(test_df_scaled, n_steps_in)

    # 标签需要与序列对齐（取每个序列结束时的标签）
    y_train = train_df[target_col].iloc[n_steps_in - 1:].values
    y_val = val_df[target_col].iloc[n_steps_in - 1:].values

    # 3. 构建和训练LSTM模型
    print("步骤3: 构建并训练LSTM分类器...")
    n_features = len(lstm_input_features)
    lstm_classifier = build_lstm_classifier(n_steps_in, n_features, lstm_units)

    early_stopping = EarlyStopping(monitor='val_auc', patience=5, mode='max', restore_best_weights=True)

    lstm_classifier.fit(
        X_train_seq, y_train,
        validation_data=(X_val_seq, y_val),
        epochs=epochs,
        batch_size=batch_size,
        callbacks=[early_stopping],
        verbose=1
    )

    # 4. 创建特征提取器
    print("步骤4: 创建特征提取器 (从训练好的LSTM模型中)...")
    feature_extractor = Model(
        inputs=lstm_classifier.input,
        outputs=lstm_classifier.get_layer('lstm_feature_layer').output
    )

    # 5. 提取特征
    print("步骤5: 为所有数据集生成LSTM特征...")
    lstm_features_train = feature_extractor.predict(X_train_seq)
    lstm_features_val = feature_extractor.predict(X_val_seq)
    lstm_features_test = feature_extractor.predict(X_test_seq)

    # 6. 将特征转换为DataFrame，并确保索引正确对齐
    print("步骤6: 将LSTM特征转换为DataFrame并对齐索引...")
    lstm_feature_cols = [f'lstm_feat_{i}' for i in range(lstm_units)]

    # 特征的索引应该对应于每个序列的最后一个时间点
    train_indices = train_df.index[n_steps_in - 1:]
    val_indices = val_df.index[n_steps_in - 1:]
    test_indices = test_df.index[n_steps_in - 1:]

    lstm_features_train_df = pd.DataFrame(lstm_features_train, index=train_indices, columns=lstm_feature_cols)
    lstm_features_val_df = pd.DataFrame(lstm_features_val, index=val_indices, columns=lstm_feature_cols)
    lstm_features_test_df = pd.DataFrame(lstm_features_test, index=test_indices, columns=lstm_feature_cols)

    print("="*20 + " LSTM特征提取流程完成 " + "="*20 + "\n")

    return lstm_features_train_df, lstm_features_val_df, lstm_features_test_df