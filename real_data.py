# real_data.py

import pandas as pd
import requests
import time
import logging
from datetime import datetime, timezone
from typing import Callable, Optional
import pytz

# A list of Binance API endpoints to cycle through for resilience.
BINANCE_API_ENDPOINTS = [
    "https://api.binance.com",
    "https://api-gcp.binance.com",
    "https://api1.binance.com",
    "https://api2.binance.com",
    "https://api3.binance.com",
    "https://api4.binance.com",
]

class MarketDataFetcher:
    """
    行情获取模块
    - Fetches K-line data and the latest price from the API.
    - Notifies the signal generator via a callback when new K-line data is available.
    """
    def __init__(self, api_symbol: str, timeframe_minutes: int, update_interval: int,
                 logger: logging.Logger, display_name: str, local_tz: pytz.BaseTzInfo):
        self.api_symbol = api_symbol
        self.timeframe_minutes = timeframe_minutes
        self.update_interval = update_interval
        self.logger = logger
        self.display_name = display_name
        self.local_tz = local_tz
        self.running = True
        self.kline_handler: Optional[Callable[[Optional[pd.DataFrame], float], None]] = None
        self.last_kline_timestamp = None

    def register_kline_handler(self, handler: Callable[[Optional[pd.DataFrame], float], None]):
        """Registers a callback function to handle new K-line data."""
        self.logger.info("行情模块：信号生成器的回调函数已注册。")
        self.kline_handler = handler

    def _fetch_binance_klines(self, limit=1000) -> Optional[pd.DataFrame]:
        """Fetches K-line data from Binance."""
        params = {'symbol': self.api_symbol, 'interval': f'{self.timeframe_minutes}m', 'limit': limit}
        for base_url in BINANCE_API_ENDPOINTS:
            try:
                url = f"{base_url}/api/v3/klines"
                response = requests.get(url, params=params, timeout=10)
                response.raise_for_status()
                klines = response.json()
                if not klines: return None
                df = pd.DataFrame(klines, columns=['Timestamp', 'Open', 'High', 'Low', 'Close', 'Volume', 'CloseTime', 'QuoteVolume', 'TradeCount', 'TakerBuyBaseVolume', 'TakerBuyQuoteVolume', 'Ignore'])
                df = df.astype({'Open': float, 'High': float, 'Low': float, 'Close': float, 'Volume': float})
                df['Timestamp'] = pd.to_datetime(df['Timestamp'], unit='ms', utc=True)
                df.set_index('Timestamp', inplace=True)
                return df[['Open', 'High', 'Low', 'Close', 'Volume']].rename(columns=str.lower)
            except requests.exceptions.RequestException:
                continue
        self.logger.error("所有Binance API节点均无法连接，获取K线数据失败。")
        return None

    def _fetch_current_price(self) -> Optional[float]:
        """Fetches the latest trade price."""
        try:
            url = f"https://api.binance.com/api/v3/ticker/price?symbol={self.api_symbol}"
            response = requests.get(url, timeout=5)
            response.raise_for_status()
            return float(response.json()['price'])
        except requests.exceptions.RequestException as e:
            self.logger.warning(f"获取最新价格失败: {e}")
            return None

    def run(self):
        """Starts the main loop."""
        while self.running:
            try:
                df = self._fetch_binance_klines(limit=1000)
                if df is None or df.empty:
                    time.sleep(self.update_interval)
                    continue
                # 获取最新价格可以使用最后k线
                # current_price = self._fetch_current_price()
                current_kline_timestamp = df.index[-1]
                current_price = df.iloc[-1]['Close']
                if self.last_kline_timestamp and current_kline_timestamp <= self.last_kline_timestamp:
                    if self.kline_handler and current_price is not None:
                        self.kline_handler(None, current_price)
                else:
                    local_kline_time_str = current_kline_timestamp.astimezone(self.local_tz).strftime('%Y-%m-%d %H:%M:%S %z')
                    coin_tag = self.display_name.split('/')[0]
                    self.logger.info(f"[{coin_tag}] 检测到新的K线: {local_kline_time_str}，开始分析上一根已收盘K线...")

                    self.last_kline_timestamp = current_kline_timestamp
                    if self.kline_handler and current_price is not None:
                        self.kline_handler(df, current_price)

                time.sleep(self.update_interval)

            except KeyboardInterrupt:
                self.running = False
            except Exception as e:
                self.logger.error(f"行情模块主循环发生错误: {e}", exc_info=True)
                time.sleep(15)
        self.logger.info("行情获取模块已停止。")