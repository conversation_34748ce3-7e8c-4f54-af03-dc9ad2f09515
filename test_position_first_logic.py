#!/usr/bin/env python3
"""
测试新的Position-First逻辑
验证仓位状态管理和订单跟踪
"""

import sys
import logging
from datetime import datetime, timezone
import pytz

# 添加trade目录到路径
sys.path.append('trade')

from trade.real_manager import PortfolioManager
from trade.database_manager import DatabaseManager
from trade.order_manager import OrderManager, OrderSide, OrderType

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger('TestPositionFirst')

def test_position_first_logic():
    """测试Position-First逻辑"""
    logger = setup_logging()
    
    # 创建数据库管理器
    db_manager = DatabaseManager("test_position_first.db")
    
    # 测试配置
    config = {
        'risk_per_order_pct': 1.0,
        'max_active_positions': 3,
        'paper_usdt_balance': 1000.0,
        'up_threshold': 0.02,
        'down_threshold': 0.02,
        'max_lookforward_minutes': 60,
        'order_manager': {
            'live_trading': False,
            'paper_usdt_balance': 1000.0
        }
    }
    
    try:
        # 创建PortfolioManager
        portfolio_mgr = PortfolioManager(
            config=config,
            logger=logger,
            display_name="ETH/USDT",
            local_tz=pytz.timezone('Asia/Shanghai'),
            enable_sound=False,
            db_manager=db_manager
        )
        
        logger.info("✅ PortfolioManager创建成功")
        logger.info(f"   风险比例: {portfolio_mgr.risk_per_order_pct}%")
        logger.info(f"   最大仓位: {portfolio_mgr.max_active_positions}")
        
        # 模拟交易信号
        test_signal = {
            'guess': 1,  # 看涨
            'probability': 0.85,
            'price': 3000.0,
            'timestamp': datetime.now(timezone.utc),
            'signal_id': 'test_signal_001'
        }
        
        logger.info("📊 测试信号:")
        logger.info(f"   方向: {'看涨' if test_signal['guess'] == 1 else '看跌'}")
        logger.info(f"   价格: {test_signal['price']}")
        logger.info(f"   信心: {test_signal['probability']}")
        
        # 处理信号
        portfolio_mgr.on_new_signal(test_signal)
        
        # 检查仓位状态
        active_positions = portfolio_mgr.active_positions
        logger.info(f"📈 活跃仓位数量: {len(active_positions)}")
        
        for pos_id, pos in active_positions.items():
            logger.info(f"   仓位 {pos_id}:")
            logger.info(f"     状态: {pos['status']}")
            logger.info(f"     开仓订单ID: {pos.get('entry_order_id', 'N/A')}")
            logger.info(f"     目标价格: 涨{pos['up_target']:.2f}, 跌{pos['down_target']:.2f}")
        
        # 模拟价格更新 - 达到上涨目标
        logger.info("🔄 模拟价格达到上涨目标...")
        portfolio_mgr.on_price_update(3060.0)  # 2%上涨
        
        # 再次检查仓位状态
        logger.info(f"📈 更新后活跃仓位数量: {len(active_positions)}")
        for pos_id, pos in active_positions.items():
            logger.info(f"   仓位 {pos_id}: 状态 = {pos['status']}")
        
        # 打印状态摘要
        portfolio_mgr.print_status()
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_schema():
    """测试数据库schema更新"""
    logger = setup_logging()
    
    try:
        db_manager = DatabaseManager("test_schema.db")
        
        # 测试插入仓位记录
        position_info = {
            'position_id': 'test_pos_001',
            'signal_id': 'test_signal_001',
            'coin_symbol': 'ETHUSDT',
            'signal_type': 1,
            'probability': 0.85,
            'up_target': 3060.0,
            'down_target': 2940.0,
            'expire_time': datetime.now(timezone.utc),
            'status': 'pending_open',
            'metadata': {'test': True}
        }
        
        pos_id = db_manager.insert_position(position_info, 'test_signal_001')
        logger.info(f"✅ 插入仓位记录成功: {pos_id}")
        
        # 测试更新仓位状态
        update_data = {
            'status': 'active',
            'entry_price': 3000.0,
            'entry_time': datetime.now(timezone.utc),
            'entry_order_id': 'test_order_001'
        }
        
        db_manager.update_position(pos_id, update_data)
        logger.info(f"✅ 更新仓位状态成功")
        
        # 测试获取活跃仓位
        active_positions = db_manager.get_active_positions('ETHUSDT')
        logger.info(f"✅ 获取活跃仓位成功: {len(active_positions)} 个")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据库测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("Position-First逻辑测试")
    print("=" * 60)
    
    # 测试1: 数据库Schema
    print("\n1. 测试数据库Schema更新...")
    if not test_database_schema():
        print("❌ 数据库Schema测试失败")
        return
    
    # 测试2: Position-First逻辑
    print("\n2. 测试Position-First逻辑...")
    if not test_position_first_logic():
        print("❌ Position-First逻辑测试失败")
        return
    
    print("\n" + "=" * 60)
    print("✅ 所有测试通过！Position-First逻辑就绪")
    print("=" * 60)
    
    print("\n📋 新功能摘要:")
    print("   - 仓位优先创建 (pending_open → active/open_failed)")
    print("   - 详细状态跟踪 (pending_open, active, open_failed, closing, completed)")
    print("   - 订单ID关联 (entry_order_id, exit_order_id)")
    print("   - 自动平仓重试机制")
    print("   - 灵活的状态更新系统")

if __name__ == "__main__":
    main() 