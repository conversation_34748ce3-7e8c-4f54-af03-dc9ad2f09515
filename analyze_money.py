# analyze_backtest.py
# 最终修正版 v2 - 修复了 'RunningMaxMoney' 的 KeyError

import pandas as pd
import numpy as np
import argparse
import os
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.ticker import FuncFormatter
from matplotlib import rcParams

# 尝试导入plotly
try:
    import plotly.graph_objects as go
    from plotly.subplots import make_subplots
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False
    print("⚠️ Plotly未安装，将跳过交互式图表生成。")

def analyze_and_plot_results(results_df, output_prefix):
    """
    主分析函数：
    1. 自动判断日志类型（资金模式或分数模式）。
    2. 使用行业标准算法，独立计算最大金额回撤和最大百分比回撤。
    3. 生成包含专业风险图表的分析报告。
    """
    print("\n=== 开始分析回测结果 (采用行业标准MDD算法) ===")

    # 设置中文字体
    try:
        rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'DejaVu Sans']
        rcParams['axes.unicode_minus'] = False
    except Exception as e:
        print(f"设置中文字体失败: {e}")

    # --- 1. 数据预处理 ---
    results_df['EndTime'] = pd.to_datetime(results_df['EndTimestamp'].str.replace(' UTC+8', '', regex=False))
    results_df['StartTime'] = pd.to_datetime(results_df['StartTimestamp'].str.replace(' UTC+8', '', regex=False))
    results_df = results_df.sort_values('EndTime').reset_index(drop=True)

    # --- 2. 模式判断与核心指标计算 ---
    is_money_mode = 'CapitalAfter' in results_df.columns and 'ProfitLoss' in results_df.columns

    if is_money_mode and not results_df.empty:
        print("检测到资金管理模式日志，将基于真实资金进行分析。")
        initial_capital = results_df.iloc[0]['CapitalAfter'] - results_df.iloc[0]['ProfitLoss']
        
        # 【关键修正】将列名从 'RunningMaxCapital' 改为 'RunningMaxMoney'
        # 以匹配后续绘图代码 f-string 的逻辑
        results_df['RunningMaxMoney'] = results_df['CapitalAfter'].cummax()

        results_df['DrawdownAbs'] = results_df['RunningMaxMoney'] - results_df['CapitalAfter']
        results_df['DrawdownPct'] = (results_df['DrawdownAbs']) / (results_df['RunningMaxMoney'] + 1e-9)
        
        max_drawdown_abs = results_df['DrawdownAbs'].max()
        max_drawdown_pct = results_df['DrawdownPct'].max() * 100

        metrics = {
            "mode": "money", "y_label": "账户资金 ($)", "y_unit": "$",
            "dist_label": "单笔盈亏 ($)", "curve_data": results_df['CapitalAfter'],
            "dist_data": results_df['ProfitLoss'], "initial_value": initial_capital,
            "total_return": results_df.iloc[-1]['CapitalAfter'] - initial_capital,
            "max_drawdown_abs": max_drawdown_abs, "max_drawdown_pct": max_drawdown_pct,
            "drawdown_abs_series": -results_df['DrawdownAbs'],
            "drawdown_pct_series": -results_df['DrawdownPct'] * 100
        }
    elif not results_df.empty:
        print("未检测到资金信息，将基于Score进行分析。")
        results_df['CumulativeScore'] = results_df['Score'].cumsum()
        results_df['RunningMaxScore'] = results_df['CumulativeScore'].cummax() # 这里是正确的
        results_df['DrawdownAbs'] = results_df['RunningMaxScore'] - results_df['CumulativeScore']
        max_drawdown_abs = results_df['DrawdownAbs'].max()

        metrics = {
            "mode": "score", "y_label": "累计得分", "y_unit": "分",
            "dist_label": "单笔得分", "curve_data": results_df['CumulativeScore'],
            "dist_data": results_df['Score'], "initial_value": 0,
            "total_return": results_df['Score'].sum(),
            "max_drawdown_abs": max_drawdown_abs, "max_drawdown_pct": None,
            "drawdown_abs_series": -results_df['DrawdownAbs'],
        }
    else:
        print("错误：回测日志为空，无法进行分析。")
        return

    # --- 3. 打印统计报告 ---
    total_trades = len(results_df)
    winning_trades = len(results_df[results_df['Score'] > 0])
    win_rate = winning_trades / total_trades * 100 if total_trades > 0 else 0
    
    print("\n--- 核心性能指标 ---")
    print(f"总交易次数: {total_trades}")
    print(f"胜率: {win_rate:.2f}% ({winning_trades}/{total_trades})")
    if is_money_mode:
        print(f"初始资金: ${metrics['initial_value']:,.2f}")
        print(f"最终资金: ${metrics['curve_data'].iloc[-1]:,.2f}")
        print(f"总收益: ${metrics['total_return']:+.2f} ({metrics['total_return']/metrics['initial_value']*100:+.2f}%)")
        print(f"最大金额回撤: ${metrics['max_drawdown_abs']:,.2f}")
        print(f"最大百分比回撤 (MDD%): {metrics['max_drawdown_pct']:.2f}%")
    else:
        print(f"总得分: {metrics['total_return']:+.2f}分")
        print(f"最大回撤: {metrics['max_drawdown_abs']:.2f}分")
    
    # --- 4. 绘制静态图表 (Matplotlib) ---
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(18, 14), gridspec_kw={'height_ratios': [2, 1]})
    fig.suptitle(f'回测分析: {os.path.basename(output_prefix)} ({metrics["mode"].title()} Mode)', fontsize=16, fontweight='bold')

    # 图1: 资金/得分曲线 - 这里的动态f-string现在可以正常工作了
    ax1.plot(results_df['EndTime'], metrics['curve_data'], 'b-', linewidth=2, label='策略表现')
    ax1.plot(results_df['EndTime'], results_df[f'RunningMax{metrics["mode"].title()}'], 'g--', linewidth=1, alpha=0.7, label='历史高点')
    ax1.set_title('策略表现曲线与历史高点')
    ax1.set_ylabel(metrics['y_label'])
    ax1.grid(True, alpha=0.3); ax1.legend()
    if is_money_mode: ax1.yaxis.set_major_formatter(FuncFormatter(lambda y, _: f'${y:,.0f}'))

    # 图2: 金额回撤曲线
    ax2.plot(results_df['EndTime'], metrics['drawdown_abs_series'], color='red')
    ax2.fill_between(results_df['EndTime'], metrics['drawdown_abs_series'], 0, color='red', alpha=0.3)
    ax2.set_title('金额回撤 (Absolute Drawdown)')
    ax2.set_ylabel(f"回撤 ({metrics['y_unit']})")
    ax2.grid(True, alpha=0.3)
    if is_money_mode: ax2.yaxis.set_major_formatter(FuncFormatter(lambda y, _: f'${-y:,.0f}'))
    
    # 图3: 盈亏/得分分布
    ax3.hist(metrics['dist_data'], bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    ax3.axvline(x=0, color='r', linestyle='--', label='盈亏平衡')
    avg_return = metrics['dist_data'].mean()
    ax3.axvline(x=avg_return, color='orange', linestyle='--', label=f'平均: {avg_return:+.3f}')
    ax3.set_title('单笔交易表现分布'); ax3.set_xlabel(metrics['dist_label'])
    ax3.set_ylabel('交易次数'); ax3.legend(); ax3.grid(True, alpha=0.3)

    # 图4: 百分比回撤曲线 (或后备的滚动胜率)
    if is_money_mode:
        ax4.plot(results_df['EndTime'], metrics['drawdown_pct_series'], color='purple')
        ax4.fill_between(results_df['EndTime'], metrics['drawdown_pct_series'], 0, color='purple', alpha=0.3)
        ax4.set_title('百分比回撤曲线 (Percentage Drawdown)')
        ax4.set_ylabel('回撤 (%)')
        ax4.yaxis.set_major_formatter(FuncFormatter(lambda y, _: f'{y:.1f}%'))
        ax4.grid(True, alpha=0.3)
    else:
        window_size = min(100, total_trades) if total_trades > 1 else 1
        rolling_win_rate = results_df['Score'].apply(lambda x: 1 if x > 0 else 0).rolling(window=window_size).mean() * 100
        ax4.plot(results_df['EndTime'], rolling_win_rate, 'purple', label=f'滚动胜率 (窗口={window_size})')
        ax4.set_title('滚动胜率'); ax4.set_ylabel('胜率 (%)'); ax4.set_ylim(0, 100)
        ax4.grid(True, alpha=0.3); ax4.legend()

    for ax in [ax1, ax2, ax3, ax4]:
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
        plt.setp(ax.get_xticklabels(), rotation=45, ha='right')

    plt.tight_layout(rect=[0, 0, 1, 0.96])
    chart_file = f"{output_prefix}_analysis.png"
    plt.savefig(chart_file, dpi=300)
    print(f"\n📊 静态分析图表已保存到: {chart_file}")
    plt.close()

    # --- 5. 生成交互式图表 (Plotly) ---
    if PLOTLY_AVAILABLE and not results_df.empty:
        # (这部分代码无需修改)
        pass

def main():
    """主函数，用于从命令行调用"""
    parser = argparse.ArgumentParser(description="分析回测CSV文件，生成包含正确MDD计算的统计报告和图表。")
    parser.add_argument("csv_file", help="回测日志CSV文件路径")
    parser.add_argument("--output-prefix", help="输出文件前缀（可选，默认使用CSV文件名）")
    args = parser.parse_args()
    
    if not os.path.exists(args.csv_file):
        print(f"错误: 文件 '{args.csv_file}' 不存在")
        return

    try:
        results_df = pd.read_csv(args.csv_file)
        print(f"成功读取 {len(results_df)} 条交易记录。")
    except Exception as e:
        print(f"读取CSV文件时出错: {e}")
        return

    output_prefix = args.output_prefix or args.csv_file.rsplit('.', 1)[0]
    
    analyze_and_plot_results(results_df, output_prefix)

if __name__ == '__main__':
    main()