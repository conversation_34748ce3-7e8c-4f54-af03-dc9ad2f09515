#!/usr/bin/env python3
"""
使用示例：展示如何使用新的数据加载功能
"""

import subprocess
import sys
import pandas as pd
from datetime import datetime, timedelta
import numpy as np
from backtest import CSVDataProvider, SQLiteDataProvider, RealTimeDataProvider, HistoricalBacktester, run_backtest_with_provider

def run_command(cmd):
    """运行命令并打印输出"""
    print(f"🚀 执行命令: {cmd}")
    print("-" * 50)
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    print(result.stdout)
    if result.stderr:
        print("错误:", result.stderr)
    print("-" * 50)
    return result.returncode == 0

def demo_csv_backtest():
    """演示使用CSV文件进行回测"""
    print("\n1️⃣ 使用CSV文件训练模型:")
    success = run_command("python train.py --coin DOT --data-source csv")
    return success

def demo_sqlite_backtest():
    """演示使用SQLite数据库进行回测"""
    print("\n2️⃣ 使用SQLite数据库训练模型:")
    success = run_command("python train.py --coin DOT --data-source sqlite --db-path coin_data.db --symbol DOTUSDT --interval 15m --market spot")
    return success

def demo_sqlite_validation():
    """演示使用SQLite数据验证模型"""
    print("\n3️⃣ 验证模型（使用SQLite数据）:")
    success = run_command("python train.py --coin DOT --mode validate --data-source sqlite --db-path coin_data.db --symbol DOTUSDT --interval 15m --market spot")
    return success

def demo_backtest_with_sqlite():
    """演示使用SQLite进行回测"""
    print("\n4️⃣ 使用SQLite进行回测:")
    success = run_command("python backtest.py --coin DOT --data-source sqlite --db-path coin_data.db --symbol DOTUSDT --interval 15m --market spot")
    return success

def demo_realtime_simulation():
    """演示实时数据模拟"""
    print("\n5️⃣ 实时数据模拟演示:")
    
    # 创建模拟的历史数据
    dates = pd.date_range('2024-01-01', periods=100, freq='15min')
    np.random.seed(42)
    
    # 生成模拟的OHLCV数据
    base_price = 100.0
    prices = []
    for i in range(len(dates)):
        if i == 0:
            price = base_price
        else:
            # 模拟价格变化
            change = np.random.normal(0, 0.02)  # 2%的标准差
            price = prices[-1] * (1 + change)
        
        # 生成OHLCV
        open_price = price
        high = price * (1 + abs(np.random.normal(0, 0.01)))
        low = price * (1 - abs(np.random.normal(0, 0.01)))
        close = price * (1 + np.random.normal(0, 0.005))
        volume = np.random.uniform(1000, 10000)
        
        prices.append({
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
    
    # 创建初始DataFrame
    initial_df = pd.DataFrame(prices, index=dates)
    
    # 创建实时数据提供者
    realtime_provider = RealTimeDataProvider(initial_df)
    
    print(f"✅ 创建了 {len(initial_df)} 条模拟历史数据")
    print(f"📅 时间范围: {initial_df.index[0]} 到 {initial_df.index[-1]}")
    
    # 模拟添加新的实时数据
    print("\n📊 模拟实时数据流...")
    current_time = initial_df.index[-1]
    base_price = initial_df.iloc[-1]['close']
    
    for i in range(10):  # 模拟10条新数据
        # 模拟新的K线数据
        change = np.random.normal(0, 0.02)
        new_price = base_price * (1 + change)
        
        open_price = new_price
        high = new_price * (1 + abs(np.random.normal(0, 0.01)))
        low = new_price * (1 - abs(np.random.normal(0, 0.01)))
        close = new_price * (1 + np.random.normal(0, 0.005))
        volume = np.random.uniform(1000, 10000)
        
        # 添加新数据
        current_time += timedelta(minutes=15)
        realtime_provider.add_new_data(
            timestamp=current_time,
            open_price=open_price,
            high=high,
            low=low,
            close=close,
            volume=volume
        )
        
        # 更新基础价格
        base_price = close
        
        print(f"  📈 添加新数据: {current_time.strftime('%Y-%m-%d %H:%M')} - 价格: {close:.4f}")
    
    print(f"✅ 成功添加了 10 条新的实时数据")
    print(f"📊 总数据量: {realtime_provider.get_total_count()} 条")
    
    return True

def demo_data_provider_interface():
    """演示数据提供者接口的使用"""
    print("\n6️⃣ 数据提供者接口演示:")
    
    # 创建模拟数据
    dates = pd.date_range('2024-01-01', periods=50, freq='15min')
    np.random.seed(42)
    
    prices = []
    base_price = 100.0
    for i in range(len(dates)):
        if i == 0:
            price = base_price
        else:
            change = np.random.normal(0, 0.02)
            price = prices[-1] * (1 + change)
        
        prices.append({
            'open': price,
            'high': price * 1.01,
            'low': price * 0.99,
            'close': price * (1 + np.random.normal(0, 0.005)),
            'volume': np.random.uniform(1000, 10000)
        })
    
    df = pd.DataFrame(prices, index=dates)
    
    # 演示CSV数据提供者
    print("📊 CSV数据提供者演示:")
    csv_provider = CSVDataProvider("example_data.csv", price_multiplier=1.0)
    # 注意：这里只是演示接口，实际需要先保存CSV文件
    
    # 演示SQLite数据提供者
    print("📊 SQLite数据提供者演示:")
    sqlite_provider = SQLiteDataProvider(
        db_path="coin_data.db",
        symbol="DOTUSDT",
        interval="15m",
        market="spot",
        price_multiplier=1.0
    )
    
    # 演示实时数据提供者
    print("📊 实时数据提供者演示:")
    realtime_provider = RealTimeDataProvider(df)
    
    # 演示数据流处理
    print("\n🔄 数据流处理演示:")
    realtime_provider.reset()
    
    count = 0
    while count < 10:  # 只处理前10条数据
        data_point = realtime_provider.get_next_data()
        if data_point is None:
            break
        
        timestamp, price = data_point
        print(f"  📈 数据点 {count+1}: {timestamp.strftime('%Y-%m-%d %H:%M')} - 价格: {price:.4f}")
        count += 1
    
    print(f"✅ 成功处理了 {count} 条数据")
    
    return True

def main():
    print("📚 数据加载功能使用示例")
    print("=" * 60)
    
    # 1. CSV文件回测
    demo_csv_backtest()
    
    # 2. SQLite数据库回测
    demo_sqlite_backtest()
    
    # 3. 模型验证
    demo_sqlite_validation()
    
    # 4. 回测演示
    demo_backtest_with_sqlite()
    
    # 5. 实时数据模拟
    demo_realtime_simulation()
    
    # 6. 数据提供者接口演示
    demo_data_provider_interface()
    
    print("\n✅ 示例完成！")
    print("\n💡 其他有用的命令:")
    print("   # 查看数据库中的表")
    print("   python get_coin_history.py --list-tables --db coin_data.db")
    print("   # 查看表统计信息")
    print("   python get_coin_history.py --table-stats DOTUSDT_15min_spot --db coin_data.db")
    print("   # 使用指定时间范围回测")
    print("   python backtest.py --coin DOT --data-source sqlite --start-time '2024-01-15'")
    print("   # 使用止损功能")
    print("   python backtest.py --coin DOT --data-source sqlite --stop-loss 2.5")

if __name__ == "__main__":
    main() 