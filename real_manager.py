# real_manager.py

import pandas as pd
import logging
from datetime import datetime, timezone
from typing import Dict
import platform
import os
import json
import pytz

class PortfolioManager:
    """
    资金管理模块
    - Manages active positions based on signals.
    - Checks positions against the latest price to determine outcomes.
    - Logs performance and statistics.
    """
    def __init__(self, config: Dict, logger: logging.Logger, display_name: str,
                 local_tz: pytz.BaseTzInfo, enable_sound: bool = True):
        self.config = config
        self.logger = logger
        self.display_name = display_name
        self.local_tz = local_tz
        self.enable_sound = enable_sound
        self.active_positions: Dict[str, dict] = {}
        self.total_predictions = 0
        self.successful_predictions = 0
        self.failed_predictions = 0
        self.timeout_predictions = 0

    def log_message(self, event_type: str, message: str, data: dict = None, level: str = 'info'):
        """Helper function for consistent log formatting."""
        log_entry = f"[{event_type}] {message}"
        if data:
            log_entry += f" | 数据: {json.dumps({k: (f'{v:.4f}' if isinstance(v, float) else v) for k, v in data.items()})}"
        getattr(self.logger, level, self.logger.info)(log_entry)

    def play_alert_sound(self, message):
        """Plays an alert sound for new signals."""
        if not self.enable_sound: return
        try:
            system = platform.system().lower()
            if system == "darwin": os.system(f'say "{message}"')
            elif system == "windows": import winsound; winsound.MessageBeep(winsound.MB_ICONEXCLAMATION)
            else: print('\a' * 3)
        except Exception as e:
            print('\a' * 3)
            self.logger.warning(f"播放报警声失败: {e}")

    def on_new_signal(self, signal: Dict):
        """Callback to handle a new signal from the SignalGenerator."""
        timestamp = signal['timestamp']
        prediction_id = f"pred_{timestamp.strftime('%Y%m%d_%H%M%S')}"
        max_wait = pd.Timedelta(minutes=self.config['max_lookforward_minutes'])
        
        position = {
            'id': prediction_id, 'guess': signal['guess'], 'probability': signal['probability'],
            'entry_price': signal['price'], 'entry_time': timestamp,
            'expire_time': timestamp + max_wait,
            'up_target': signal['price'] * (1 + self.config['up_threshold']),
            'down_target': signal['price'] * (1 - self.config['down_threshold']), 'status': 'active'
        }
        self.active_positions[prediction_id] = position
        self.total_predictions += 1
        
        direction = f"先涨{self.config['up_threshold']*100:.1f}%" if signal['guess'] == 1 else f"先跌{self.config['down_threshold']*100:.1f}%"
        local_ts_str = timestamp.astimezone(self.local_tz).strftime('%Y-%m-%d %H:%M:%S %z')
        
        msg = (f"K线: {local_ts_str}, 方向: {direction}, 价格: {signal['price']:.4f}, "
               f"信心: {signal['probability']:.4f}, 目标: [涨: {position['up_target']:.4f}, 跌: {position['down_target']:.4f}]")
        self.log_message("新预测", msg)
        
        self.play_alert_sound(f"发现交易信号: {self.display_name} {direction}，当前价格 {signal['price']:.4f}")

    def on_price_update(self, current_price: float):
        """Callback to check active positions against the new price."""
        completed_ids = []
        now = datetime.now(timezone.utc)
        for pos_id, pos in self.active_positions.items():
            if pos['status'] != 'active': continue
            if current_price >= pos['up_target']:
                result, reason = (1, "达到上涨目标") if pos['guess'] == 1 else (0, "达到上涨目标 (预测错误)")
                self._complete_position(pos_id, result, current_price, now, reason)
                completed_ids.append(pos_id)
            elif current_price <= pos['down_target']:
                result, reason = (1, "达到下跌目标") if pos['guess'] == 0 else (0, "达到下跌目标 (预测错误)")
                self._complete_position(pos_id, result, current_price, now, reason)
                completed_ids.append(pos_id)
            elif now >= pos['expire_time']:
                self._complete_position(pos_id, -1, current_price, now, "超时")
                completed_ids.append(pos_id)
        
        for pos_id in completed_ids:
            if pos_id in self.active_positions: del self.active_positions[pos_id]

    def _complete_position(self, pos_id: str, result: int, final_price: float, end_time: datetime, reason: str):
        pos = self.active_positions.get(pos_id)
        if not pos: return
        pos['status'] = 'completed'
        
        status_map = {1: "成功✅", 0: "失败❌", -1: "超时⏰"}
        if result == 1: self.successful_predictions += 1
        elif result == 0: self.failed_predictions += 1
        else: self.timeout_predictions += 1
            
        direction = f"先涨{self.config['up_threshold']*100:.1f}%" if pos['guess'] == 1 else f"先跌{self.config['down_threshold']*100:.1f}%"
        self.log_message("预测完成", f"ID: {pos_id}, 结果: {status_map.get(result)}", {
            'direction': direction, 'confidence': pos['probability'], 'start_price': pos['entry_price'],
            'end_price': final_price, 'duration_min': (end_time - pos['entry_time']).total_seconds() / 60, 'reason': reason
        })

    def print_status(self):
        """Prints a summary of the performance."""
        active_count = len(self.active_positions)
        valid_trades = self.successful_predictions + self.failed_predictions
        success_rate = (self.successful_predictions / max(1, valid_trades)) * 100
        self.logger.info("\n--- 状态摘要 ---\n"
                        f"  活跃预测: {active_count}, 总预测数: {self.total_predictions}\n"
                        f"  成功: {self.successful_predictions}, 失败: {self.failed_predictions}, 超时: {self.timeout_predictions}\n"
                        f"  胜率 (非超时): {success_rate:.2f}%\n"
                        "------------------")