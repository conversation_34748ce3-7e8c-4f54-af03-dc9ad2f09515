#!/usr/bin/env python3
"""
测试增强特征工程功能

验证多时间尺度特征和市场状态特征的计算
"""

import pandas as pd
import numpy as np
import sqlite3
from model_utils import load_daily_data, calculate_market_regime_features, calculate_features
from data_loader import load_data_for_training

def test_daily_data_loading():
    """测试日线数据加载"""
    print("=== 测试日线数据加载 ===")
    
    daily_df = load_daily_data('coin_data.db', 'ETHUSDT', 'spot')
    
    if daily_df is not None:
        print(f"✅ 成功加载日线数据: {len(daily_df)} 条记录")
        print(f"📅 时间范围: {daily_df.index[0]} 到 {daily_df.index[-1]}")
        print(f"📊 数据列: {list(daily_df.columns)}")
        print("\n最近5天数据:")
        print(daily_df.tail().round(2))
        return True
    else:
        print("❌ 日线数据加载失败")
        return False

def test_market_regime_features():
    """测试市场状态特征计算"""
    print("\n=== 测试市场状态特征计算 ===")
    
    daily_df = load_daily_data('coin_data.db', 'ETHUSDT', 'spot')
    
    if daily_df is None:
        print("❌ 无法加载日线数据，跳过市场状态特征测试")
        return False
    
    regime_features = calculate_market_regime_features(daily_df)
    
    if regime_features is not None and len(regime_features) > 0:
        print(f"✅ 成功计算市场状态特征: {len(regime_features.columns)} 个特征")
        print(f"📊 特征列表:")
        for i, col in enumerate(regime_features.columns):
            print(f"  {i+1:2d}. {col}")
        
        print(f"\n📈 最近5天的市场状态特征:")
        print(regime_features.tail().round(4))
        return True
    else:
        print("❌ 市场状态特征计算失败")
        return False

def test_enhanced_features():
    """测试增强特征计算"""
    print("\n=== 测试增强特征计算 ===")
    
    # 加载15分钟数据
    df = load_data_for_training('ETH', 'coin_data.db', 'ETHUSDT', '15m', 'spot', 
                               start_time='2024-12-01', end_time='2024-12-31')
    
    if df is None:
        print("❌ 无法加载15分钟数据")
        return False
    
    print(f"📊 原始数据: {len(df)} 条记录")
    
    # 计算增强特征
    df_features = calculate_features(df, timeframe=15, db_path='coin_data.db', 
                                   symbol='ETHUSDT', market='spot')
    
    if df_features is not None and len(df_features) > 0:
        print(f"✅ 成功计算增强特征: {len(df_features.columns)} 个特征")
        
        # 按特征类型分组统计
        feature_groups = {
            '时间特征': [f for f in df_features.columns if any(x in f for x in ['hour_', 'day_', 'month_'])],
            'K线形态特征': [f for f in df_features.columns if any(x in f for x in ['body_', 'range_', 'shadow_'])],
            '动量特征': [f for f in df_features.columns if any(x in f for x in ['return_', 'momentum_', 'acceleration_'])],
            '趋势特征': [f for f in df_features.columns if any(x in f for x in ['price_div_sma', 'ma_alignment'])],
            '波动率特征': [f for f in df_features.columns if any(x in f for x in ['volatility_', 'vol_', 'atr_'])],
            '震荡器特征': [f for f in df_features.columns if any(x in f for x in ['rsi_', 'bb_'])],
            '成交量特征': [f for f in df_features.columns if any(x in f for x in ['volume_', 'vwap'])],
            '市场状态特征': [f for f in df_features.columns if f.startswith('daily_')],
            '微观结构特征': [f for f in df_features.columns if any(x in f for x in ['jump', 'trend'])],
        }
        
        print("\n📊 特征分组统计:")
        total_classified = 0
        for group_name, group_features in feature_groups.items():
            if group_features:
                print(f"  {group_name}: {len(group_features)}个")
                total_classified += len(group_features)
        
        other_features = len(df_features.columns) - total_classified
        if other_features > 0:
            print(f"  其他特征: {other_features}个")
        
        # 显示一些示例特征值
        print(f"\n📈 最近5条记录的部分特征:")
        sample_features = []
        for group_features in feature_groups.values():
            if group_features:
                sample_features.extend(group_features[:2])  # 每组取2个特征
        
        if sample_features:
            print(df_features[sample_features[:10]].tail().round(4))
        
        return True
    else:
        print("❌ 增强特征计算失败")
        return False

def test_feature_robustness():
    """测试特征的鲁棒性"""
    print("\n=== 测试特征鲁棒性 ===")
    
    # 测试不同时间段的特征稳定性
    time_periods = [
        ('2024-11-01', '2024-11-30'),
        ('2024-12-01', '2024-12-31'),
    ]
    
    feature_stats = {}
    
    for start_time, end_time in time_periods:
        print(f"\n📅 测试时间段: {start_time} 到 {end_time}")
        
        df = load_data_for_training('ETH', 'coin_data.db', 'ETHUSDT', '15m', 'spot', 
                                   start_time=start_time, end_time=end_time)
        
        if df is None or len(df) < 100:
            print(f"⚠️  数据不足，跳过时间段 {start_time} - {end_time}")
            continue
        
        df_features = calculate_features(df, timeframe=15, db_path='coin_data.db', 
                                       symbol='ETHUSDT', market='spot')
        
        if df_features is not None and len(df_features) > 0:
            # 计算特征统计
            period_stats = {}
            for col in df_features.select_dtypes(include=[np.number]).columns:
                if col not in ['open', 'high', 'low', 'close', 'volume']:
                    period_stats[col] = {
                        'mean': df_features[col].mean(),
                        'std': df_features[col].std(),
                        'min': df_features[col].min(),
                        'max': df_features[col].max()
                    }
            
            feature_stats[f"{start_time}_{end_time}"] = period_stats
            print(f"✅ 计算了 {len(period_stats)} 个特征的统计信息")
    
    # 比较不同时间段的特征稳定性
    if len(feature_stats) >= 2:
        print(f"\n📊 特征稳定性分析:")
        periods = list(feature_stats.keys())
        common_features = set(feature_stats[periods[0]].keys()) & set(feature_stats[periods[1]].keys())
        
        unstable_features = []
        for feature in common_features:
            mean1 = feature_stats[periods[0]][feature]['mean']
            mean2 = feature_stats[periods[1]][feature]['mean']
            
            if abs(mean1) > 1e-6 and abs(mean2) > 1e-6:  # 避免除零
                relative_change = abs(mean1 - mean2) / max(abs(mean1), abs(mean2))
                if relative_change > 0.5:  # 变化超过50%
                    unstable_features.append((feature, relative_change))
        
        if unstable_features:
            print(f"⚠️  发现 {len(unstable_features)} 个可能不稳定的特征:")
            for feature, change in sorted(unstable_features, key=lambda x: x[1], reverse=True)[:5]:
                print(f"  - {feature}: 变化 {change:.2%}")
        else:
            print("✅ 所有特征在不同时间段表现稳定")
    
    return True

def main():
    """主函数"""
    print("🧪 增强特征工程功能测试\n")
    
    tests = [
        ("日线数据加载", test_daily_data_loading),
        ("市场状态特征计算", test_market_regime_features),
        ("增强特征计算", test_enhanced_features),
        ("特征鲁棒性", test_feature_robustness),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*60}")
            print(f"🔬 {test_name}")
            print('='*60)
            
            success = test_func()
            results.append((test_name, success))
            
            if success:
                print(f"✅ {test_name} - 通过")
            else:
                print(f"❌ {test_name} - 失败")
                
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n{'='*60}")
    print("📋 测试总结")
    print('='*60)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！增强特征工程功能正常。")
    else:
        print("⚠️  部分测试失败，请检查相关功能。")

if __name__ == "__main__":
    main()
