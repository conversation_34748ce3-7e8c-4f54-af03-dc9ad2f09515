#!/usr/bin/env python3
"""
数据库集成测试脚本
测试交易系统的数据库功能
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timezone, timedelta
import tempfile
import logging

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database_manager import DatabaseManager
from order_manager import OrderManager, OrderType, OrderSide

def setup_test_logging():
    """设置测试日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger('DatabaseTest')

def create_test_market_data():
    """创建测试行情数据"""
    dates = pd.date_range(
        start=datetime.now(timezone.utc) - timedelta(days=7),
        end=datetime.now(timezone.utc),
        freq='15min'
    )
    
    # 生成模拟价格数据
    np.random.seed(42)
    base_price = 7.0
    price_changes = np.random.normal(0, 0.01, len(dates))
    prices = [base_price]
    
    for change in price_changes[1:]:
        new_price = prices[-1] * (1 + change)
        prices.append(max(new_price, 0.1))  # 确保价格为正
    
    df = pd.DataFrame({
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.005))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.005))) for p in prices],
        'close': prices,
        'volume': np.random.uniform(1000, 10000, len(dates))
    }, index=dates)
    
    # 确保high >= max(open, close) 和 low <= min(open, close)
    df['high'] = np.maximum(df['high'], np.maximum(df['open'], df['close']))
    df['low'] = np.minimum(df['low'], np.minimum(df['open'], df['close']))
    
    return df

def test_database_manager():
    """测试数据库管理器"""
    logger = setup_test_logging()
    logger.info("开始测试数据库管理器...")
    
    # 创建临时数据库文件
    with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_file:
        db_path = tmp_file.name
    
    try:
        # 初始化数据库管理器
        db_manager = DatabaseManager(db_path)
        logger.info(f"✅ 数据库初始化成功: {db_path}")
        
        # 测试行情数据插入
        logger.info("测试行情数据插入...")
        test_data = create_test_market_data()
        db_manager.insert_market_data("DOTUSDT", "15m", test_data)
        logger.info(f"✅ 插入 {len(test_data)} 条行情数据")
        
        # 测试行情数据查询
        logger.info("测试行情数据查询...")
        retrieved_data = db_manager.get_market_data("DOTUSDT", "15m", limit=100)
        logger.info(f"✅ 查询到 {len(retrieved_data)} 条行情数据")
        
        # 测试交易信号插入
        logger.info("测试交易信号插入...")
        signal_info = {
            'coin_symbol': 'DOTUSDT',
            'signal_type': 1,
            'probability': 0.75,
            'price': 7.25,
            'up_target': 7.40,
            'down_target': 7.10,
            'max_hold_minutes': 60,
            'timestamp': datetime.now(timezone.utc),
            'kline_timestamp': datetime.now(timezone.utc)
        }
        signal_id = db_manager.insert_trading_signal(signal_info)
        logger.info(f"✅ 插入交易信号: {signal_id}")
        
        # 测试仓位插入
        logger.info("测试仓位插入...")
        position_info = {
            'position_id': 'test_pos_001',
            'coin_symbol': 'DOTUSDT',
            'signal_type': 1,
            'probability': 0.75,
            'entry_price': 7.25,
            'entry_time': datetime.now(timezone.utc),
            'up_target': 7.40,
            'down_target': 7.10,
            'expire_time': datetime.now(timezone.utc) + timedelta(hours=1)
        }
        db_manager.insert_position(position_info, signal_id)
        logger.info("✅ 插入仓位记录")
        
        # 测试仓位更新
        logger.info("测试仓位更新...")
        update_info = {
            'status': 'completed',
            'exit_price': 7.35,
            'exit_time': datetime.now(timezone.utc),
            'result': 1,
            'pnl': 0.0138,
            'exit_reason': '达到上涨目标'
        }
        db_manager.update_position('test_pos_001', update_info)
        logger.info("✅ 更新仓位状态")
        
        # 测试统计查询
        logger.info("测试统计查询...")
        stats = db_manager.get_trading_statistics('DOTUSDT', days=7)
        logger.info(f"✅ 获取统计信息: {stats}")
        
        # 测试活跃仓位查询
        active_positions = db_manager.get_active_positions('DOTUSDT')
        logger.info(f"✅ 活跃仓位数量: {len(active_positions)}")
        
        # 测试系统状态记录
        db_manager.log_system_status('DOTUSDT', 'info', '测试系统状态记录')
        logger.info("✅ 记录系统状态")
        
        logger.info("🎉 数据库管理器测试完成！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据库管理器测试失败: {e}")
        return False
    finally:
        # 清理临时文件
        if os.path.exists(db_path):
            os.unlink(db_path)

def test_order_manager():
    """测试订单管理器"""
    logger = setup_test_logging()
    logger.info("开始测试订单管理器...")
    
    # 创建临时数据库文件
    with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_file:
        db_path = tmp_file.name
    
    try:
        # 初始化管理器
        db_manager = DatabaseManager(db_path)
        order_manager = OrderManager(db_manager)
        logger.info("✅ 订单管理器初始化成功")
        
        # 测试创建订单
        logger.info("测试创建订单...")
        order = order_manager.create_order(
            position_id="test_pos_001",
            coin_symbol="DOTUSDT",
            order_type=OrderType.MARKET,
            side=OrderSide.BUY,
            quantity=100.0
        )
        logger.info(f"✅ 创建订单: {order.order_id}")
        
        # 测试订单成交
        logger.info("测试订单成交...")
        success = order_manager.simulate_fill_order(order.order_id, 7.25, commission=0.001)
        logger.info(f"✅ 订单成交: {success}")
        
        # 测试创建限价订单
        limit_order = order_manager.create_order(
            position_id="test_pos_002",
            coin_symbol="DOTUSDT",
            order_type=OrderType.LIMIT,
            side=OrderSide.SELL,
            quantity=50.0,
            price=7.50
        )
        logger.info(f"✅ 创建限价订单: {limit_order.order_id}")
        
        # 测试取消订单
        success = order_manager.cancel_order(limit_order.order_id, "测试取消")
        logger.info(f"✅ 取消订单: {success}")
        
        # 测试获取订单统计
        stats = order_manager.get_order_statistics("DOTUSDT")
        logger.info(f"✅ 订单统计: {stats}")
        
        # 测试获取活跃订单
        active_orders = order_manager.get_active_orders("DOTUSDT")
        logger.info(f"✅ 活跃订单数量: {len(active_orders)}")
        
        logger.info("🎉 订单管理器测试完成！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 订单管理器测试失败: {e}")
        return False
    finally:
        # 清理临时文件
        if os.path.exists(db_path):
            os.unlink(db_path)

def test_integration():
    """集成测试"""
    logger = setup_test_logging()
    logger.info("开始集成测试...")
    
    # 创建临时数据库文件
    with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_file:
        db_path = tmp_file.name
    
    try:
        # 初始化所有管理器
        db_manager = DatabaseManager(db_path)
        order_manager = OrderManager(db_manager)
        
        # 模拟完整的交易流程
        logger.info("模拟完整交易流程...")
        
        # 1. 插入行情数据
        test_data = create_test_market_data()
        db_manager.insert_market_data("DOTUSDT", "15m", test_data)
        
        # 2. 生成交易信号
        signal_info = {
            'coin_symbol': 'DOTUSDT',
            'signal_type': 1,
            'probability': 0.80,
            'price': 7.20,
            'up_target': 7.35,
            'down_target': 7.05,
            'max_hold_minutes': 60,
            'timestamp': datetime.now(timezone.utc),
            'kline_timestamp': datetime.now(timezone.utc)
        }
        signal_id = db_manager.insert_trading_signal(signal_info)
        
        # 3. 创建仓位
        position_info = {
            'position_id': 'integration_pos_001',
            'coin_symbol': 'DOTUSDT',
            'signal_type': 1,
            'probability': 0.80,
            'entry_price': 7.20,
            'entry_time': datetime.now(timezone.utc),
            'up_target': 7.35,
            'down_target': 7.05,
            'expire_time': datetime.now(timezone.utc) + timedelta(hours=1)
        }
        db_manager.insert_position(position_info, signal_id)
        
        # 4. 创建订单
        order = order_manager.create_order(
            position_id="integration_pos_001",
            coin_symbol="DOTUSDT",
            order_type=OrderType.MARKET,
            side=OrderSide.BUY,
            quantity=100.0
        )
        
        # 5. 订单成交
        order_manager.simulate_fill_order(order.order_id, 7.20)
        
        # 6. 仓位平仓
        update_info = {
            'status': 'completed',
            'exit_price': 7.35,
            'exit_time': datetime.now(timezone.utc),
            'result': 1,
            'pnl': (7.35 - 7.20) / 7.20,
            'exit_reason': '达到上涨目标'
        }
        db_manager.update_position('integration_pos_001', update_info)
        
        # 7. 验证数据完整性
        stats = db_manager.get_trading_statistics('DOTUSDT')
        order_stats = order_manager.get_order_statistics('DOTUSDT')
        market_data = db_manager.get_market_data('DOTUSDT', '15m', limit=10)
        
        logger.info(f"✅ 交易统计: {stats}")
        logger.info(f"✅ 订单统计: {order_stats}")
        logger.info(f"✅ 行情数据条数: {len(market_data)}")
        
        # 验证数据一致性
        assert stats['total_positions'] >= 1, "仓位数据不一致"
        assert order_stats['total_orders'] >= 1, "订单数据不一致"
        assert len(market_data) > 0, "行情数据为空"
        
        logger.info("🎉 集成测试完成！所有功能正常工作")
        return True
        
    except Exception as e:
        logger.error(f"❌ 集成测试失败: {e}")
        return False
    finally:
        # 清理临时文件
        if os.path.exists(db_path):
            os.unlink(db_path)

def main():
    """主测试函数"""
    logger = setup_test_logging()
    logger.info("=" * 50)
    logger.info("开始数据库集成测试")
    logger.info("=" * 50)
    
    tests = [
        ("数据库管理器", test_database_manager),
        ("订单管理器", test_order_manager),
        ("集成测试", test_integration)
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n{'=' * 20} {test_name} {'=' * 20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"测试 {test_name} 发生异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    logger.info("\n" + "=" * 50)
    logger.info("测试结果汇总")
    logger.info("=" * 50)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        logger.info("\n🎉 所有测试通过！数据库集成功能正常")
    else:
        logger.error("\n❌ 部分测试失败，请检查错误信息")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)