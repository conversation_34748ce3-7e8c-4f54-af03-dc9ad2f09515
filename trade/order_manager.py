#!/usr/bin/env python3
"""
订单管理模块
为未来的实际交易功能提供订单管理基础设施
目前主要用于模拟和记录订单执行
"""

import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Tuple
from enum import Enum
import uuid
import os
from database_manager import DatabaseManager
from dotenv import load_dotenv
load_dotenv()
# 可选依赖：仅在开启实盘时需要
try:
    from binance.um_futures import UMFutures
    from binance.error import ClientError
except Exception:
    UMFutures = None  # type: ignore
    ClientError = Exception  # type: ignore

class OrderType(Enum):
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"

class OrderSide(Enum):
    BUY = "buy"
    SELL = "sell"

class OrderStatus(Enum):
    PENDING = "pending"
    FILLED = "filled"
    CANCELLED = "cancelled"
    FAILED = "failed"

class Order:
    """订单类"""
    
    def __init__(self, position_id: str, coin_symbol: str, order_type: OrderType, 
                 side: OrderSide, quantity: float, price: float = None):
        self.order_id = str(uuid.uuid4())
        self.position_id = position_id
        self.coin_symbol = coin_symbol
        self.order_type = order_type
        self.side = side
        self.quantity = quantity
        self.price = price
        self.status = OrderStatus.PENDING
        self.filled_quantity = 0.0
        self.filled_price = None
        self.commission = 0.0
        self.timestamp = datetime.now(timezone.utc)
        self.filled_time = None
        self.metadata = {}

class OrderManager:
    """订单管理器"""
    
    def __init__(self, logger: logging.Logger, db_manager: DatabaseManager = None, config: Dict = None):
        self.db_manager = db_manager or DatabaseManager()
        # self.logger = logging.getLogger('OrderManager')
        self.logger = logger
        self.active_orders: Dict[str, Order] = {}
        
        # 交易配置
        self.config = config or {}
        self.live_trading: bool = bool(self.config.get('live_trading', False))
        self.futures_leverage: int = int(self.config.get('futures_leverage', 20))
        self.binance_symbol_overrides: Dict[str, str] = self.config.get('binance_symbol_overrides', {}) or {}
        self.client: Optional[UMFutures] = None
        if self.live_trading:
            self._init_binance_client()

    def _init_binance_client(self):
        """初始化币安合约客户端（仅在实盘开启时）。"""
        self.logger.info("尝试开启实盘...")
        if UMFutures is None:
            self.logger.error("未安装 python-binance，无法开启实盘交易。请先安装并配置凭据。")
            self.live_trading = False
            return
        api_key = os.environ.get('BINANCE_APIKEY')
        private_key = None
        try:
            with open('rsa.txt', 'r') as f:
                private_key = f.read().strip()
        except FileNotFoundError:
            self.logger.error("rsa.txt 文件未找到。将关闭实盘模式。")
            self.live_trading = False
            return
        if not api_key or not private_key:
            self.logger.error("BINANCE_APIKEY 或 rsa.txt 缺失，无法开启实盘交易。")
            self.live_trading = False
            return
        try:
            self.client = UMFutures(key=api_key, private_key=private_key)
            self.client.ping()
            self.logger.info("成功连接到币安UM合约接口。")
        except Exception as e:
            self.logger.error(f"连接币安失败: {e}")
            self.live_trading = False

    def set_symbol_leverage(self, symbol: str, leverage: int):
        """设置币种杠杆（实盘）。"""
        if not self.live_trading or not self.client:
            return
        try:
            self.client.change_leverage(symbol=symbol, leverage=int(leverage))
            self.logger.info(f"设置 {symbol} 杠杆为 {leverage}x 成功")
        except ClientError as e:  # type: ignore
            self.logger.warning(f"设置杠杆失败: {getattr(e, 'error_message', str(e))}")
        except Exception as e:
            self.logger.warning(f"设置杠杆失败: {e}")

    def get_available_usdt(self) -> Optional[float]:
        """查询可用USDT余额（实盘）。"""
        if not self.live_trading or not self.client:
            return None
        try:
            balances = self.client.balance()
            for b in balances:
                if str(b.get('asset')).upper() == 'USDT':
                    return float(b.get('availableBalance', b.get('balance', 0)))
        except Exception as e:
            self.logger.warning(f"获取账户余额失败: {e}")
        return None

    def compute_order_quantity(self, symbol: str, price: float, risk_per_order_pct: float, leverage: int = None) -> float:
        """根据账户资金和价格计算下单数量（名义1%仓位，考虑杠杆）。"""
        leverage_to_use = int(leverage or self.futures_leverage)
        if self.live_trading:
            usdt = self.get_available_usdt()
            print(f"当前可用USDT: {usdt}")
            if usdt is None or usdt <= 0:
                usdt = 1000.0
        else:
            usdt = float(self.config.get('paper_usdt_balance', 1000.0))
        notional = usdt * float(risk_per_order_pct) * max(1, leverage_to_use) /100
        if price <= 0:
            return 0.0
        qty = notional / price
        print(f"计算下单数量: {qty} {symbol},ALL {notional} USDT,risk_per_order_pct {risk_per_order_pct},leverage {leverage_to_use}")
        return max(0.0, round(qty, 3))

    def create_order(self, position_id: str, coin_symbol: str, order_type: OrderType,
                    side: OrderSide, quantity: float, price: float = None) -> Order:
        """创建订单"""
        order = Order(position_id, coin_symbol, order_type, side, quantity, price)
        self.active_orders[order.order_id] = order
        
        # 保存到数据库
        try:
            self._save_order_to_db(order)
            self.logger.info(f"创建订单: {order.order_id} - {side.value} {quantity} {coin_symbol}")
        except Exception as e:
            self.logger.error(f"保存订单到数据库失败: {e}")
            
        return order
        
    def simulate_fill_order(self, order_id: str, fill_price: float, 
                           fill_quantity: float = None, commission: float = 0.001) -> bool:
        """模拟订单成交"""
        order = self.active_orders.get(order_id)
        if not order or order.status != OrderStatus.PENDING:
            return False
            
        fill_quantity = fill_quantity or order.quantity
        order.filled_quantity = fill_quantity
        order.filled_price = fill_price
        order.commission = fill_quantity * fill_price * commission
        order.status = OrderStatus.FILLED
        order.filled_time = datetime.now(timezone.utc)
        
        # 更新数据库
        try:
            self._update_order_in_db(order)
            self.logger.info(f"订单成交: {order_id} - 价格: {fill_price}, 数量: {fill_quantity}")
        except Exception as e:
            self.logger.error(f"更新订单状态到数据库失败: {e}")
            
        return True
        
    def cancel_order(self, order_id: str, reason: str = "用户取消") -> bool:
        """取消订单"""
        order = self.active_orders.get(order_id)
        if not order or order.status != OrderStatus.PENDING:
            return False
            
        order.status = OrderStatus.CANCELLED
        order.metadata['cancel_reason'] = reason
        
        # 更新数据库
        try:
            self._update_order_in_db(order)
            self.logger.info(f"订单取消: {order_id} - 原因: {reason}")
        except Exception as e:
            self.logger.error(f"更新订单状态到数据库失败: {e}")
            
        return True
        
    def get_orders_by_position(self, position_id: str) -> List[Order]:
        """获取指定仓位的所有订单"""
        return [order for order in self.active_orders.values() 
                if order.position_id == position_id]
        
    def get_active_orders(self, coin_symbol: str = None) -> List[Order]:
        """获取活跃订单"""
        orders = [order for order in self.active_orders.values() 
                 if order.status == OrderStatus.PENDING]
        
        if coin_symbol:
            orders = [order for order in orders if order.coin_symbol == coin_symbol]
            
        return orders

    def place_market_order_live(self, symbol: str, side: OrderSide, position_side: str, quantity: float) -> Tuple[bool, Dict]:
        """在实盘下市价单，返回(成功与否, 响应/错误)。"""
        if not self.live_trading or not self.client:
            return False, {"error": "live_trading_disabled"}
        try:
            response = self.client.new_order(
                symbol=symbol,
                side="BUY" if side == OrderSide.BUY else "SELL",
                positionSide=position_side,
                type="MARKET",
                quantity=str(quantity)
            )
            return True, response
        except ClientError as e:  # type: ignore
            return False, {"error": getattr(e, 'error_message', str(e)), "code": getattr(e, 'error_code', None)}
        except Exception as e:
            return False, {"error": str(e)}

    def execute_market_order(self, position_id: str, coin_symbol: str, 
                              side: OrderSide, quantity: float, price_hint: float = None,
                              position_side: str = None) -> Order:
        """统一入口：创建并执行市价单（实盘或模拟）。"""
        order = self.create_order(position_id, coin_symbol, OrderType.MARKET, side, quantity, price_hint)
        symbol = self.binance_symbol_overrides.get(coin_symbol, coin_symbol)
        pos_side = position_side or ("LONG" if side == OrderSide.BUY else "SHORT")

        if self.live_trading:
            ok, resp = self.place_market_order_live(symbol, side, pos_side, quantity)
            order.metadata['live_response'] = resp
            if ok:
                avg_price = None
                filled_qty = quantity
                try:
                    avg_price = float(resp.get('avgPrice') or resp.get('price') or 0) or price_hint
                except Exception:
                    avg_price = price_hint
                self._mark_filled(order, filled_qty, avg_price)
            else:
                order.status = OrderStatus.FAILED
                self._update_order_in_db(order)
                self.logger.error(f"实盘下单失败: {resp}")
        else:
            fill_price = price_hint if price_hint else 0.0
            self.simulate_fill_order(order.order_id, fill_price, commission=float(self.config.get('paper_commission_rate', 0.0005)))
        return order

    def _mark_filled(self, order: Order, filled_qty: float, filled_price: Optional[float]):
        """内部：标记订单为成交并写库。"""
        order.filled_quantity = filled_qty
        order.filled_price = filled_price
        fee_rate = float(self.config.get('taker_fee_rate', 0.0005))
        order.commission = (filled_price or 0) * filled_qty * fee_rate
        order.status = OrderStatus.FILLED
        order.filled_time = datetime.now(timezone.utc)
        try:
            self._update_order_in_db(order)
        except Exception as e:
            self.logger.error(f"写入成交失败: {e}")

    def _save_order_to_db(self, order: Order):
        """保存订单到数据库"""
        with self.db_manager.get_connection() as conn:
            cursor = conn.cursor()
            
            current_time = int(datetime.now(timezone.utc).timestamp() * 1000)
            
            cursor.execute('''
                INSERT INTO orders 
                (order_id, position_id, coin_symbol, order_type, side, quantity, 
                 price, status, filled_quantity, filled_price, commission, 
                 timestamp, filled_time, created_at, updated_at, metadata)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                order.order_id,
                order.position_id,
                order.coin_symbol,
                order.order_type.value,
                order.side.value,
                order.quantity,
                order.price,
                order.status.value,
                order.filled_quantity,
                order.filled_price,
                order.commission,
                int(order.timestamp.timestamp() * 1000),
                int(order.filled_time.timestamp() * 1000) if order.filled_time else None,
                current_time,
                current_time,
                str(order.metadata)
            ))
            
            conn.commit()
            
    def _update_order_in_db(self, order: Order):
        """更新数据库中的订单"""
        with self.db_manager.get_connection() as conn:
            cursor = conn.cursor()
            
            current_time = int(datetime.now(timezone.utc).timestamp() * 1000)
            
            cursor.execute('''
                UPDATE orders 
                SET status = ?, filled_quantity = ?, filled_price = ?, 
                    commission = ?, filled_time = ?, updated_at = ?, metadata = ?
                WHERE order_id = ?
            ''', (
                order.status.value,
                order.filled_quantity,
                order.filled_price,
                order.commission,
                int(order.filled_time.timestamp() * 1000) if order.filled_time else None,
                current_time,
                str(order.metadata),
                order.order_id
            ))
            
            conn.commit()
            
    def get_order_statistics(self, coin_symbol: str = None, days: int = 30) -> Dict:
        """获取订单统计信息"""
        with self.db_manager.get_connection() as conn:
            cutoff_time = int((datetime.now(timezone.utc).timestamp() - days * 24 * 3600) * 1000)
            
            query = '''
                SELECT 
                    COUNT(*) as total_orders,
                    SUM(CASE WHEN status = 'filled' THEN 1 ELSE 0 END) as filled_orders,
                    SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled_orders,
                    SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_orders,
                    AVG(CASE WHEN status = 'filled' THEN commission ELSE NULL END) as avg_commission,
                    SUM(CASE WHEN status = 'filled' THEN commission ELSE 0 END) as total_commission
                FROM orders 
                WHERE created_at >= ?
            '''
            params = [cutoff_time]
            
            if coin_symbol:
                query += " AND coin_symbol = ?"
                params.append(coin_symbol)
                
            result = conn.execute(query, params).fetchone()
            
            return {
                'total_orders': result[0] or 0,
                'filled_orders': result[1] or 0,
                'cancelled_orders': result[2] or 0,
                'failed_orders': result[3] or 0,
                'fill_rate': (result[1] / max(1, result[0])) * 100 if result[0] else 0,
                'avg_commission': result[4] or 0,
                'total_commission': result[5] or 0
            }
            
    def cleanup_old_orders(self, days: int = 30):
        """清理旧订单记录"""
        completed_orders = [
            order_id for order_id, order in self.active_orders.items()
            if order.status in [OrderStatus.FILLED, OrderStatus.CANCELLED, OrderStatus.FAILED]
            and (datetime.now(timezone.utc) - order.timestamp).days > days
        ]
        
        for order_id in completed_orders:
            del self.active_orders[order_id]
            
        self.logger.info(f"清理了 {len(completed_orders)} 个旧订单记录")

# 示例用法
if __name__ == "__main__":
    # 创建订单管理器（模拟）
    order_manager = OrderManager(config={"live_trading": False})
    
    # 创建一个市价买单并模拟成交
    buy_order = order_manager.execute_market_order(
        position_id="pos_20241215_143000",
        coin_symbol="DOTUSDT",
        side=OrderSide.BUY,
        quantity=2.0,
        price_hint=7.25
    )
    
    # 获取统计信息
    stats = order_manager.get_order_statistics("DOTUSDT")
    print("订单统计:", stats)