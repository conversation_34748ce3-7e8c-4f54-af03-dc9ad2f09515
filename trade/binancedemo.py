# futures_complete_demo.py

import os
import time
from binance.um_futures import UMFutures
from binance.error import ClientError
from dotenv import load_dotenv

# ==============================================================================
# --- 1. 配置区域 ---
# ==============================================================================
load_dotenv()

# API Key 和私钥配置
API_KEY = os.environ.get('BINANCE_APIKEY')
try:
    with open('rsa.txt', 'r') as f:
        PRIVATE_KEY = f.read()
except FileNotFoundError:
    print("错误：rsa.txt 文件未找到。请确保私钥文件与脚本在同一目录。")
    exit()

# 交易参数配置
SYMBOL = "DOTUSDT"    # 交易对
LEVERAGE = 20         # 杠杆倍数
ORDER_QUANTITY = 2    # 初始开仓数量
PARTIAL_QUANTITY = 1  # 部分平仓/止盈损的数量


# ==============================================================================
# --- 2. 初始化客户端 ---
# ==============================================================================
if not API_KEY:
    print("错误：BINANCE_APIKEY 未设置。请在 .env 文件中配置。")
    exit()

client = UMFutures(
    key=API_KEY,
    private_key=PRIVATE_KEY,
)


# ==============================================================================
# --- 3. 核心功能函数 ---
# ==============================================================================

def get_current_price(symbol: str) -> float:
    """获取指定交易对的当前市价"""
    try:
        ticker = client.ticker_price(symbol)
        price = float(ticker['price'])
        print(f"获取到 {symbol} 当前价格: {price}")
        return price
    except ClientError as e:
        print(f"获取价格失败: {e.error_message}")
        return 0.0

def set_leverage(symbol: str, leverage: int):
    """设置杠杆"""
    print(f"\n--- 正在设置 {symbol} 杠杆为 {leverage}x ---")
    try:
        response = client.change_leverage(symbol=symbol, leverage=leverage)
        print(f"设置杠杆成功: {response['leverage']}x")
    except ClientError as e:
        print(f"设置杠杆失败: {e.error_message}")

def cancel_all_open_orders(symbol: str):
    """取消所有挂单"""
    print(f"\n--- 正在取消 {symbol} 的所有挂单 ---")
    try:
        client.cancel_open_orders(symbol=symbol)
        print("所有挂单已取消。")
    except ClientError as e:
        # 如果没有挂单，会报-2011，这是正常的，不是错误
        if e.error_code == -2011:
            print("没有需要取消的挂单。")
        else:
            print(f"取消挂单失败: {e.error_message}")

# --- 多单操作 ---

def open_long(symbol: str, quantity: float):
    """市价开多单"""
    print(f"\n--- 正在市价开多单 {quantity} {symbol} ---")
    try:
        response = client.new_order(symbol=symbol, side="BUY", positionSide="LONG", type="MARKET", quantity=quantity)
        print("开多单成功!", response)
    except ClientError as e:
        print(f"开多单失败: {e.error_message}")

def partial_close_long(symbol: str, quantity: float):
    """市价部分平多仓"""
    print(f"\n--- 正在市价部分平多仓 {quantity} {symbol} ---")
    try:
        response = client.new_order(symbol=symbol, side="SELL", positionSide="LONG", type="MARKET", quantity=quantity)
        print("部分平多仓成功!", response)
    except ClientError as e:
        print(f"部分平多仓失败: {e.error_message}")

def full_close_long(symbol: str):
    """市价全部平多仓"""
    print(f"\n--- 正在市价全部平多仓 {symbol} ---")
    try:
        response = client.new_order(symbol=symbol, side="SELL", positionSide="LONG", type="STOP_MARKET", closePosition=True, stopPrice=get_current_price(symbol)*0.9) # stopPrice可以设一个较低的价格确保立即触发
        print("全部平多仓成功!", response)
    except ClientError as e:
        print(f"全部平多仓失败: {e.error_message}")

def set_sl_tp_for_long(symbol: str, price: float, quantity: float):
    """为多单设置部分止盈止损"""
    print(f"\n--- 正在为多仓设置 {quantity} 单位的部分止盈/止损 ---")
    sl_price = round(price * 0.95, 3) # 5% 止损
    tp_price = round(price * 1.10, 3) # 10% 止盈
    print(f"止损价: {sl_price}, 止盈价: {tp_price}")
    try:
        # 下止损单
        client.new_order(symbol=symbol, side="SELL", positionSide="LONG", type="STOP_MARKET", stopPrice=str(sl_price), quantity=quantity)
        print("多单部分止损设置成功!")
        # 下止盈单
        client.new_order(symbol=symbol, side="SELL", positionSide="LONG", type="TAKE_PROFIT_MARKET", stopPrice=str(tp_price), quantity=quantity)
        print("多单部分止盈设置成功!")
    except ClientError as e:
        print(f"设置多单止盈止损失败: {e.error_message}")

# --- 空单操作 (逻辑与多单镜像) ---

def open_short(symbol: str, quantity: float):
    """市价开空单"""
    print(f"\n--- 正在市价开空单 {quantity} {symbol} ---")
    try:
        response = client.new_order(symbol=symbol, side="SELL", positionSide="SHORT", type="MARKET", quantity=quantity)
        print("开空单成功!", response)
    except ClientError as e:
        print(f"开空单失败: {e.error_message}")

def partial_close_short(symbol: str, quantity: float):
    """市价部分平空仓"""
    print(f"\n--- 正在市价部分平空仓 {quantity} {symbol} ---")
    try:
        response = client.new_order(symbol=symbol, side="BUY", positionSide="SHORT", type="MARKET", quantity=quantity)
        print("部分平空仓成功!", response)
    except ClientError as e:
        print(f"部分平空仓失败: {e.error_message}")

def full_close_short(symbol: str):
    """市价全部平空仓"""
    print(f"\n--- 正在市价全部平空仓 {symbol} ---")
    try:
        response = client.new_order(symbol=symbol, side="BUY", positionSide="SHORT", type="STOP_MARKET", closePosition=True, stopPrice=get_current_price(symbol)*1.1) # stopPrice可以设一个较高的价格确保立即触发
        print("全部平空仓成功!", response)
    except ClientError as e:
        print(f"全部平空仓失败: {e.error_message}")

def set_sl_tp_for_short(symbol: str, price: float, quantity: float):
    """为空单设置部分止盈止损"""
    print(f"\n--- 正在为空仓设置 {quantity} 单位的部分止盈/止损 ---")
    sl_price = round(price * 1.05, 3) # 5% 止损
    tp_price = round(price * 0.90, 3) # 10% 止盈
    print(f"止损价: {sl_price}, 止盈价: {tp_price}")
    try:
        # 下止损单
        client.new_order(symbol=symbol, side="BUY", positionSide="SHORT", type="STOP_MARKET", stopPrice=str(sl_price), quantity=quantity)
        print("空单部分止损设置成功!")
        # 下止盈单
        client.new_order(symbol=symbol, side="BUY", positionSide="SHORT", type="TAKE_PROFIT_MARKET", stopPrice=str(tp_price), quantity=quantity)
        print("空单部分止盈设置成功!")
    except ClientError as e:
        print(f"设置空单止盈止损失败: {e.error_message}")


# ==============================================================================
# --- 4. 主执行逻辑 ---
# ==============================================================================

if __name__ == "__main__":
    # --- 安全检查和准备 ---
    try:
        client.ping()
        print("成功连接到币安服务器。")
    except Exception as e:
        print(f"连接币安服务器失败: {e}")
        exit()

    cancel_all_open_orders(SYMBOL) # 启动时先取消所有挂单，避免干扰
    set_leverage(SYMBOL, LEVERAGE)
    time.sleep(1)
    current_price = get_current_price(SYMBOL)
    time.sleep(1)


    # ==============================================================
    # --- 场景一：做多流程演示 (默认启用) ---
    # ==============================================================
    print("\n\n" + "="*30)
    print(">>> 开始执行【做多】流程演示 <<<")
    print("="*30)
    # 1. 开仓 (数量为2)
    open_long(SYMBOL, ORDER_QUANTITY)
    time.sleep(2)
    # 2. 为其中一部分(数量为1)设置止盈止损
    set_sl_tp_for_long(SYMBOL, current_price, PARTIAL_QUANTITY)
    time.sleep(2)
    # 3. 手动部分平仓 (平掉剩下的1个)
    partial_close_long(SYMBOL, PARTIAL_QUANTITY)
    time.sleep(2)
    # 4. 最后清理：取消所有剩余的挂单 (比如之前下的止盈止损单)
    cancel_all_open_orders(SYMBOL)
    print("\n【做多】流程演示完毕。")
    # ==============================================================


    # ==============================================================
    # --- 场景二：做空流程演示 (默认禁用, 如需测试请取消下面的注释) ---
    # ==============================================================
    # print("\n\n" + "="*30)
    # print(">>> 开始执行【做空】流程演示 <<<")
    # print("="*30)
    # # 1. 开仓 (数量为2)
    # open_short(SYMBOL, ORDER_QUANTITY)
    # time.sleep(2)
    # # 2. 为其中一部分(数量为1)设置止盈止损
    # set_sl_tp_for_short(SYMBOL, current_price, PARTIAL_QUANTITY)
    # time.sleep(2)
    # # 3. 手动部分平仓 (平掉剩下的1个)
    # partial_close_short(SYMBOL, PARTIAL_QUANTITY)
    # time.sleep(2)
    # # 4. 最后清理
    # cancel_all_open_orders(SYMBOL)
    # print("\n【做空】流程演示完毕。")
    # ==============================================================

    print("\n\n--- 所有演示流程执行完毕 ---")