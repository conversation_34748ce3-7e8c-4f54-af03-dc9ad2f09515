# 实时交易系统数据库集成

## 概述

本系统为实时交易系统提供了完整的数据库持久化解决方案，包括：

- **行情数据实时更新**：自动保存K线数据到数据库
- **交易信号记录**：记录所有生成的交易信号
- **仓位管理**：跟踪和记录所有仓位的生命周期
- **订单管理**：为未来的实际交易提供订单管理基础设施
- **系统监控**：记录系统状态和错误信息
- **数据分析**：提供丰富的统计和报告功能

## 文件结构

```
trade/
├── database_manager.py      # 核心数据库管理模块
├── real_data.py            # 行情数据获取模块 (已集成数据库)
├── real_signal.py          # 信号生成模块 (已集成数据库)
├── real_manager.py         # 资金管理模块 (已集成数据库)
├── real_main.py            # 主程序 (已集成数据库)
├── order_manager.py        # 订单管理模块
├── database_monitor.py     # 数据库监控和报告工具
└── README_database_integration.md
```

## 数据库结构

### 核心表结构

1. **trading_signals** - 交易信号表
   - 记录所有生成的交易信号
   - 包含信号类型、概率、目标价格等信息

2. **positions** - 仓位表
   - 跟踪所有仓位的完整生命周期
   - 记录开仓、平仓、盈亏等信息

3. **market_data_{symbol}_{interval}** - 行情数据表
   - 按币种和时间间隔分表存储
   - 实时更新K线数据

4. **orders** - 订单表
   - 为未来实际交易准备的订单管理
   - 支持多种订单类型和状态

5. **system_status** - 系统状态表
   - 记录系统运行状态、错误和警告
   - 用于系统监控和故障排查

6. **data_quality** - 数据质量监控表
   - 监控数据完整性和准确性
   - 记录数据质量检查结果

## 使用方法

### 1. 启动实时交易系统

```bash
# 启动DOT币种的实时交易系统
python real_main.py --coin DOT --update-interval 60

# 启动时会自动创建数据库文件: models/dot_trading_system.db
```

### 2. 监控系统状态

```bash
# 查看系统概览
python database_monitor.py --db models/dot_trading_system.db --action overview

# 生成性能报告
python database_monitor.py --db models/dot_trading_system.db --action report --days 30

# 检查系统健康状态
python database_monitor.py --db models/dot_trading_system.db --action health

# 生成性能图表
python database_monitor.py --db models/dot_trading_system.db --action chart --days 30 --output performance.png

# 导出交易数据
python database_monitor.py --db models/dot_trading_system.db --action export --days 30 --output trading_data.csv
```

### 3. 编程接口使用

```python
from database_manager import DatabaseManager

# 创建数据库管理器
db_manager = DatabaseManager("trading_system.db")

# 获取活跃仓位
active_positions = db_manager.get_active_positions("DOTUSDT")

# 获取交易统计
stats = db_manager.get_trading_statistics("DOTUSDT", days=30)

# 获取行情数据
from datetime import datetime, timedelta
end_time = datetime.now()
start_time = end_time - timedelta(days=7)
market_data = db_manager.get_market_data("DOTUSDT", "15m", start_time, end_time)
```

## 主要功能

### 1. 实时数据持久化

- **自动保存K线数据**：每次获取新的K线数据时自动保存到数据库
- **信号记录**：每个生成的交易信号都会被记录，包括完整的元数据
- **仓位跟踪**：从开仓到平仓的完整过程都被记录
- **系统日志**：所有重要的系统事件都会被记录

### 2. 数据完整性保障

- **事务支持**：使用数据库事务确保数据一致性
- **错误处理**：数据库操作失败时的优雅降级
- **数据验证**：插入前验证数据格式和完整性
- **重复数据处理**：使用UPSERT操作避免重复数据

### 3. 性能优化

- **连接池管理**：使用上下文管理器优化数据库连接
- **批量操作**：支持批量插入提高性能
- **索引优化**：在关键字段上创建索引
- **WAL模式**：使用Write-Ahead Logging提高并发性能

### 4. 监控和报告

- **实时统计**：提供实时的交易统计信息
- **性能分析**：详细的性能报告和图表
- **系统健康监控**：监控系统运行状态
- **数据导出**：支持将数据导出为CSV格式

## 配置选项

### 数据库配置

```python
# 在初始化时可以配置数据库路径
db_manager = DatabaseManager("custom_path/trading_system.db")

# 数据库会自动配置以下优化参数：
# - WAL模式：提高并发性能
# - 同步模式：NORMAL（平衡性能和安全性）
# - 缓存大小：10000页
```

### 数据保留策略

```python
# 清理旧数据（默认保留30天）
db_manager.cleanup_old_data(days=30)

# 清理旧订单记录
order_manager.cleanup_old_orders(days=30)
```

## 扩展功能

### 1. 订单管理系统

虽然当前系统主要用于信号生成和模拟交易，但已经为未来的实际交易准备了完整的订单管理基础设施：

```python
from order_manager import OrderManager, OrderType, OrderSide

order_manager = OrderManager(db_manager)

# 创建订单
order = order_manager.create_order(
    position_id="pos_123",
    coin_symbol="DOTUSDT",
    order_type=OrderType.MARKET,
    side=OrderSide.BUY,
    quantity=100.0
)

# 模拟订单成交
order_manager.simulate_fill_order(order.order_id, 7.25)
```

### 2. 数据质量监控

系统包含数据质量监控功能，可以检测：
- 数据完整性
- 数据准确性
- 数据及时性

### 3. 多币种支持

数据库设计支持多币种同时运行：
- 每个币种使用独立的行情数据表
- 统一的信号和仓位管理
- 按币种分类的统计和报告

## 故障排查

### 常见问题

1. **数据库锁定**
   ```
   解决方案：系统使用WAL模式和连接超时机制，通常会自动恢复
   ```

2. **磁盘空间不足**
   ```
   解决方案：定期运行cleanup_old_data()清理旧数据
   ```

3. **数据不一致**
   ```
   解决方案：检查系统日志，使用事务回滚机制
   ```

### 日志分析

系统会记录详细的操作日志：
- 数据库操作成功/失败
- 数据保存状态
- 系统错误和警告

## 性能监控

### 关键指标

- **数据库大小**：监控数据库文件大小增长
- **查询性能**：监控关键查询的执行时间
- **错误率**：监控数据库操作的错误率
- **数据完整性**：定期检查数据完整性

### 优化建议

1. **定期清理**：设置自动清理旧数据的计划任务
2. **索引维护**：定期重建索引以保持查询性能
3. **备份策略**：实施定期备份策略
4. **监控告警**：设置关键指标的告警阈值

## 未来扩展

1. **实时交易接口**：集成真实的交易所API
2. **风险管理**：添加更复杂的风险控制机制
3. **策略回测**：基于历史数据的策略回测功能
4. **分布式部署**：支持多实例部署和数据同步
5. **Web界面**：提供Web界面进行监控和管理

## 注意事项

1. **数据安全**：确保数据库文件的安全性和备份
2. **性能监控**：定期监控系统性能和资源使用
3. **版本兼容**：升级时注意数据库结构的兼容性
4. **错误处理**：关注系统日志中的错误信息
5. **资源管理**：合理配置数据保留策略避免磁盘空间不足