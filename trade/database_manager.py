#!/usr/bin/env python3
"""
数据库管理模块
负责实时交易系统的数据持久化，包括：
- 行情数据实时更新
- 交易信号记录
- 仓位管理记录
- 订单执行记录
"""

import sqlite3
import pandas as pd
import logging
import json
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any, Tuple
from contextlib import contextmanager
import threading
import hashlib

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_path: str = "trading_system.db"):
        """
        初始化数据库管理器
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
        self.lock = threading.Lock()
        self.logger = logging.getLogger('DatabaseManager')
        
        # 初始化数据库结构
        self._init_database()
        
    @contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            conn.execute("PRAGMA journal_mode=WAL")
            conn.execute("PRAGMA synchronous=NORMAL")
            conn.execute("PRAGMA cache_size=10000")
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            raise e
        finally:
            if conn:
                conn.close()
                
    def _init_database(self):
        """初始化数据库表结构"""
        with self.lock:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 1. 行情数据表 (按币种分表)
                # 表名格式: market_data_{symbol}_{interval}
                # 例如: market_data_DOTUSDT_15m
                
                # 2. 交易信号表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS trading_signals (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        signal_id TEXT UNIQUE NOT NULL,
                        coin_symbol TEXT NOT NULL,
                        signal_type INTEGER NOT NULL,  -- 1: 看涨, 0: 看跌
                        probability REAL NOT NULL,
                        entry_price REAL NOT NULL,
                        up_target REAL NOT NULL,
                        down_target REAL NOT NULL,
                        max_hold_minutes INTEGER NOT NULL,
                        timestamp INTEGER NOT NULL,
                        kline_timestamp INTEGER NOT NULL,
                        created_at INTEGER NOT NULL,
                        metadata TEXT  -- JSON格式的额外信息
                    )
                ''')
                
                # 创建交易信号表的索引
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_trading_signals_coin_symbol ON trading_signals(coin_symbol)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_trading_signals_timestamp ON trading_signals(timestamp)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_trading_signals_created_at ON trading_signals(created_at)')
                
                # 3. 仓位管理表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS positions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        position_id TEXT UNIQUE NOT NULL,
                        signal_id TEXT NOT NULL,
                        coin_symbol TEXT NOT NULL,
                        signal_type INTEGER NOT NULL,
                        probability REAL NOT NULL,
                        entry_price REAL, -- Can be NULL until order is filled
                        entry_time INTEGER, -- Can be NULL until order is filled
                        up_target REAL NOT NULL,
                        down_target REAL NOT NULL,
                        expire_time INTEGER NOT NULL,
                        status TEXT NOT NULL,  -- pending_open, active, open_failed, closing, closing_failed, completed
                        exit_price REAL,
                        exit_time INTEGER,
                        result INTEGER,  -- 1: 成功, 0: 失败, -1: 超时, -2: 开仓失败, -3: 平仓重试失败
                        pnl REAL DEFAULT 0.0,
                        exit_reason TEXT,
                        entry_order_id TEXT, -- NEW: To store the entry order ID
                        exit_order_id TEXT,  -- NEW: To store the exit order ID
                        pending_result INTEGER, -- NEW: For retry protection
                        pending_reason TEXT,   -- NEW: For retry protection
                        exit_attempts INTEGER DEFAULT 0, -- NEW: Retry counter
                        last_retry_time INTEGER,        -- NEW: Last retry timestamp
                        closing_start_time INTEGER,     -- NEW: When closing started
                        created_at INTEGER NOT NULL,
                        updated_at INTEGER NOT NULL,
                        metadata TEXT, -- JSON格式的额外信息
                        FOREIGN KEY (signal_id) REFERENCES trading_signals(signal_id)
                    )
                ''')
                
                # 创建仓位表的索引
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_positions_coin_symbol ON positions(coin_symbol)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_positions_status ON positions(status)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_positions_entry_time ON positions(entry_time)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_positions_created_at ON positions(created_at)')
                
                # 4. 订单执行表 (为未来扩展准备)
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS orders (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        order_id TEXT UNIQUE NOT NULL,
                        position_id TEXT NOT NULL,
                        coin_symbol TEXT NOT NULL,
                        order_type TEXT NOT NULL,  -- market, limit, stop
                        side TEXT NOT NULL,  -- buy, sell
                        quantity REAL NOT NULL,
                        price REAL,
                        status TEXT NOT NULL DEFAULT 'pending',  -- pending, filled, cancelled, failed
                        filled_quantity REAL DEFAULT 0.0,
                        filled_price REAL,
                        commission REAL DEFAULT 0.0,
                        timestamp INTEGER NOT NULL,
                        filled_time INTEGER,
                        created_at INTEGER NOT NULL,
                        updated_at INTEGER NOT NULL,
                        metadata TEXT,  -- JSON格式的额外信息
                        FOREIGN KEY (position_id) REFERENCES positions(position_id)
                    )
                ''')
                
                # 创建订单表的索引
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_orders_coin_symbol ON orders(coin_symbol)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_orders_timestamp ON orders(timestamp)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at)')
                
                # 5. 系统状态表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS system_status (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        coin_symbol TEXT NOT NULL,
                        status_type TEXT NOT NULL,  -- heartbeat, error, warning, info
                        message TEXT NOT NULL,
                        timestamp INTEGER NOT NULL,
                        metadata TEXT  -- JSON格式的额外信息
                    )
                ''')
                
                # 创建系统状态表的索引
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_system_status_coin_symbol ON system_status(coin_symbol)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_system_status_type ON system_status(status_type)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_system_status_timestamp ON system_status(timestamp)')
                
                # 6. 数据质量监控表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS data_quality (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        table_name TEXT NOT NULL,
                        coin_symbol TEXT NOT NULL,
                        check_type TEXT NOT NULL,  -- completeness, accuracy, timeliness
                        check_result TEXT NOT NULL,  -- pass, fail, warning
                        details TEXT,
                        timestamp INTEGER NOT NULL,
                        metadata TEXT
                    )
                ''')
                
                # 创建数据质量表的索引
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_data_quality_table_name ON data_quality(table_name)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_data_quality_coin_symbol ON data_quality(coin_symbol)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_data_quality_check_type ON data_quality(check_type)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_data_quality_timestamp ON data_quality(timestamp)')
                
                conn.commit()
                self.logger.info(f"数据库初始化完成: {self.db_path}")
                
    def create_market_data_table(self, coin_symbol: str, interval: str):
        """为特定币种和时间间隔创建行情数据表"""
        table_name = f"market_data_{coin_symbol}_{interval}"
        
        with self.lock:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute(f'''
                    CREATE TABLE IF NOT EXISTS {table_name} (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp INTEGER UNIQUE NOT NULL,
                        open REAL NOT NULL,
                        high REAL NOT NULL,
                        low REAL NOT NULL,
                        close REAL NOT NULL,
                        volume REAL NOT NULL,
                        close_time INTEGER,
                        quote_volume REAL,
                        trade_count INTEGER,
                        taker_buy_base_volume REAL,
                        taker_buy_quote_volume REAL,
                        data_hash TEXT,
                        created_at INTEGER NOT NULL,
                        updated_at INTEGER NOT NULL
                    )
                ''')
                
                # 创建行情数据表的索引
                cursor.execute(f'CREATE INDEX IF NOT EXISTS idx_{table_name}_timestamp ON {table_name}(timestamp)')
                
                conn.commit()
                self.logger.info(f"创建行情数据表: {table_name}")
                
    def insert_market_data(self, coin_symbol: str, interval: str, df: pd.DataFrame):
        """插入行情数据"""
        table_name = f"market_data_{coin_symbol}_{interval}"
        
        # 确保表存在
        self.create_market_data_table(coin_symbol, interval)
        
        with self.lock:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                current_time = int(datetime.now(timezone.utc).timestamp() * 1000)
                
                # 准备数据
                records = []
                for idx, row in df.iterrows():
                    timestamp = int(idx.timestamp() * 1000)
                    data_hash = self._calculate_row_hash(row.to_dict())
                    
                    record = (
                        timestamp,
                        float(row['open']),
                        float(row['high']),
                        float(row['low']),
                        float(row['close']),
                        float(row['volume']),
                        timestamp + (int(interval.replace('m', '')) * 60 * 1000),  # close_time
                        float(row.get('quote_volume', 0)),
                        int(row.get('trade_count', 0)),
                        float(row.get('taker_buy_base_volume', 0)),
                        float(row.get('taker_buy_quote_volume', 0)),
                        data_hash,
                        current_time,
                        current_time
                    )
                    records.append(record)
                
                # 批量插入
                cursor.executemany(f'''
                    INSERT OR REPLACE INTO {table_name} 
                    (timestamp, open, high, low, close, volume, close_time, 
                     quote_volume, trade_count, taker_buy_base_volume, 
                     taker_buy_quote_volume, data_hash, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', records)
                
                conn.commit()
                self.logger.debug(f"插入 {len(records)} 条行情数据到 {table_name}")
                
    def insert_trading_signal(self, signal_info: Dict) -> str:
        """插入交易信号记录"""
        with self.lock:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                current_time = int(datetime.now(timezone.utc).timestamp() * 1000)
                signal_id = f"signal_{signal_info['timestamp'].strftime('%Y%m%d_%H%M%S')}_{signal_info['coin_symbol']}"
                
                cursor.execute('''
                    INSERT OR REPLACE INTO trading_signals 
                    (signal_id, coin_symbol, signal_type, probability, entry_price,
                     up_target, down_target, max_hold_minutes, timestamp, 
                     kline_timestamp, created_at, metadata)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    signal_id,
                    signal_info['coin_symbol'],
                    signal_info['signal_type'],
                    signal_info['probability'],
                    signal_info['price'],
                    signal_info['up_target'],
                    signal_info['down_target'],
                    signal_info['max_hold_minutes'],
                    int(signal_info['timestamp'].timestamp() * 1000),
                    int(signal_info.get('kline_timestamp', signal_info['timestamp']).timestamp() * 1000),
                    current_time,
                    json.dumps(signal_info.get('metadata', {}))
                ))
                
                conn.commit()
                self.logger.info(f"插入交易信号: {signal_id}")
                return signal_id
                
    def insert_position(self, position_info: Dict, signal_id: str) -> str:
        """插入仓位记录"""
        with self.lock:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                current_time = int(datetime.now(timezone.utc).timestamp() * 1000)
                
                cursor.execute('''
                    INSERT OR REPLACE INTO positions 
                    (position_id, signal_id, coin_symbol, signal_type, probability,
                     entry_price, entry_time, up_target, down_target, expire_time,
                     status, entry_order_id, exit_order_id, pending_result, pending_reason,
                     exit_attempts, last_retry_time, closing_start_time, created_at, updated_at, metadata)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    position_info['position_id'],
                    signal_id,
                    position_info['coin_symbol'],
                    position_info['signal_type'],
                    position_info['probability'],
                    position_info.get('entry_price'),  # Can be NULL
                    int(position_info.get('entry_time').timestamp() * 1000) if position_info.get('entry_time') else None,
                    position_info['up_target'],
                    position_info['down_target'],
                    int(position_info['expire_time'].timestamp() * 1000),
                    position_info.get('status', 'pending_open'),  # Default to pending_open
                    position_info.get('entry_order_id'),  # Can be NULL initially
                    position_info.get('exit_order_id'),   # Can be NULL initially
                    position_info.get('pending_result'),  # Can be NULL initially
                    position_info.get('pending_reason'),  # Can be NULL initially
                    position_info.get('exit_attempts', 0),  # Default to 0
                    int(position_info.get('last_retry_time').timestamp() * 1000) if position_info.get('last_retry_time') else None,
                    int(position_info.get('closing_start_time').timestamp() * 1000) if position_info.get('closing_start_time') else None,
                    current_time,
                    current_time,
                    json.dumps(position_info.get('metadata', {}))
                ))
                
                conn.commit()
                self.logger.info(f"插入仓位记录: {position_info['position_id']} (状态: {position_info.get('status', 'pending_open')})")
                return position_info['position_id']
                
    def update_position(self, position_id: str, update_info: Dict):
        """
        更新仓位状态。
        现在更灵活，可以更新提供的任何字段。
        """
        with self.lock:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                current_time = int(datetime.now(timezone.utc).timestamp() * 1000)
                update_info['updated_at'] = current_time
                
                # 构建 SET 子句
                set_clause = ", ".join([f"{key} = ?" for key in update_info.keys()])
                params = list(update_info.values())
                params.append(position_id)
                
                query = f"UPDATE positions SET {set_clause} WHERE position_id = ?"
                
                cursor.execute(query, tuple(params))
                
                conn.commit()
                # 安全地序列化日志数据，处理datetime等不可序列化对象
                safe_data = {}
                for k, v in update_info.items():
                    if k != 'updated_at':
                        if isinstance(v, datetime):
                            safe_data[k] = v.isoformat()
                        else:
                            safe_data[k] = v
                
                self.logger.info(f"更新仓位: {position_id} -> {json.dumps(safe_data)}")
                
    def log_system_status(self, coin_symbol: str, status_type: str, message: str, metadata: Dict = None):
        """记录系统状态"""
        with self.lock:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                current_time = int(datetime.now(timezone.utc).timestamp() * 1000)
                
                cursor.execute('''
                    INSERT INTO system_status 
                    (coin_symbol, status_type, message, timestamp, metadata)
                    VALUES (?, ?, ?, ?, ?)
                ''', (
                    coin_symbol,
                    status_type,
                    message,
                    current_time,
                    json.dumps(metadata or {})
                ))
                
                conn.commit()
                
    def get_active_positions(self, coin_symbol: str = None) -> List[Dict]:
        """获取活跃仓位"""
        with self.get_connection() as conn:
            query = "SELECT * FROM positions WHERE status = 'active'"
            params = []
            
            if coin_symbol:
                query += " AND coin_symbol = ?"
                params.append(coin_symbol)
                
            query += " ORDER BY entry_time DESC"
            
            df = pd.read_sql_query(query, conn, params=params)
            return df.to_dict('records')
            
    def get_trading_statistics(self, coin_symbol: str = None, days: int = 30) -> Dict:
        """获取交易统计信息"""
        with self.get_connection() as conn:
            cutoff_time = int((datetime.now(timezone.utc).timestamp() - days * 24 * 3600) * 1000)
            
            query = '''
                SELECT 
                    COUNT(*) as total_positions,
                    SUM(CASE WHEN result = 1 THEN 1 ELSE 0 END) as successful_positions,
                    SUM(CASE WHEN result = 0 THEN 1 ELSE 0 END) as failed_positions,
                    SUM(CASE WHEN result = -1 THEN 1 ELSE 0 END) as timeout_positions,
                    AVG(pnl) as avg_pnl,
                    SUM(pnl) as total_pnl,
                    AVG(probability) as avg_probability
                FROM positions 
                WHERE created_at >= ?
            '''
            params = [cutoff_time]
            
            if coin_symbol:
                query += " AND coin_symbol = ?"
                params.append(coin_symbol)
                
            result = pd.read_sql_query(query, conn, params=params)
            stats = result.iloc[0].to_dict()
            
            # 计算胜率
            valid_trades = stats['successful_positions'] + stats['failed_positions']
            stats['success_rate'] = (stats['successful_positions'] / max(1, valid_trades)) * 100
            
            return stats
            
    def cleanup_old_data(self, days: int = 30):
        """清理旧数据"""
        cutoff_time = int((datetime.now(timezone.utc).timestamp() - days * 24 * 3600) * 1000)
        
        with self.lock:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 清理旧的系统状态记录
                cursor.execute('DELETE FROM system_status WHERE timestamp < ?', (cutoff_time,))
                
                # 清理旧的数据质量记录
                cursor.execute('DELETE FROM data_quality WHERE timestamp < ?', (cutoff_time,))
                
                conn.commit()
                self.logger.info(f"清理了 {days} 天前的旧数据")
                
    def _calculate_row_hash(self, row_data: Dict) -> str:
        """计算行数据的哈希值"""
        data_str = json.dumps(row_data, sort_keys=True)
        return hashlib.md5(data_str.encode()).hexdigest()
        
    def get_market_data(self, coin_symbol: str, interval: str, 
                       start_time: datetime = None, end_time: datetime = None,
                       limit: int = None) -> pd.DataFrame:
        """获取行情数据"""
        table_name = f"market_data_{coin_symbol}_{interval}"
        
        with self.get_connection() as conn:
            query = f"SELECT * FROM {table_name}"
            params = []
            conditions = []
            
            if start_time:
                conditions.append("timestamp >= ?")
                params.append(int(start_time.timestamp() * 1000))
                
            if end_time:
                conditions.append("timestamp <= ?")
                params.append(int(end_time.timestamp() * 1000))
                
            if conditions:
                query += " WHERE " + " AND ".join(conditions)
                
            query += " ORDER BY timestamp DESC"
            
            if limit:
                query += f" LIMIT {limit}"
                
            try:
                df = pd.read_sql_query(query, conn, params=params)
                if not df.empty:
                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms', utc=True)
                    df.set_index('timestamp', inplace=True)
                return df
            except Exception as e:
                self.logger.error(f"获取行情数据失败: {e}")
                return pd.DataFrame()