#!/usr/bin/env python3
"""
数据库监控和报告工具
提供交易系统数据库的监控、统计和报告功能
"""

import argparse
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timezone, timedelta
from database_manager import DatabaseManager
import json

class DatabaseMonitor:
    """数据库监控器"""
    
    def __init__(self, db_path: str):
        self.db_manager = DatabaseManager(db_path)
        
    def get_system_overview(self, coin_symbol: str = None, days: int = 7) -> dict:
        """获取系统概览"""
        stats = self.db_manager.get_trading_statistics(coin_symbol, days)
        active_positions = self.db_manager.get_active_positions(coin_symbol)
        
        return {
            'trading_stats': stats,
            'active_positions_count': len(active_positions),
            'active_positions': active_positions[:10]  # 只显示前10个
        }
        
    def get_performance_report(self, coin_symbol: str = None, days: int = 30):
        """生成性能报告"""
        with self.db_manager.get_connection() as conn:
            cutoff_time = int((datetime.now(timezone.utc).timestamp() - days * 24 * 3600) * 1000)
            
            # 基础查询
            query = '''
                SELECT 
                    coin_symbol,
                    signal_type,
                    probability,
                    entry_price,
                    exit_price,
                    result,
                    pnl,
                    (exit_time - entry_time) / (1000 * 60) as duration_minutes,
                    DATE(entry_time / 1000, 'unixepoch') as trade_date
                FROM positions 
                WHERE status = 'completed' AND created_at >= ?
            '''
            params = [cutoff_time]
            
            if coin_symbol:
                query += " AND coin_symbol = ?"
                params.append(coin_symbol)
                
            query += " ORDER BY entry_time DESC"
            
            df = pd.read_sql_query(query, conn, params=params)
            
            if df.empty:
                return {"message": "没有找到交易数据"}
                
            # 计算各种统计指标
            report = {
                'total_trades': len(df),
                'successful_trades': len(df[df['result'] == 1]),
                'failed_trades': len(df[df['result'] == 0]),
                'timeout_trades': len(df[df['result'] == -1]),
                'success_rate': len(df[df['result'] == 1]) / max(1, len(df[df['result'] != -1])) * 100,
                'avg_pnl': df['pnl'].mean() * 100,
                'total_pnl': df['pnl'].sum() * 100,
                'avg_duration': df['duration_minutes'].mean(),
                'avg_probability': df['probability'].mean(),
                'best_trade': df.loc[df['pnl'].idxmax()].to_dict() if not df.empty else None,
                'worst_trade': df.loc[df['pnl'].idxmin()].to_dict() if not df.empty else None,
            }
            
            # 按日期统计
            daily_stats = df.groupby('trade_date').agg({
                'result': ['count', lambda x: (x == 1).sum()],
                'pnl': 'sum'
            }).round(4)
            
            report['daily_performance'] = daily_stats.to_dict()
            
            # 按信号类型统计
            signal_stats = df.groupby('signal_type').agg({
                'result': ['count', lambda x: (x == 1).sum()],
                'pnl': ['mean', 'sum'],
                'probability': 'mean'
            }).round(4)
            
            report['signal_type_performance'] = signal_stats.to_dict()
            
            return report
            
    def get_market_data_summary(self, coin_symbol: str, interval: str, days: int = 7):
        """获取行情数据摘要"""
        end_time = datetime.now(timezone.utc)
        start_time = end_time - timedelta(days=days)
        
        df = self.db_manager.get_market_data(coin_symbol, interval, start_time, end_time)
        
        if df.empty:
            return {"message": "没有找到行情数据"}
            
        summary = {
            'total_candles': len(df),
            'date_range': {
                'start': df.index.min().isoformat(),
                'end': df.index.max().isoformat()
            },
            'price_stats': {
                'min': df['low'].min(),
                'max': df['high'].max(),
                'avg': df['close'].mean(),
                'current': df['close'].iloc[-1] if not df.empty else None
            },
            'volume_stats': {
                'total': df['volume'].sum(),
                'avg': df['volume'].mean(),
                'max': df['volume'].max()
            }
        }
        
        return summary
        
    def get_system_health(self, coin_symbol: str = None, hours: int = 24):
        """获取系统健康状态"""
        with self.db_manager.get_connection() as conn:
            cutoff_time = int((datetime.now(timezone.utc).timestamp() - hours * 3600) * 1000)
            
            query = '''
                SELECT status_type, COUNT(*) as count
                FROM system_status 
                WHERE timestamp >= ?
            '''
            params = [cutoff_time]
            
            if coin_symbol:
                query += " AND coin_symbol = ?"
                params.append(coin_symbol)
                
            query += " GROUP BY status_type"
            
            status_counts = pd.read_sql_query(query, conn, params=params)
            
            # 获取最近的错误和警告
            error_query = '''
                SELECT coin_symbol, message, timestamp
                FROM system_status 
                WHERE status_type IN ('error', 'warning') AND timestamp >= ?
            '''
            error_params = [cutoff_time]
            
            if coin_symbol:
                error_query += " AND coin_symbol = ?"
                error_params.append(coin_symbol)
                
            error_query += " ORDER BY timestamp DESC LIMIT 10"
            
            recent_issues = pd.read_sql_query(error_query, conn, params=error_params)
            
            return {
                'status_summary': status_counts.to_dict('records'),
                'recent_issues': recent_issues.to_dict('records')
            }
            
    def create_performance_chart(self, coin_symbol: str = None, days: int = 30, save_path: str = None):
        """创建性能图表"""
        with self.db_manager.get_connection() as conn:
            cutoff_time = int((datetime.now(timezone.utc).timestamp() - days * 24 * 3600) * 1000)
            
            query = '''
                SELECT 
                    DATE(entry_time / 1000, 'unixepoch') as trade_date,
                    result,
                    pnl,
                    probability
                FROM positions 
                WHERE status = 'completed' AND created_at >= ?
            '''
            params = [cutoff_time]
            
            if coin_symbol:
                query += " AND coin_symbol = ?"
                params.append(coin_symbol)
                
            df = pd.read_sql_query(query, conn, params=params)
            
            if df.empty:
                print("没有数据可以绘制图表")
                return
                
            # 创建子图
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle(f'交易系统性能报告 - {coin_symbol or "所有币种"} (最近{days}天)', fontsize=16)
            
            # 1. 每日盈亏
            daily_pnl = df.groupby('trade_date')['pnl'].sum() * 100
            axes[0, 0].plot(daily_pnl.index, daily_pnl.values, marker='o')
            axes[0, 0].set_title('每日盈亏 (%)')
            axes[0, 0].tick_params(axis='x', rotation=45)
            axes[0, 0].grid(True, alpha=0.3)
            
            # 2. 胜率分布
            success_rate = df.groupby('trade_date').apply(
                lambda x: (x['result'] == 1).sum() / max(1, len(x[x['result'] != -1])) * 100
            )
            axes[0, 1].bar(success_rate.index, success_rate.values, alpha=0.7)
            axes[0, 1].set_title('每日胜率 (%)')
            axes[0, 1].tick_params(axis='x', rotation=45)
            axes[0, 1].grid(True, alpha=0.3)
            
            # 3. 概率分布
            axes[1, 0].hist(df['probability'], bins=20, alpha=0.7, edgecolor='black')
            axes[1, 0].set_title('信号概率分布')
            axes[1, 0].set_xlabel('概率')
            axes[1, 0].set_ylabel('频次')
            axes[1, 0].grid(True, alpha=0.3)
            
            # 4. 盈亏分布
            pnl_percent = df['pnl'] * 100
            axes[1, 1].hist(pnl_percent, bins=20, alpha=0.7, edgecolor='black')
            axes[1, 1].set_title('盈亏分布 (%)')
            axes[1, 1].set_xlabel('盈亏 (%)')
            axes[1, 1].set_ylabel('频次')
            axes[1, 1].grid(True, alpha=0.3)
            
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                print(f"图表已保存到: {save_path}")
            else:
                plt.show()
                
    def export_data(self, coin_symbol: str = None, days: int = 30, output_file: str = None):
        """导出数据到CSV"""
        with self.db_manager.get_connection() as conn:
            cutoff_time = int((datetime.now(timezone.utc).timestamp() - days * 24 * 3600) * 1000)
            
            # 导出交易记录
            query = '''
                SELECT 
                    p.*,
                    s.signal_id,
                    datetime(p.entry_time / 1000, 'unixepoch') as entry_datetime,
                    datetime(p.exit_time / 1000, 'unixepoch') as exit_datetime
                FROM positions p
                LEFT JOIN trading_signals s ON p.signal_id = s.signal_id
                WHERE p.created_at >= ?
            '''
            params = [cutoff_time]
            
            if coin_symbol:
                query += " AND p.coin_symbol = ?"
                params.append(coin_symbol)
                
            df = pd.read_sql_query(query, conn, params=params)
            
            if output_file:
                df.to_csv(output_file, index=False)
                print(f"数据已导出到: {output_file}")
            else:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"trading_data_{coin_symbol or 'all'}_{timestamp}.csv"
                df.to_csv(filename, index=False)
                print(f"数据已导出到: {filename}")
                
            return df

def main():
    parser = argparse.ArgumentParser(description="交易系统数据库监控工具")
    parser.add_argument("--db", required=True, help="数据库文件路径")
    parser.add_argument("--coin", help="币种符号 (如: DOTUSDT)")
    parser.add_argument("--days", type=int, default=7, help="查询天数")
    parser.add_argument("--action", choices=['overview', 'report', 'health', 'chart', 'export'], 
                       default='overview', help="执行的操作")
    parser.add_argument("--output", help="输出文件路径")
    
    args = parser.parse_args()
    
    monitor = DatabaseMonitor(args.db)
    
    if args.action == 'overview':
        overview = monitor.get_system_overview(args.coin, args.days)
        print("=== 系统概览 ===")
        print(json.dumps(overview, indent=2, default=str))
        
    elif args.action == 'report':
        report = monitor.get_performance_report(args.coin, args.days)
        print("=== 性能报告 ===")
        print(json.dumps(report, indent=2, default=str))
        
    elif args.action == 'health':
        health = monitor.get_system_health(args.coin, args.days * 24)
        print("=== 系统健康状态 ===")
        print(json.dumps(health, indent=2, default=str))
        
    elif args.action == 'chart':
        monitor.create_performance_chart(args.coin, args.days, args.output)
        
    elif args.action == 'export':
        monitor.export_data(args.coin, args.days, args.output)

if __name__ == "__main__":
    main()