# real_signal.py

import pandas as pd
import joblib
import json
import logging
from datetime import datetime, timezone
from typing import Callable, Optional, Dict
import pytz

# Assume calculate_features is in a shared utility module.
from model_utils_815 import calculate_features

class SignalGenerator:
    """
    信号生成模块
    - Loads the ML model and configuration.
    - Generates trading signals based on K-line data.
    - Passes signals and price updates to the portfolio manager via callbacks.
    """
    def __init__(self, model_file: str, config_file: str, logger: logging.Logger,
                 display_name: str, local_tz: pytz.BaseTzInfo):
        self.logger = logger
        self.display_name = display_name
        self.local_tz = local_tz
        self.model = None
        self.config = None
        self.signal_handler: Optional[Callable[[Dict], None]] = None
        self.price_update_handler: Optional[Callable[[float], None]] = None

        if not self._load_model_and_config(model_file, config_file):
            raise RuntimeError("模型或配置加载失败，无法启动信号生成器。")

    def register_handlers(self, signal_handler: Callable[[Dict], None], price_update_handler: Callable[[float], None]):
        """Registers callback functions from the portfolio manager."""
        self.logger.info("信号生成器：资金管理模块的回调函数已注册。")
        self.signal_handler = signal_handler
        self.price_update_handler = price_update_handler

    def _load_model_and_config(self, model_file, config_file) -> bool:
        """Loads the model and configuration, logging details."""
        try:
            self.model = joblib.load(model_file)
            with open(config_file, 'r') as f:
                self.config = json.load(f)
            self.logger.info("[系统] 模型和配置加载成功")
            self.logger.info(f"   - 目标涨幅: {self.config['up_threshold']*100:.2f}%")
            self.logger.info(f"   - 目标跌幅: {self.config['down_threshold']*100:.2f}%")
            self.logger.info(f"   - 最优阈值: {self.config['best_threshold']:.3f}")
            self.logger.info(f"   - K线周期: {self.config.get('timeframe_minutes', 5)}分钟")
            return True
        except Exception as e:
            self.logger.critical(f"加载模型或配置失败: {e}", exc_info=True)
            return False

    def _make_prediction(self, df: pd.DataFrame) -> Optional[Dict]:
        """Makes a prediction based on the latest K-line data."""
        try:
            features_df = calculate_features(df, timeframe=self.config['timeframe_minutes'])
            features_df_clean = features_df.dropna()

            if len(features_df_clean) < 2:
                self.logger.warning(f"特征计算后数据不足2行，无法预测。")
                return None

            latest_features = features_df_clean.iloc[-2]
            kline_timestamp = latest_features.name.to_pydatetime()
            price = latest_features['close']
            probability = self.model.predict_proba(latest_features[self.config['feature_list']].to_frame().T)[0, 1]

            best_threshold = self.config['best_threshold']
            guess = None
            if probability > best_threshold: guess = 1
            elif probability < (1 - best_threshold): guess = 0

            if guess is not None:
                return {'guess': guess, 'probability': probability, 'price': price, 'timestamp': kline_timestamp.replace(tzinfo=timezone.utc)}
            else:
                local_ts_str = kline_timestamp.astimezone(self.local_tz).strftime('%Y-%m-%d %H:%M:%S %z')
                coin_tag = self.display_name.split('/')[0]
                log_msg = f"[{coin_tag}] 分析已收盘K线: {local_ts_str}, 价格: {price:.4f}, 信心不足 ({probability:.4f}), 放弃预测"
                self.logger.info(f"[分析结果] {log_msg}")
                return None
        except Exception as e:
            self.logger.error(f"预测过程中发生错误: {e}", exc_info=True)
            return None

    def on_new_kline_data(self, df: Optional[pd.DataFrame], current_price: float):
        """Callback method invoked by the MarketDataFetcher."""
        if self.price_update_handler:
            self.price_update_handler(current_price)

        if df is not None:
            signal = self._make_prediction(df)
            if signal and self.signal_handler:
                self.signal_handler(signal)