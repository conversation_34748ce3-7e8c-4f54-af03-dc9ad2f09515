#!/usr/bin/env python3
"""
测试平仓重试保护和防重复机制
验证closing状态的仓位不会被重复调用平仓
"""

import sys
import logging
from datetime import datetime, timezone
import pytz
import time

# 添加trade目录到路径
sys.path.append('trade')

from trade.real_manager import PortfolioManager
from trade.database_manager import DatabaseManager

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger('TestRetryProtection')

def test_retry_protection():
    """测试重试保护机制"""
    logger = setup_logging()
    
    # 创建数据库管理器
    db_manager = DatabaseManager("test_retry_protection.db")
    
    # 测试配置
    config = {
        'risk_per_order_pct': 1.0,
        'max_active_positions': 3,
        'paper_usdt_balance': 1000.0,
        'up_threshold': 0.02,
        'down_threshold': 0.02,
        'max_lookforward_minutes': 60,
        'order_manager': {
            'live_trading': False,
            'paper_usdt_balance': 1000.0
        }
    }
    
    try:
        # 创建PortfolioManager
        portfolio_mgr = PortfolioManager(
            config=config,
            logger=logger,
            display_name="ETH/USDT",
            local_tz=pytz.timezone('Asia/Shanghai'),
            enable_sound=False,
            db_manager=db_manager
        )
        
        logger.info("✅ PortfolioManager创建成功")
        
        # 模拟交易信号
        test_signal = {
            'guess': 1,  # 看涨
            'probability': 0.85,
            'price': 3000.0,
            'timestamp': datetime.now(timezone.utc),
            'signal_id': 'test_signal_001'
        }
        
        logger.info("📊 创建测试信号...")
        portfolio_mgr.on_new_signal(test_signal)
        
        # 检查仓位状态
        active_positions = portfolio_mgr.active_positions
        if not active_positions:
            logger.error("❌ 没有创建任何仓位")
            return False
            
        pos_id = list(active_positions.keys())[0]
        pos = active_positions[pos_id]
        logger.info(f"📈 仓位 {pos_id} 状态: {pos['status']}")
        
        # 模拟价格达到目标，触发平仓
        logger.info("🔄 模拟价格达到上涨目标，触发平仓...")
        portfolio_mgr.on_price_update(3060.0)  # 2%上涨
        
        # 检查是否进入closing状态
        pos = active_positions.get(pos_id)
        if not pos:
            logger.info("✅ 仓位已完成，从活跃列表中移除")
        else:
            logger.info(f"📈 仓位状态: {pos['status']}")
            if pos['status'] == 'closing':
                logger.info(f"   重试次数: {pos.get('exit_attempts', 0)}")
                logger.info(f"   上次重试: {pos.get('last_retry_time', 'N/A')}")
        
        # 模拟多次价格更新，测试重试保护
        logger.info("🔄 模拟多次价格更新，测试重试保护...")
        for i in range(5):
            logger.info(f"   第{i+1}次价格更新...")
            portfolio_mgr.on_price_update(3060.0 + i)  # 轻微价格变化
            
            # 检查仓位状态
            pos = active_positions.get(pos_id)
            if not pos:
                logger.info(f"   ✅ 第{i+1}次更新后仓位已完成")
                break
            else:
                logger.info(f"   📈 仓位状态: {pos['status']}, 重试次数: {pos.get('exit_attempts', 0)}")
                
                # 如果重试次数达到上限，应该被标记为失败
                if pos.get('exit_attempts', 0) >= 3:
                    logger.info("   ⚠️ 重试次数已达上限")
                    break
                
                # 模拟30秒间隔
                time.sleep(0.1)  # 测试用，实际应该是30秒
        
        # 打印最终状态摘要
        portfolio_mgr.print_status()
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_duplicate_prevention():
    """测试防重复机制"""
    logger = setup_logging()
    
    # 创建数据库管理器
    db_manager = DatabaseManager("test_duplicate_prevention.db")
    
    # 测试配置
    config = {
        'risk_per_order_pct': 1.0,
        'max_active_positions': 3,
        'paper_usdt_balance': 1000.0,
        'up_threshold': 0.02,
        'down_threshold': 0.02,
        'max_lookforward_minutes': 60,
        'order_manager': {
            'live_trading': False,
            'paper_usdt_balance': 1000.0
        }
    }
    
    try:
        # 创建PortfolioManager
        portfolio_mgr = PortfolioManager(
            config=config,
            logger=logger,
            display_name="ETH/USDT",
            local_tz=pytz.timezone('Asia/Shanghai'),
            enable_sound=False,
            db_manager=db_manager
        )
        
        logger.info("✅ PortfolioManager创建成功")
        
        # 模拟交易信号
        test_signal = {
            'guess': 1,  # 看涨
            'probability': 0.85,
            'price': 3000.0,
            'timestamp': datetime.now(timezone.utc),
            'signal_id': 'test_signal_002'
        }
        
        logger.info("📊 创建测试信号...")
        portfolio_mgr.on_new_signal(test_signal)
        
        # 检查仓位状态
        active_positions = portfolio_mgr.active_positions
        if not active_positions:
            logger.error("❌ 没有创建任何仓位")
            return False
            
        pos_id = list(active_positions.keys())[0]
        pos = active_positions[pos_id]
        logger.info(f"📈 仓位 {pos_id} 状态: {pos['status']}")
        
        # 模拟价格达到目标，触发平仓
        logger.info("🔄 模拟价格达到上涨目标，触发平仓...")
        portfolio_mgr.on_price_update(3060.0)  # 2%上涨
        
        # 检查是否进入closing状态
        pos = active_positions.get(pos_id)
        if pos and pos['status'] == 'closing':
            logger.info(f"📈 仓位进入closing状态，重试次数: {pos.get('exit_attempts', 0)}")
            
            # 快速连续调用价格更新，测试防重复
            logger.info("🔄 快速连续调用价格更新，测试防重复...")
            for i in range(10):
                portfolio_mgr.on_price_update(3060.0 + i * 0.1)
                
                # 检查重试次数是否增加
                pos = active_positions.get(pos_id)
                if pos:
                    logger.info(f"   第{i+1}次调用 - 状态: {pos['status']}, 重试次数: {pos.get('exit_attempts', 0)}")
                else:
                    logger.info(f"   第{i+1}次调用 - 仓位已完成")
                    break
        else:
            logger.info("✅ 仓位已完成或状态异常")
        
        # 打印最终状态摘要
        portfolio_mgr.print_status()
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("平仓重试保护和防重复机制测试")
    print("=" * 60)
    
    # 测试1: 重试保护机制
    print("\n1. 测试重试保护机制...")
    if not test_retry_protection():
        print("❌ 重试保护机制测试失败")
        return
    
    # 测试2: 防重复机制
    print("\n2. 测试防重复机制...")
    if not test_duplicate_prevention():
        print("❌ 防重复机制测试失败")
        return
    
    print("\n" + "=" * 60)
    print("✅ 所有测试通过！重试保护和防重复机制就绪")
    print("=" * 60)
    
    print("\n📋 保护机制摘要:")
    print("   - 最大重试次数: 3次")
    print("   - 重试间隔: 30秒")
    print("   - 重试计数跟踪")
    print("   - 防重复平仓订单")
    print("   - 自动失败标记")

if __name__ == "__main__":
    main() 