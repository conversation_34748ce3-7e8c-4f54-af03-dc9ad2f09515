#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高性能TradingView风格回测可视化工具
专门优化大数据量的渲染性能，使用数据分页和动态加载
"""

import pandas as pd
import numpy as np
import sqlite3
import argparse
import os
from datetime import datetime, timedelta
import pytz
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import json

# 引入现有的工具函数
from get_coin_history import get_table_name

# 北京时区
BEIJING_TZ = pytz.timezone('Asia/Shanghai')

def parse_time_input(time_str):
    """解析时间输入"""
    if not time_str:
        return None
    try:
        formats = ['%Y-%m-%d %H:%M:%S', '%Y-%m-%d %H:%M', '%Y-%m-%d', '%m-%d %H:%M', '%m-%d']
        dt = None
        for fmt in formats:
            try:
                dt = datetime.strptime(time_str, fmt)
                break
            except ValueError:
                continue
        if dt is None:
            raise ValueError(f"无法解析时间格式: {time_str}")
        if dt.year == 1900:
            dt = dt.replace(year=datetime.now().year)
        beijing_dt = BEIJING_TZ.localize(dt)
        utc_dt = beijing_dt.astimezone(pytz.UTC)
        return pd.Timestamp(utc_dt).tz_localize(None)
    except Exception as e:
        print(f"时间解析错误: {e}")
        return None

def load_kline_data_optimized(db_path, coin, interval, market, start_time=None, end_time=None, max_days=30):
    """优化的K线数据加载，自动限制时间范围"""
    # 将币种名称转换为完整的交易对名称
    if coin.upper() in ['BTC', 'ETH', 'DOT', 'ENA', 'LINK', 'SUI', 'UNI']:
        symbol = f"{coin.upper()}USDT"
    else:
        symbol = coin.upper()
    
    table_name = get_table_name(symbol, interval, market)
    conn = sqlite3.connect(db_path)
    
    # 如果没有指定时间范围，自动限制为最近的数据
    if not start_time and not end_time:
        # 获取最新时间戳
        latest_query = f"SELECT MAX(timestamp) FROM {table_name}"
        latest_result = conn.execute(latest_query).fetchone()
        if latest_result[0]:
            latest_timestamp = pd.Timestamp(latest_result[0], unit='s', tz='UTC').tz_localize(None)
            start_time = latest_timestamp - timedelta(days=max_days)
            print(f"⚡ 自动限制时间范围为最近 {max_days} 天以提高性能")
    
    query = f"SELECT timestamp, open, high, low, close, volume FROM {table_name}"
    conditions = []
    params = []
    
    if start_time:
        conditions.append("timestamp >= ?")
        params.append(int(start_time.timestamp()))
    if end_time:
        conditions.append("timestamp <= ?")
        params.append(int(end_time.timestamp()))
    
    if conditions:
        query += " WHERE " + " AND ".join(conditions)
    query += " ORDER BY timestamp ASC"
    
    df = pd.read_sql_query(query, conn, params=params)
    conn.close()
    
    if df.empty:
        print("❌ 数据库中无符合条件的K线数据")
        return None
    
    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s', utc=True)
    df.set_index('timestamp', inplace=True)
    df = df.astype(float)
    
    print(f"✅ 加载了 {len(df)} 条K线数据")
    return df

def adaptive_sampling(df, target_points=2000):
    """自适应采样算法"""
    if len(df) <= target_points:
        return df
    
    print(f"⚡ 使用自适应采样：{len(df)} → {target_points} 数据点")
    
    # 计算采样步长
    step = len(df) // target_points
    
    # 保留关键点：开始、结束、极值点
    indices = set()
    
    # 1. 均匀采样
    indices.update(range(0, len(df), step))
    
    # 2. 保留价格极值点
    high_extremes = df['high'].nlargest(target_points // 10).index
    low_extremes = df['low'].nsmallest(target_points // 10).index
    indices.update(df.index.get_indexer(high_extremes))
    indices.update(df.index.get_indexer(low_extremes))
    
    # 3. 保留成交量极值点
    volume_extremes = df['volume'].nlargest(target_points // 20).index
    indices.update(df.index.get_indexer(volume_extremes))
    
    # 4. 确保包含首尾
    indices.add(0)
    indices.add(len(df) - 1)
    
    # 转换为排序的列表
    indices = sorted(list(indices))
    
    # 限制最终数量
    if len(indices) > target_points:
        step = len(indices) // target_points
        indices = indices[::step]
    
    sampled_df = df.iloc[indices]
    print(f"✅ 自适应采样完成：保留了 {len(sampled_df)} 个关键数据点")
    return sampled_df

def load_backtest_log_optimized(log_file, max_signals=500):
    """优化的回测日志加载"""
    if not os.path.exists(log_file):
        print(f"❌ 回测日志文件不存在: {log_file}")
        return None
    
    df = pd.read_csv(log_file)
    
    # 解析时间戳
    df['StartTime'] = pd.to_datetime(df['StartTimestamp'].str.replace(' UTC+8', '', regex=False))
    df['EndTime'] = pd.to_datetime(df['EndTimestamp'].str.replace(' UTC+8', '', regex=False))
    
    # 转换为UTC时间
    df['StartTime'] = df['StartTime'].dt.tz_localize('Asia/Shanghai').dt.tz_convert('UTC').dt.tz_localize(None)
    df['EndTime'] = df['EndTime'].dt.tz_localize('Asia/Shanghai').dt.tz_convert('UTC').dt.tz_localize(None)
    
    # 如果信号太多，进行智能过滤
    if len(df) > max_signals:
        print(f"⚡ 交易信号较多 ({len(df)} 个)，智能筛选重要信号...")
        
        # 优先级：成功交易 > 大盈亏交易 > 最近交易
        successful = df[df['Result'] == 1]
        high_profit = df[abs(df['ProfitLoss']) > df['ProfitLoss'].std()]
        recent = df.tail(max_signals // 3)
        
        # 合并并去重
        important_df = pd.concat([successful, high_profit, recent]).drop_duplicates()
        
        if len(important_df) > max_signals:
            # 如果还是太多，按盈亏排序取前N个
            important_df = important_df.nlargest(max_signals, 'ProfitLoss')
        
        df = important_df.sort_values('StartTime')
        print(f"✅ 筛选后保留 {len(df)} 个重要交易信号")
    
    print(f"✅ 加载了 {len(df)} 条回测记录")
    return df

def create_fast_chart(kline_df, backtest_df, coin, interval, output_file=None):
    """创建高性能的简化图表"""
    
    # 数据采样
    if len(kline_df) > 2000:
        kline_df = adaptive_sampling(kline_df, 2000)
    
    # 创建简化的双子图布局
    fig = make_subplots(
        rows=2, cols=1,
        shared_xaxes=True,
        vertical_spacing=0.05,
        subplot_titles=(f'{coin.upper()} {interval} K线图 (高性能模式)', '资金曲线'),
        row_heights=[0.7, 0.3]
    )
    
    # 添加K线图（简化版）
    fig.add_trace(
        go.Candlestick(
            x=kline_df.index,
            open=kline_df['open'],
            high=kline_df['high'],
            low=kline_df['low'],
            close=kline_df['close'],
            name='K线',
            increasing_line_color='#26a69a',
            decreasing_line_color='#ef5350',
            showlegend=False
        ),
        row=1, col=1
    )
    
    if backtest_df is not None and not backtest_df.empty:
        # 计算价格范围用于信号位置调整
        price_range = kline_df['high'].max() - kline_df['low'].min()
        offset = price_range * 0.02  # 2%的偏移量

        # 只显示成功的交易信号
        successful_trades = backtest_df[backtest_df['Result'] == 1]

        if not successful_trades.empty:
            # 买入信号 - 显示在K线下方
            buy_signals = successful_trades[successful_trades['Prediction'] == 1]
            if not buy_signals.empty:
                # 计算买入信号的位置
                buy_y_positions = []
                for _, signal in buy_signals.iterrows():
                    signal_time = pd.Timestamp(signal['StartTime']).tz_localize(None) if pd.Timestamp(signal['StartTime']).tz else signal['StartTime']
                    kline_index = kline_df.index.tz_localize(None) if kline_df.index.tz else kline_df.index
                    time_diff = abs(kline_index - signal_time)
                    closest_idx = time_diff.argmin()
                    closest_time = kline_index[closest_idx]
                    low_price = kline_df.loc[closest_time, 'low'] - offset
                    buy_y_positions.append(low_price)

                fig.add_trace(
                    go.Scatter(
                        x=buy_signals['StartTime'],
                        y=buy_y_positions,
                        mode='markers',
                        marker=dict(symbol='triangle-up', size=10, color='#4CAF50', line=dict(width=1, color='white')),
                        name='成功买入',
                        text=[f"买入信号<br>收益: ${pl:+.2f}" for pl in buy_signals['ProfitLoss']],
                        hovertemplate='%{text}<extra></extra>'
                    ),
                    row=1, col=1
                )

            # 卖出信号 - 显示在K线上方
            sell_signals = successful_trades[successful_trades['Prediction'] == 0]
            if not sell_signals.empty:
                # 计算卖出信号的位置
                sell_y_positions = []
                for _, signal in sell_signals.iterrows():
                    signal_time = pd.Timestamp(signal['StartTime']).tz_localize(None) if pd.Timestamp(signal['StartTime']).tz else signal['StartTime']
                    kline_index = kline_df.index.tz_localize(None) if kline_df.index.tz else kline_df.index
                    time_diff = abs(kline_index - signal_time)
                    closest_idx = time_diff.argmin()
                    closest_time = kline_index[closest_idx]
                    high_price = kline_df.loc[closest_time, 'high'] + offset
                    sell_y_positions.append(high_price)

                fig.add_trace(
                    go.Scatter(
                        x=sell_signals['StartTime'],
                        y=sell_y_positions,
                        mode='markers',
                        marker=dict(symbol='triangle-down', size=10, color='#F44336', line=dict(width=1, color='white')),
                        name='成功卖出',
                        text=[f"卖出信号<br>收益: ${pl:+.2f}" for pl in sell_signals['ProfitLoss']],
                        hovertemplate='%{text}<extra></extra>'
                    ),
                    row=1, col=1
                )
        
        # 简化的资金曲线
        if 'CapitalAfter' in backtest_df.columns:
            # 进一步采样资金曲线
            capital_data = backtest_df[['EndTime', 'CapitalAfter']].copy()
            if len(capital_data) > 200:
                step = len(capital_data) // 200
                capital_data = capital_data.iloc[::step]
            
            fig.add_trace(
                go.Scatter(
                    x=capital_data['EndTime'],
                    y=capital_data['CapitalAfter'],
                    mode='lines',
                    line=dict(color='#2196F3', width=2),
                    name='账户资金',
                    showlegend=False
                ),
                row=2, col=1
            )
    
    # 优化的布局设置
    fig.update_layout(
        title=dict(
            text=f'{coin.upper()} {interval} 高性能回测可视化',
            x=0.5,
            font=dict(size=16)
        ),
        template='plotly_dark',
        height=700,  # 降低高度
        showlegend=True,
        legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1),
        margin=dict(l=50, r=50, t=60, b=50)
    )
    
    # 设置轴标签
    fig.update_xaxes(showticklabels=False, row=1, col=1)
    fig.update_yaxes(title_text="价格", row=1, col=1)
    fig.update_yaxes(title_text="资金", row=2, col=1)
    fig.update_xaxes(title_text="时间", row=2, col=1)
    
    # 添加简化的时间选择器
    fig.update_layout(
        xaxis2=dict(
            rangeselector=dict(
                buttons=[
                    dict(count=1, label="1天", step="day", stepmode="backward"),
                    dict(count=7, label="7天", step="day", stepmode="backward"),
                    dict(step="all", label="全部")
                ]
            ),
            rangeslider=dict(visible=False),
            type="date"
        )
    )
    
    # 保存图表
    if output_file:
        fig.write_html(output_file)
        print(f"📊 高性能图表已保存到: {output_file}")
    
    return fig

def main():
    parser = argparse.ArgumentParser(description="高性能TradingView风格回测可视化工具")
    parser.add_argument("--coin", default="ETH", help="交易对，例如 BTC")
    parser.add_argument("--interval", default="15m", help="K线间隔，例如 15m")
    parser.add_argument("--market", default="spot", choices=["spot", "futures"], help="市场类型")
    parser.add_argument("--db", default="coin_data.db", help="SQLite数据库路径")
    parser.add_argument("--log", default="backtest_money_log_quick.csv", help="回测日志文件路径")
    parser.add_argument("--start-time", help="开始时间(北京时间, YYYY-MM-DD HH:MM)")
    parser.add_argument("--end-time", help="结束时间(北京时间, YYYY-MM-DD HH:MM)")
    parser.add_argument("--output", help="输出HTML文件路径")
    parser.add_argument("--show", action='store_true', help="显示图表")
    parser.add_argument("--max-days", type=int, default=30, help="最大数据天数 (默认30天)")
    parser.add_argument("--max-signals", type=int, default=500, help="最大信号数量 (默认500)")
    
    args = parser.parse_args()
    
    # 解析时间
    start_time = parse_time_input(args.start_time) if args.start_time else None
    end_time = parse_time_input(args.end_time) if args.end_time else None
    
    # 加载K线数据（优化版）
    print("正在加载K线数据（高性能模式）...")
    kline_df = load_kline_data_optimized(
        args.db, args.coin, args.interval, args.market, 
        start_time, end_time, args.max_days
    )
    
    if kline_df is None:
        return
    
    # 加载回测日志（优化版）
    print("正在加载回测日志（高性能模式）...")
    backtest_df = load_backtest_log_optimized(args.log, args.max_signals)
    
    # 生成输出文件名
    if not args.output:
        output_file = f"tradingview_fast_{args.coin}_{args.interval}.html"
    else:
        output_file = args.output
    
    # 创建高性能图表
    print("正在生成高性能图表...")
    fig = create_fast_chart(kline_df, backtest_df, args.coin, args.interval, output_file)
    
    # 显示图表
    if args.show:
        fig.show()
    
    print("✅ 高性能可视化完成！")
    print(f"💡 提示: 在浏览器中打开 {output_file} 查看图表")

if __name__ == '__main__':
    main()
