#!/usr/bin/env python3
"""
测试反向平仓功能的简单脚本
"""

import subprocess
import sys
import os

def test_reverse_close_mode():
    """测试反向平仓模式"""
    print("=== 测试反向平仓功能 ===")
    
    # 检查必要文件是否存在
    model_file = "models/eth_15m_model.joblib"
    config_file = "models/eth_15m_config.json"
    db_file = "coin_data.db"
    
    missing_files = []
    for file_path in [model_file, config_file, db_file]:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {missing_files}")
        return False
    
    print("✅ 所有必要文件都存在")
    
    # 测试命令参数
    base_cmd = [
        sys.executable, "backtest_money_quick.py",
        "--coin", "ETH",
        "--interval", "15m", 
        "--market", "spot",
        "--db", db_file,
        "--initial-capital", "10000",
        "--risk-per-trade", "1.0",
        "--max-active-predictions", "5",
        "--quick",
        "--start-time", "2024-01-01",
        "--end-time", "2024-01-07"
    ]
    
    print("\n1. 测试不启用反向平仓模式...")
    try:
        result1 = subprocess.run(base_cmd, capture_output=True, text=True, timeout=60)
        if result1.returncode == 0:
            print("✅ 正常模式测试成功")
            # 检查输出中是否没有反向平仓信息
            if "反向平仓模式: 启用" not in result1.stdout:
                print("✅ 确认未启用反向平仓模式")
            else:
                print("❌ 意外启用了反向平仓模式")
        else:
            print(f"❌ 正常模式测试失败: {result1.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("❌ 正常模式测试超时")
        return False
    except Exception as e:
        print(f"❌ 正常模式测试异常: {e}")
        return False
    
    print("\n2. 测试启用反向平仓模式...")
    reverse_cmd = base_cmd + ["--reverse-close"]
    try:
        result2 = subprocess.run(reverse_cmd, capture_output=True, text=True, timeout=60)
        if result2.returncode == 0:
            print("✅ 反向平仓模式测试成功")
            # 检查输出中是否包含反向平仓信息
            if "反向平仓模式: 启用" in result2.stdout:
                print("✅ 确认启用了反向平仓模式")
            else:
                print("❌ 未检测到反向平仓模式启用信息")
            
            # 检查是否有反向平仓统计
            if "反向平仓:" in result2.stdout:
                print("✅ 检测到反向平仓统计信息")
            else:
                print("ℹ️  本次测试中没有触发反向平仓")
                
        else:
            print(f"❌ 反向平仓模式测试失败: {result2.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("❌ 反向平仓模式测试超时")
        return False
    except Exception as e:
        print(f"❌ 反向平仓模式测试异常: {e}")
        return False
    
    print("\n=== 测试完成 ===")
    print("✅ 反向平仓功能已成功集成到回测系统中")
    print("\n使用方法:")
    print("  python backtest_money_quick.py --reverse-close [其他参数...]")
    print("\n功能说明:")
    print("  - 当启用 --reverse-close 时，系统会监控每个时刻的新信号")
    print("  - 如果当前活跃仓位是看涨(1)，而新信号是看跌(0)，则自动平仓")
    print("  - 如果当前活跃仓位是看跌(0)，而新信号是看涨(1)，则自动平仓")
    print("  - 反向平仓的结果代码是 -3，在统计中单独计算")
    
    return True

if __name__ == "__main__":
    success = test_reverse_close_mode()
    sys.exit(0 if success else 1)
