import pandas as pd

def generate_pine_script_from_predictions(csv_file_path):
    """
    从指定的预测CSV文件中读取数据，并生成TradingView Pine脚本。
    """
    try:
        # 读取CSV文件，并告知pandas 'StartTimestamp'是日期列，同时处理时区
        df = pd.read_csv(csv_file_path)
        # 将字符串时间转换为带时区的datetime对象
        df['StartTimestamp'] = pd.to_datetime(df['StartTimestamp'])
    except Exception as e:
        print(f"读取或解析CSV文件时出错: {e}")
        return None

    # 将datetime对象转换为UNIX毫秒时间戳
    timestamps = (df['StartTimestamp'].astype('int64') // 10**6).tolist()
    prices = df['StartPrice'].tolist()
    predictions = df['Prediction'].tolist()
    up_targets = df['UpTarget'].tolist()
    down_targets = df['DownTarget'].tolist()

    # 将Prediction（0或1）转换为更易读的信号类型 ("sell" 或 "buy")
    signal_types = ['buy' if p == 1 else 'sell' for p in predictions]

    # --- 开始生成 Pine Script 字符串 ---
    
    # 将Python列表转换为Pine Script数组格式的字符串
    timestamps_str = ', '.join(map(str, timestamps))
    prices_str = ', '.join(map(str, prices))
    # 字符串数组需要特殊处理，每个元素都要加上双引号
    signal_types_str = '"' + '", "'.join(signal_types) + '"'
    up_targets_str = ', '.join(map(str, up_targets))
    down_targets_str = ', '.join(map(str, down_targets))

    pine_script = f"""//@version=5
indicator("我的预测信号", overlay=true, max_labels_count=500)

// --- 由 Python 自动生成的数据 ---
var signal_timestamps = array.from({timestamps_str})
var signal_prices = array.from({prices_str})
var signal_types = array.from({signal_types_str})
var signal_up_targets = array.from({up_targets_str})
var signal_down_targets = array.from({down_targets_str})

// --- 绘图逻辑 ---
// 仅在图表的最后一个K线上执行循环，这是最高效的绘图方式
if (barstate.islast)
    for i = 0 to array.size(signal_timestamps) - 1
        // 从数组中获取当前信号的数据
        t = array.get(signal_timestamps, i)
        p = array.get(signal_prices, i)
        type = array.get(signal_types, i)
        up_target = array.get(signal_up_targets, i)
        down_target = array.get(signal_down_targets, i)
        
        // 根据信号类型 ("buy" 或 "sell") 设置不同的标签
        if (type == "buy")
            // 创建一个买入信号的标签
            label_text = "预测涨\\n价格: " + str.tostring(p) + "\\n目标: " + str.tostring(up_target)
            label.new(
                 x=t, 
                 y=p, 
                 text=label_text, 
                 color=color.new(color.green, 20), 
                 textcolor=color.white, 
                 style=label.style_label_up,
                 tooltip="买入信号\\n时间: " + str.format_time(t, "yyyy-MM-dd HH:mm")
                 )
        if (type == "sell")
            // 创建一个卖出信号的标签
            label_text = "预测跌\\n价格: " + str.tostring(p) + "\\n目标: " + str.tostring(down_target)
            label.new(
                 x=t, 
                 y=p, 
                 text=label_text, 
                 color=color.new(color.red, 20), 
                 textcolor=color.white, 
                 style=label.style_label_down,
                 tooltip="卖出信号\\n时间: " + str.format_time(t, "yyyy-MM-dd HH:mm")
                 )
"""
    return pine_script

# --- 主程序 ---
if __name__ == "__main__":
    # <<<<<<<<<<<<<<<< 在这里修改您的CSV文件名 <<<<<<<<<<<<<<<<
    csv_filename = "backtest_money_log.csv"
    
    # 调用函数生成代码
    pine_code = generate_pine_script_from_predictions(csv_filename)
    
    if pine_code:
        # 将生成的代码写入一个文本文件
        output_filename = "generated_pine_script.txt"
        with open(output_filename, "w", encoding="utf-8") as f:
            f.write(pine_code)
        print(f"Pine 脚本已成功生成到 '{output_filename}' 文件中。")
        print("请将该文件的内容完整复制到 TradingView 的 Pine 编辑器中。")