//@version=5
indicator("我的预测信号", overlay=true, max_labels_count=500)

// --- 由 Python 自动生成的数据 ---
var signal_timestamps = array.from(1736689500000, 1736930700000, 1737018000000, 1737251100000, 1737342000000, 1737369900000, 1737396900000, 1737532800000, 1737708300000, 1737823500000, 1738007100000, 1738168200000, 1738510200000, 1738616400000, 1738626300000, 1738771200000, 1739099700000, 1739241900000, 1739358900000, 1739446200000, 1739833200000, 1739861100000, 1739952900000, 1740238200000, 1740436200000, 1740519900000, 1740528000000, 1740640500000, 1740786300000, 1740927600000, 1741041900000, 1741112100000, 1741190400000, 1741431600000, 1741521600000, 1741590900000, 1741697100000, 1741824000000, 1742229000000, 1742400000000, 1742453100000, 1742847300000, 1743072300000, 1743195600000, 1743436800000, 1743576300000, 1743663600000, 1744067700000, 1744093800000, 1744180200000, 1744211700000, 1744329600000, 1744537500000, 1744673400000, 1744784100000, 1744875000000, 1745251200000, 1745349300000, 1745424000000, 1745542800000, 1745888400000, 1746080100000, 1746512100000, 1746633600000, 1746776700000, 1746831600000, 1746941400000, 1747001700000, 1747088100000, 1747243800000, 1747289700000, 1747512000000, 1747720800000, 1747826100000, 1748073600000, 1748275200000, 1748563200000, 1748880000000, 1749020400000, 1749225600000, 1749521700000, 1749657600000, 1749846600000, 1750089600000, 1750279500000, 1750510800000, 1750611600000, 1750659300000, 1750743000000, 1750815000000, 1750922100000, 1751299200000, 1751529600000, 1751915700000, 1752124500000, 1752517800000, 1752681600000, 1752757200000, 1753079400000, 1753165800000, 1753260300000, 1753365600000, 1753700400000, 1753959600000, 1754323200000, 1754397000000, 1754566200000, 1754637300000)
var signal_prices = array.from(3307.1, 3191.13, 3347.95, 3270.97, 3173.22, 3432.73, 3268.2, 3340.08, 3254.72, 3273.97, 3183.6, 3106.93, 3131.41, 2465.33, 2622.35, 2724.03, 2646.44, 2647.47, 2596.49, 2672.38, 2691.01, 2826.6, 2660.07, 2663.11, 2727.6, 2506.05, 2388.79, 2431.72, 2106.2, 2200.39, 2372.68, 2055.55, 2171.89, 2189.59, 2222.99, 2114.39, 1869.18, 1925.77, 1898.9, 1934.0, 2030.87, 2016.66, 1994.68, 1925.8, 1808.58, 1882.53, 1882.01, 1451.38, 1563.82, 1547.22, 1470.3, 1596.37, 1633.82, 1627.81, 1612.56, 1593.37, 1593.12, 1577.46, 1754.81, 1741.96, 1807.74, 1764.46, 1803.69, 1819.89, 2051.3, 2294.36, 2422.55, 2543.05, 2508.08, 2718.0, 2569.21, 2492.29, 2428.82, 2480.59, 2560.31, 2547.91, 2724.63, 2536.19, 2637.83, 2406.9, 2532.2, 2821.39, 2516.53, 2543.49, 2536.97, 2420.4, 2293.27, 2170.34, 2289.08, 2416.92, 2412.99, 2505.85, 2534.97, 2586.48, 2662.99, 3005.5, 3119.62, 3381.04, 3744.17, 3847.73, 3716.98, 3602.34, 3848.22, 3716.47, 3499.93, 3702.8, 3673.0, 3828.96)
var signal_types = array.from("buy", "buy", "buy", "sell", "buy", "sell", "buy", "buy", "buy", "sell", "sell", "buy", "sell", "buy", "buy", "buy", "buy", "buy", "sell", "buy", "buy", "sell", "buy", "sell", "sell", "sell", "buy", "buy", "buy", "buy", "sell", "buy", "buy", "buy", "buy", "sell", "buy", "buy", "buy", "buy", "buy", "buy", "sell", "buy", "buy", "buy", "buy", "buy", "buy", "buy", "buy", "sell", "buy", "buy", "buy", "buy", "buy", "buy", "buy", "sell", "buy", "buy", "buy", "buy", "buy", "sell", "buy", "buy", "buy", "buy", "buy", "sell", "buy", "buy", "sell", "buy", "buy", "buy", "buy", "buy", "buy", "buy", "buy", "buy", "buy", "sell", "buy", "buy", "buy", "buy", "buy", "buy", "buy", "buy", "buy", "buy", "buy", "buy", "buy", "buy", "buy", "buy", "buy", "buy", "buy", "sell", "buy", "buy")
var signal_up_targets = array.from(3472.455, 3350.6865, 3515.3475, 3434.5185, 3331.881, 3604.3665, 3431.61, 3507.084, 3417.456, 3437.6685, 3342.78, 3262.2765, 3287.9805, 2588.5965, 2753.4675, 2860.2315, 2778.762, 2779.8435, 2726.3145, 2805.999, 2825.5605, 2967.93, 2793.0735, 2796.2655, 2863.98, 2631.3525, 2508.2295, 2553.306, 2211.51, 2310.4095, 2491.314, 2158.3275, 2280.4845, 2299.0695, 2334.1395, 2220.1095, 1962.639, 2022.0585, 1993.845, 2030.7, 2132.4135, 2117.493, 2094.414, 2022.09, 1899.009, 1976.6565, 1976.1105, 1523.949, 1642.011, 1624.581, 1543.815, 1676.1885, 1715.511, 1709.2005, 1693.188, 1673.0385, 1672.776, 1656.333, 1842.5505, 1829.058, 1898.127, 1852.683, 1893.8745, 1910.8845, 2153.865, 2409.078, 2543.6775, 2670.2025, 2633.484, 2853.9, 2697.6705, 2616.9045, 2550.261, 2604.6195, 2688.3255, 2675.3055, 2860.8615, 2662.9995, 2769.7215, 2527.245, 2658.81, 2962.4595, 2642.3565, 2670.6645, 2663.8185, 2541.42, 2407.9335, 2278.857, 2403.534, 2537.766, 2533.6395, 2631.1425, 2661.7185, 2715.804, 2796.1395, 3155.775, 3275.601, 3550.092, 3931.3785, 4040.1165, 3902.829, 3782.457, 4040.631, 3902.2935, 3674.9265, 3887.94, 3856.65, 4020.408)
var signal_down_targets = array.from(3141.745, 3031.5735, 3180.5525, 3107.4215, 3014.559, 3261.0935, 3104.79, 3173.076, 3091.984, 3110.2715, 3024.42, 2951.5835, 2974.8395, 2342.0635, 2491.2325, 2587.8285, 2514.118, 2515.0965, 2466.6655, 2538.761, 2556.4595, 2685.27, 2527.0665, 2529.9545, 2591.22, 2380.7475, 2269.3505, 2310.134, 2000.89, 2090.3705, 2254.046, 1952.7725, 2063.2955, 2080.1105, 2111.8405, 2008.6705, 1775.721, 1829.4815, 1803.955, 1837.3, 1929.3265, 1915.827, 1894.946, 1829.51, 1718.151, 1788.4035, 1787.9095, 1378.811, 1485.629, 1469.859, 1396.785, 1516.5515, 1552.129, 1546.4195, 1531.932, 1513.7015, 1513.464, 1498.587, 1667.0695, 1654.862, 1717.353, 1676.237, 1713.5055, 1728.8955, 1948.735, 2179.642, 2301.4225, 2415.8975, 2382.676, 2582.1, 2440.7495, 2367.6755, 2307.379, 2356.5605, 2432.2945, 2420.5145, 2588.3985, 2409.3805, 2505.9385, 2286.555, 2405.59, 2680.3205, 2390.7035, 2416.3155, 2410.1215, 2299.38, 2178.6065, 2061.823, 2174.626, 2296.074, 2292.3405, 2380.5575, 2408.2215, 2457.156, 2529.8405, 2855.225, 2963.639, 3211.988, 3556.9615, 3655.3435, 3531.131, 3422.223, 3655.809, 3530.6465, 3324.9335, 3517.66, 3489.35, 3637.512)

// --- 绘图逻辑 ---
// 仅在图表的最后一个K线上执行循环，这是最高效的绘图方式
if (barstate.islast)
    for i = 0 to array.size(signal_timestamps) - 1
        // 从数组中获取当前信号的数据
        t = array.get(signal_timestamps, i)
        p = array.get(signal_prices, i)
        type = array.get(signal_types, i)
        up_target = array.get(signal_up_targets, i)
        down_target = array.get(signal_down_targets, i)
        
        // 根据信号类型 ("buy" 或 "sell") 设置不同的标签
        if (type == "buy")
            // 创建一个买入信号的标签
            label_text = "预测涨\n价格: " + str.tostring(p) + "\n目标: " + str.tostring(up_target)
            label.new(
                 x=t, 
                 y=p, 
                 text=label_text, 
                 color=color.new(color.green, 20), 
                 textcolor=color.white, 
                 style=label.style_label_up,
                 tooltip="买入信号\n时间: " + str.format_time(t, "yyyy-MM-dd HH:mm")
                 )
        if (type == "sell")
            // 创建一个卖出信号的标签
            label_text = "预测跌\n价格: " + str.tostring(p) + "\n目标: " + str.tostring(down_target)
            label.new(
                 x=t, 
                 y=p, 
                 text=label_text, 
                 color=color.new(color.red, 20), 
                 textcolor=color.white, 
                 style=label.style_label_down,
                 tooltip="卖出信号\n时间: " + str.format_time(t, "yyyy-MM-dd HH:mm")
                 )
