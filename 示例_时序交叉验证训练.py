#!/usr/bin/env python3
"""
时序交叉验证训练示例

这个脚本演示如何使用新的时序交叉验证功能来训练更可靠的模型。
"""

import subprocess
import sys
import os
from datetime import datetime

def run_command(cmd, description):
    """运行命令并显示结果"""
    print(f"\n{'='*60}")
    print(f"🚀 {description}")
    print(f"{'='*60}")
    print(f"命令: {cmd}")
    print()
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        if result.stdout:
            print("输出:")
            print(result.stdout)
        
        if result.stderr:
            print("错误:")
            print(result.stderr)
        
        if result.returncode == 0:
            print("✅ 命令执行成功")
        else:
            print(f"❌ 命令执行失败，返回码: {result.returncode}")
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ 执行命令时发生异常: {e}")
        return False

def main():
    """主函数"""
    print("🧪 时序交叉验证训练示例")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查是否存在必要的文件
    required_files = ['train.py', 'model_utils.py', 'data_loader.py']
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"❌ 缺少必要文件: {missing_files}")
        return
    
    print("✅ 所有必要文件存在")
    
    # 示例1: 查看帮助信息
    success = run_command(
        "python train.py --help",
        "查看训练脚本的帮助信息"
    )
    
    if not success:
        print("❌ 无法运行训练脚本，请检查环境配置")
        return
    
    # 示例2: 使用时序交叉验证训练（小数据集快速测试）
    print("\n" + "="*60)
    print("📋 推荐的训练命令示例:")
    print("="*60)
    
    examples = [
        {
            "description": "基础时序交叉验证训练",
            "command": "python train.py --coin ETH --cv-splits 3 --start-time '2024-11-01' --end-time '2024-12-31'"
        },
        {
            "description": "完整时序交叉验证训练（保存数据）",
            "command": "python train.py --coin ETH --cv-splits 5 --save-data --start-time '2024-01-01' --end-time '2024-12-31'"
        },
        {
            "description": "快速训练（禁用交叉验证）",
            "command": "python train.py --coin ETH --no-time-series-cv --start-time '2024-12-01' --end-time '2024-12-31'"
        },
        {
            "description": "使用预处理数据训练",
            "command": "python train.py --coin ETH --load-data --cv-splits 5"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}. {example['description']}:")
        print(f"   {example['command']}")
    
    # 交互式选择
    print(f"\n{'='*60}")
    print("🎯 选择要执行的示例 (输入数字 1-4，或按 Enter 跳过):")
    
    try:
        choice = input("请选择: ").strip()
        
        if choice and choice.isdigit():
            choice_num = int(choice)
            if 1 <= choice_num <= len(examples):
                selected_example = examples[choice_num - 1]
                
                print(f"\n您选择了: {selected_example['description']}")
                confirm = input("确认执行吗? (y/N): ").strip().lower()
                
                if confirm in ['y', 'yes']:
                    success = run_command(
                        selected_example['command'],
                        f"执行示例 {choice_num}: {selected_example['description']}"
                    )
                    
                    if success:
                        print("\n🎉 训练完成！")
                        print("📁 检查以下输出文件:")
                        print("   - ETH_5min_model.joblib (模型文件)")
                        print("   - ETH_5min_config.json (配置文件)")
                        print("   - cv_results_ETH_5min.json (交叉验证结果)")
                        print("   - test_results_ETH_5min.csv (测试结果)")
                        print("   - feature_importance_ETH_5min.csv (特征重要性)")
                    else:
                        print("\n❌ 训练失败，请检查错误信息")
                else:
                    print("取消执行")
            else:
                print("无效选择")
        else:
            print("跳过执行示例")
    
    except KeyboardInterrupt:
        print("\n\n用户中断")
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
    
    # 显示重要提示
    print(f"\n{'='*60}")
    print("📚 重要提示:")
    print("="*60)
    print("1. 时序交叉验证会显著增加训练时间，但能提高模型可靠性")
    print("2. 建议先用小数据集测试，确认无误后再用完整数据")
    print("3. 查看 '时序交叉验证使用指南.md' 了解详细用法")
    print("4. 交叉验证结果保存在 cv_results_*.json 文件中")
    print("5. 如果计算资源有限，可以减少 --cv-splits 数量")
    
    print(f"\n🏁 示例演示完成 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
